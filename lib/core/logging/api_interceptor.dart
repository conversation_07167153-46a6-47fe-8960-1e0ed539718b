import 'dart:convert';
import 'package:http/http.dart' as http;
import 'api_logger.dart';

/// HTTP client wrapper with beautiful API logging
class ApiInterceptor {
  final http.Client _client;
  final String? baseUrl;
  final Map<String, String> _defaultHeaders;
  
  ApiInterceptor({
    http.Client? client,
    this.baseUrl,
    Map<String, String>? defaultHeaders,
  }) : _client = client ?? http.Client(),
       _defaultHeaders = defaultHeaders ?? {};

  /// GET request with logging
  Future<http.Response> get(
    String path, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) async {
    final uri = _buildUri(path, queryParams);
    final requestHeaders = {..._defaultHeaders, ...?headers};
    final stopwatch = Stopwatch()..start();
    
    // Log request
    ApiLogger.logRequest(
      method: 'GET',
      url: uri.toString(),
      headers: requestHeaders,
      queryParams: queryParams,
      tag: tag,
    );
    
    try {
      final response = await _client.get(uri, headers: requestHeaders);
      stopwatch.stop();
      
      // Log response
      ApiLogger.logResponse(
        method: 'GET',
        url: uri.toString(),
        statusCode: response.statusCode,
        headers: response.headers,
        body: _parseResponseBody(response),
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log error
      ApiLogger.logError(
        method: 'GET',
        url: uri.toString(),
        error: error.toString(),
        stackTrace: stackTrace,
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      rethrow;
    }
  }

  /// POST request with logging
  Future<http.Response> post(
    String path, {
    Map<String, String>? headers,
    dynamic body,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) async {
    final uri = _buildUri(path, queryParams);
    final requestHeaders = {..._defaultHeaders, ...?headers};
    final stopwatch = Stopwatch()..start();
    
    // Prepare body
    String? requestBody;
    if (body != null) {
      if (body is String) {
        requestBody = body;
      } else {
        requestBody = jsonEncode(body);
        requestHeaders['Content-Type'] = 'application/json';
      }
    }
    
    // Log request
    ApiLogger.logRequest(
      method: 'POST',
      url: uri.toString(),
      headers: requestHeaders,
      body: body,
      queryParams: queryParams,
      tag: tag,
    );
    
    try {
      final response = await _client.post(
        uri,
        headers: requestHeaders,
        body: requestBody,
      );
      stopwatch.stop();
      
      // Log response
      ApiLogger.logResponse(
        method: 'POST',
        url: uri.toString(),
        statusCode: response.statusCode,
        headers: response.headers,
        body: _parseResponseBody(response),
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log error
      ApiLogger.logError(
        method: 'POST',
        url: uri.toString(),
        error: error.toString(),
        stackTrace: stackTrace,
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      rethrow;
    }
  }

  /// PUT request with logging
  Future<http.Response> put(
    String path, {
    Map<String, String>? headers,
    dynamic body,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) async {
    final uri = _buildUri(path, queryParams);
    final requestHeaders = {..._defaultHeaders, ...?headers};
    final stopwatch = Stopwatch()..start();
    
    // Prepare body
    String? requestBody;
    if (body != null) {
      if (body is String) {
        requestBody = body;
      } else {
        requestBody = jsonEncode(body);
        requestHeaders['Content-Type'] = 'application/json';
      }
    }
    
    // Log request
    ApiLogger.logRequest(
      method: 'PUT',
      url: uri.toString(),
      headers: requestHeaders,
      body: body,
      queryParams: queryParams,
      tag: tag,
    );
    
    try {
      final response = await _client.put(
        uri,
        headers: requestHeaders,
        body: requestBody,
      );
      stopwatch.stop();
      
      // Log response
      ApiLogger.logResponse(
        method: 'PUT',
        url: uri.toString(),
        statusCode: response.statusCode,
        headers: response.headers,
        body: _parseResponseBody(response),
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log error
      ApiLogger.logError(
        method: 'PUT',
        url: uri.toString(),
        error: error.toString(),
        stackTrace: stackTrace,
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      rethrow;
    }
  }

  /// DELETE request with logging
  Future<http.Response> delete(
    String path, {
    Map<String, String>? headers,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) async {
    final uri = _buildUri(path, queryParams);
    final requestHeaders = {..._defaultHeaders, ...?headers};
    final stopwatch = Stopwatch()..start();
    
    // Log request
    ApiLogger.logRequest(
      method: 'DELETE',
      url: uri.toString(),
      headers: requestHeaders,
      queryParams: queryParams,
      tag: tag,
    );
    
    try {
      final response = await _client.delete(uri, headers: requestHeaders);
      stopwatch.stop();
      
      // Log response
      ApiLogger.logResponse(
        method: 'DELETE',
        url: uri.toString(),
        statusCode: response.statusCode,
        headers: response.headers,
        body: _parseResponseBody(response),
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log error
      ApiLogger.logError(
        method: 'DELETE',
        url: uri.toString(),
        error: error.toString(),
        stackTrace: stackTrace,
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      rethrow;
    }
  }

  /// PATCH request with logging
  Future<http.Response> patch(
    String path, {
    Map<String, String>? headers,
    dynamic body,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) async {
    final uri = _buildUri(path, queryParams);
    final requestHeaders = {..._defaultHeaders, ...?headers};
    final stopwatch = Stopwatch()..start();
    
    // Prepare body
    String? requestBody;
    if (body != null) {
      if (body is String) {
        requestBody = body;
      } else {
        requestBody = jsonEncode(body);
        requestHeaders['Content-Type'] = 'application/json';
      }
    }
    
    // Log request
    ApiLogger.logRequest(
      method: 'PATCH',
      url: uri.toString(),
      headers: requestHeaders,
      body: body,
      queryParams: queryParams,
      tag: tag,
    );
    
    try {
      final response = await _client.patch(
        uri,
        headers: requestHeaders,
        body: requestBody,
      );
      stopwatch.stop();
      
      // Log response
      ApiLogger.logResponse(
        method: 'PATCH',
        url: uri.toString(),
        statusCode: response.statusCode,
        headers: response.headers,
        body: _parseResponseBody(response),
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      return response;
    } catch (error, stackTrace) {
      stopwatch.stop();
      
      // Log error
      ApiLogger.logError(
        method: 'PATCH',
        url: uri.toString(),
        error: error.toString(),
        stackTrace: stackTrace,
        duration: stopwatch.elapsed,
        tag: tag,
      );
      
      rethrow;
    }
  }

  /// Build URI with base URL and query parameters
  Uri _buildUri(String path, Map<String, dynamic>? queryParams) {
    final url = baseUrl != null ? '$baseUrl$path' : path;
    final uri = Uri.parse(url);
    
    if (queryParams != null && queryParams.isNotEmpty) {
      return uri.replace(queryParameters: {
        ...uri.queryParameters,
        ...queryParams.map((key, value) => MapEntry(key, value.toString())),
      });
    }
    
    return uri;
  }

  /// Parse response body for logging
  dynamic _parseResponseBody(http.Response response) {
    if (response.body.isEmpty) return null;
    
    try {
      return jsonDecode(response.body);
    } catch (e) {
      return response.body;
    }
  }

  /// Close the HTTP client
  void close() {
    _client.close();
  }
}
