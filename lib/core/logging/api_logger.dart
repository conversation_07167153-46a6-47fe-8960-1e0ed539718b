import 'dart:convert';
import 'dart:developer' as developer;
import 'package:flutter/foundation.dart';

/// Beautiful API logger with pretty formatting and colors
class ApiLogger {
  static const String _tag = '🌐 API';
  static const bool _enableLogging = kDebugMode;
  
  // ANSI Color codes for terminal output
  static const String _reset = '\x1B[0m';
  static const String _bold = '\x1B[1m';
  static const String _red = '\x1B[31m';
  static const String _green = '\x1B[32m';
  static const String _yellow = '\x1B[33m';
  static const String _blue = '\x1B[34m';
  static const String _magenta = '\x1B[35m';
  static const String _cyan = '\x1B[36m';
  static const String _white = '\x1B[37m';
  static const String _gray = '\x1B[90m';
  
  // Box drawing characters
  static const String _topLeft = '┌';
  static const String _topRight = '┐';
  static const String _bottomLeft = '└';
  static const String _bottomRight = '┘';
  static const String _horizontal = '─';
  static const String _vertical = '│';
  static const String _cross = '┼';
  static const String _teeDown = '┬';
  static const String _teeUp = '┴';
  static const String _teeRight = '├';
  static const String _teeLeft = '┤';

  /// Log API request with beautiful formatting
  static void logRequest({
    required String method,
    required String url,
    Map<String, String>? headers,
    dynamic body,
    Map<String, dynamic>? queryParams,
    String? tag,
  }) {
    if (!_enableLogging) return;

    final timestamp = DateTime.now().toIso8601String();
    final requestTag = tag ?? 'REQUEST';
    
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('$_cyan$_bold$_topLeft${_horizontal * 80}$_topRight$_reset');
    buffer.writeln('$_cyan$_vertical$_white$_bold 🚀 API REQUEST - $requestTag ${' ' * (80 - 20 - requestTag.length)}$_cyan$_vertical$_reset');
    buffer.writeln('$_cyan$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Timestamp
    buffer.writeln('$_cyan$_vertical$_gray ⏰ $timestamp${' ' * (80 - timestamp.length - 4)}$_cyan$_vertical$_reset');
    buffer.writeln('$_cyan$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Method and URL
    final methodColor = _getMethodColor(method);
    buffer.writeln('$_cyan$_vertical$methodColor$_bold $method$_reset $url${' ' * (80 - method.length - url.length - 2)}$_cyan$_vertical$_reset');
    
    // Query Parameters
    if (queryParams != null && queryParams.isNotEmpty) {
      buffer.writeln('$_cyan$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_cyan$_vertical$_yellow$_bold 📋 QUERY PARAMETERS${' ' * (80 - 18)}$_cyan$_vertical$_reset');
      for (final entry in queryParams.entries) {
        final line = '   ${entry.key}: ${entry.value}';
        buffer.writeln('$_cyan$_vertical$_white$line${' ' * (80 - line.length)}$_cyan$_vertical$_reset');
      }
    }
    
    // Headers
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('$_cyan$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_cyan$_vertical$_magenta$_bold 📄 HEADERS${' ' * (80 - 10)}$_cyan$_vertical$_reset');
      for (final entry in headers.entries) {
        final line = '   ${entry.key}: ${entry.value}';
        final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
        buffer.writeln('$_cyan$_vertical$_white$truncatedLine${' ' * (80 - truncatedLine.length)}$_cyan$_vertical$_reset');
      }
    }
    
    // Body
    if (body != null) {
      buffer.writeln('$_cyan$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_cyan$_vertical$_blue$_bold 📦 REQUEST BODY${' ' * (80 - 15)}$_cyan$_vertical$_reset');
      final bodyString = _formatJson(body);
      final bodyLines = bodyString.split('\n');
      for (final line in bodyLines) {
        final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
        buffer.writeln('$_cyan$_vertical$_white$truncatedLine${' ' * (80 - truncatedLine.length)}$_cyan$_vertical$_reset');
      }
    }
    
    // Footer
    buffer.writeln('$_cyan$_bottomLeft${_horizontal * 80}$_bottomRight$_reset');
    
    developer.log(buffer.toString(), name: _tag);
  }

  /// Log API response with beautiful formatting
  static void logResponse({
    required String method,
    required String url,
    required int statusCode,
    Map<String, String>? headers,
    dynamic body,
    Duration? duration,
    String? tag,
  }) {
    if (!_enableLogging) return;

    final timestamp = DateTime.now().toIso8601String();
    final responseTag = tag ?? 'RESPONSE';
    
    final buffer = StringBuffer();
    
    // Header
    final statusColor = _getStatusColor(statusCode);
    buffer.writeln('$_green$_bold$_topLeft${_horizontal * 80}$_topRight$_reset');
    buffer.writeln('$_green$_vertical$_white$_bold 📥 API RESPONSE - $responseTag ${' ' * (80 - 21 - responseTag.length)}$_green$_vertical$_reset');
    buffer.writeln('$_green$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Timestamp
    buffer.writeln('$_green$_vertical$_gray ⏰ $timestamp${' ' * (80 - timestamp.length - 4)}$_green$_vertical$_reset');
    buffer.writeln('$_green$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Method, URL, and Status
    final methodColor = _getMethodColor(method);
    final statusText = '$statusCode ${_getStatusText(statusCode)}';
    buffer.writeln('$_green$_vertical$methodColor$_bold $method$_reset $url${' ' * (80 - method.length - url.length - 2)}$_green$_vertical$_reset');
    buffer.writeln('$_green$_vertical$statusColor$_bold 📊 $statusText${' ' * (80 - statusText.length - 4)}$_green$_vertical$_reset');
    
    // Duration
    if (duration != null) {
      final durationText = '⚡ ${duration.inMilliseconds}ms';
      buffer.writeln('$_green$_vertical$_yellow$durationText${' ' * (80 - durationText.length)}$_green$_vertical$_reset');
    }
    
    // Headers
    if (headers != null && headers.isNotEmpty) {
      buffer.writeln('$_green$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_green$_vertical$_magenta$_bold 📄 RESPONSE HEADERS${' ' * (80 - 18)}$_green$_vertical$_reset');
      for (final entry in headers.entries) {
        final line = '   ${entry.key}: ${entry.value}';
        final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
        buffer.writeln('$_green$_vertical$_white$truncatedLine${' ' * (80 - truncatedLine.length)}$_green$_vertical$_reset');
      }
    }
    
    // Body
    if (body != null) {
      buffer.writeln('$_green$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_green$_vertical$_blue$_bold 📦 RESPONSE BODY${' ' * (80 - 16)}$_green$_vertical$_reset');
      final bodyString = _formatJson(body);
      final bodyLines = bodyString.split('\n');
      for (final line in bodyLines.take(20)) { // Limit to 20 lines
        final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
        buffer.writeln('$_green$_vertical$_white$truncatedLine${' ' * (80 - truncatedLine.length)}$_green$_vertical$_reset');
      }
      if (bodyLines.length > 20) {
        buffer.writeln('$_green$_vertical$_gray   ... (${bodyLines.length - 20} more lines)${' ' * (80 - 25 - (bodyLines.length - 20).toString().length)}$_green$_vertical$_reset');
      }
    }
    
    // Footer
    buffer.writeln('$_green$_bottomLeft${_horizontal * 80}$_bottomRight$_reset');
    
    developer.log(buffer.toString(), name: _tag);
  }

  /// Log API error with beautiful formatting
  static void logError({
    required String method,
    required String url,
    required String error,
    StackTrace? stackTrace,
    Duration? duration,
    String? tag,
  }) {
    if (!_enableLogging) return;

    final timestamp = DateTime.now().toIso8601String();
    final errorTag = tag ?? 'ERROR';
    
    final buffer = StringBuffer();
    
    // Header
    buffer.writeln('$_red$_bold$_topLeft${_horizontal * 80}$_topRight$_reset');
    buffer.writeln('$_red$_vertical$_white$_bold ❌ API ERROR - $errorTag ${' ' * (80 - 16 - errorTag.length)}$_red$_vertical$_reset');
    buffer.writeln('$_red$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Timestamp
    buffer.writeln('$_red$_vertical$_gray ⏰ $timestamp${' ' * (80 - timestamp.length - 4)}$_red$_vertical$_reset');
    buffer.writeln('$_red$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    
    // Method and URL
    final methodColor = _getMethodColor(method);
    buffer.writeln('$_red$_vertical$methodColor$_bold $method$_reset $url${' ' * (80 - method.length - url.length - 2)}$_red$_vertical$_reset');
    
    // Duration
    if (duration != null) {
      final durationText = '⚡ ${duration.inMilliseconds}ms';
      buffer.writeln('$_red$_vertical$_yellow$durationText${' ' * (80 - durationText.length)}$_red$_vertical$_reset');
    }
    
    // Error Message
    buffer.writeln('$_red$_teeRight${_horizontal * 80}$_teeLeft$_reset');
    buffer.writeln('$_red$_vertical$_white$_bold 💥 ERROR MESSAGE${' ' * (80 - 16)}$_red$_vertical$_reset');
    final errorLines = error.split('\n');
    for (final line in errorLines) {
      final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
      buffer.writeln('$_red$_vertical$_white   $truncatedLine${' ' * (80 - truncatedLine.length - 3)}$_red$_vertical$_reset');
    }
    
    // Stack Trace (first 10 lines)
    if (stackTrace != null) {
      buffer.writeln('$_red$_teeRight${_horizontal * 80}$_teeLeft$_reset');
      buffer.writeln('$_red$_vertical$_gray$_bold 📚 STACK TRACE${' ' * (80 - 14)}$_red$_vertical$_reset');
      final stackLines = stackTrace.toString().split('\n');
      for (final line in stackLines.take(10)) {
        final truncatedLine = line.length > 77 ? '${line.substring(0, 74)}...' : line;
        buffer.writeln('$_red$_vertical$_gray   $truncatedLine${' ' * (80 - truncatedLine.length - 3)}$_red$_vertical$_reset');
      }
      if (stackLines.length > 10) {
        buffer.writeln('$_red$_vertical$_gray   ... (${stackLines.length - 10} more lines)${' ' * (80 - 25 - (stackLines.length - 10).toString().length)}$_red$_vertical$_reset');
      }
    }
    
    // Footer
    buffer.writeln('$_red$_bottomLeft${_horizontal * 80}$_bottomRight$_reset');
    
    developer.log(buffer.toString(), name: _tag);
  }

  /// Log simple message with formatting
  static void logInfo(String message, {String? tag}) {
    if (!_enableLogging) return;
    
    final infoTag = tag ?? 'INFO';
    final timestamp = DateTime.now().toIso8601String();
    
    developer.log(
      '$_blue$_bold💡 [$infoTag] $_reset$_white$message$_reset $_gray($timestamp)$_reset',
      name: _tag,
    );
  }

  /// Get color for HTTP method
  static String _getMethodColor(String method) {
    switch (method.toUpperCase()) {
      case 'GET':
        return _green;
      case 'POST':
        return _blue;
      case 'PUT':
        return _yellow;
      case 'DELETE':
        return _red;
      case 'PATCH':
        return _magenta;
      default:
        return _white;
    }
  }

  /// Get color for HTTP status code
  static String _getStatusColor(int statusCode) {
    if (statusCode >= 200 && statusCode < 300) {
      return _green;
    } else if (statusCode >= 300 && statusCode < 400) {
      return _yellow;
    } else if (statusCode >= 400 && statusCode < 500) {
      return _red;
    } else if (statusCode >= 500) {
      return _magenta;
    } else {
      return _white;
    }
  }

  /// Get status text for HTTP status code
  static String _getStatusText(int statusCode) {
    switch (statusCode) {
      case 200:
        return 'OK';
      case 201:
        return 'Created';
      case 204:
        return 'No Content';
      case 400:
        return 'Bad Request';
      case 401:
        return 'Unauthorized';
      case 403:
        return 'Forbidden';
      case 404:
        return 'Not Found';
      case 500:
        return 'Internal Server Error';
      case 502:
        return 'Bad Gateway';
      case 503:
        return 'Service Unavailable';
      default:
        return 'Unknown';
    }
  }

  /// Format JSON with proper indentation
  static String _formatJson(dynamic json) {
    try {
      if (json is String) {
        // Try to parse as JSON first
        try {
          final parsed = jsonDecode(json);
          return const JsonEncoder.withIndent('  ').convert(parsed);
        } catch (e) {
          // If not JSON, return as is
          return json;
        }
      } else {
        return const JsonEncoder.withIndent('  ').convert(json);
      }
    } catch (e) {
      return json.toString();
    }
  }
}
