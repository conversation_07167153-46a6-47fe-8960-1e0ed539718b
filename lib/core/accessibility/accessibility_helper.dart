import 'package:flutter/material.dart';
import 'package:flutter/semantics.dart';

/// Accessibility helper utilities for better app accessibility
class AccessibilityHelper {
  /// Create accessible button with proper semantics
  static Widget accessibleButton({
    required Widget child,
    required VoidCallback? onPressed,
    required String semanticLabel,
    String? tooltip,
    bool excludeSemantics = false,
  }) {
    Widget button;
    
    if (child is Icon) {
      button = IconButton(
        onPressed: onPressed,
        icon: child,
        tooltip: tooltip ?? semanticLabel,
      );
    } else {
      button = ElevatedButton(
        onPressed: onPressed,
        child: child,
      );
    }

    return Semantics(
      label: semanticLabel,
      button: true,
      enabled: onPressed != null,
      excludeSemantics: excludeSemantics,
      child: button,
    );
  }

  /// Create accessible text field with proper semantics
  static Widget accessibleTextField({
    required TextEditingController controller,
    required String label,
    required String hint,
    String? semanticLabel,
    TextInputType? keyboardType,
    bool obscureText = false,
    String? Function(String?)? validator,
    Widget? prefixIcon,
    Widget? suffixIcon,
  }) {
    return Semantics(
      label: semanticLabel ?? '$label input field',
      textField: true,
      child: TextForm<PERSON>ield(
        controller: controller,
        keyboardType: keyboardType,
        obscureText: obscureText,
        validator: validator,
        decoration: InputDecoration(
          labelText: label,
          hintText: hint,
          prefixIcon: prefixIcon,
          suffixIcon: suffixIcon,
        ),
      ),
    );
  }

  /// Create accessible card with proper semantics
  static Widget accessibleCard({
    required Widget child,
    required String semanticLabel,
    VoidCallback? onTap,
    String? semanticHint,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: Card(
        child: InkWell(
          onTap: onTap,
          child: child,
        ),
      ),
    );
  }

  /// Create accessible list item
  static Widget accessibleListTile({
    required Widget title,
    Widget? subtitle,
    Widget? leading,
    Widget? trailing,
    required String semanticLabel,
    VoidCallback? onTap,
    String? semanticHint,
  }) {
    return Semantics(
      label: semanticLabel,
      hint: semanticHint,
      button: onTap != null,
      child: ListTile(
        title: title,
        subtitle: subtitle,
        leading: leading,
        trailing: trailing,
        onTap: onTap,
      ),
    );
  }

  /// Create accessible image with proper semantics
  static Widget accessibleImage({
    required ImageProvider image,
    required String semanticLabel,
    double? width,
    double? height,
    BoxFit? fit,
  }) {
    return Semantics(
      label: semanticLabel,
      image: true,
      child: Image(
        image: image,
        width: width,
        height: height,
        fit: fit,
        semanticLabel: semanticLabel,
      ),
    );
  }

  /// Create accessible loading indicator
  static Widget accessibleLoadingIndicator({
    String? semanticLabel,
  }) {
    return Semantics(
      label: semanticLabel ?? 'Loading',
      liveRegion: true,
      child: const Center(
        child: CircularProgressIndicator(),
      ),
    );
  }

  /// Create accessible error message
  static Widget accessibleErrorMessage({
    required String message,
    VoidCallback? onRetry,
  }) {
    return Semantics(
      label: 'Error: $message',
      liveRegion: true,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 48,
            color: Colors.red,
            semanticLabel: 'Error icon',
          ),
          const SizedBox(height: 16),
          Text(
            message,
            textAlign: TextAlign.center,
            style: const TextStyle(fontSize: 16),
          ),
          if (onRetry != null) ...[
            const SizedBox(height: 16),
            accessibleButton(
              child: const Text('Retry'),
              onPressed: onRetry,
              semanticLabel: 'Retry button',
            ),
          ],
        ],
      ),
    );
  }

  /// Create accessible snackbar
  static SnackBar accessibleSnackBar({
    required String message,
    String? actionLabel,
    VoidCallback? onActionPressed,
    Duration duration = const Duration(seconds: 4),
  }) {
    return SnackBar(
      content: Text(message),
      duration: duration,
      action: actionLabel != null && onActionPressed != null
          ? SnackBarAction(
              label: actionLabel,
              onPressed: onActionPressed,
            )
          : null,
      behavior: SnackBarBehavior.floating,
    );
  }

  /// Announce message to screen readers
  static void announceMessage(String message) {
    SemanticsService.announce(message, TextDirection.ltr);
  }

  /// Check if accessibility features are enabled
  static bool isAccessibilityEnabled(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.accessibleNavigation ||
           mediaQuery.boldText ||
           mediaQuery.highContrast;
  }

  /// Get accessible text scale factor
  static double getAccessibleTextScale(BuildContext context) {
    final mediaQuery = MediaQuery.of(context);
    return mediaQuery.textScaler.scale(1.0);
  }

  /// Create accessible navigation
  static Widget accessibleBottomNavigationBar({
    required int currentIndex,
    required List<BottomNavigationBarItem> items,
    required ValueChanged<int> onTap,
  }) {
    return Semantics(
      label: 'Bottom navigation',
      child: BottomNavigationBar(
        currentIndex: currentIndex,
        items: items,
        onTap: onTap,
        type: BottomNavigationBarType.fixed,
      ),
    );
  }
}
