import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:go_router/go_router.dart';
import 'package:get_it/get_it.dart';

import '../config/app_config.dart';
import '../../presentation/splash/splash_screen.dart';

/// Service locator for dependency injection
class ServiceLocator {
  static final GetIt _getIt = GetIt.instance;
  
  static GetIt get instance => _getIt;
  
  /// Initialize all services and dependencies
  static Future<void> init() async {
    // Register core services
    await _registerCoreServices();
    
    // Register repositories
    await _registerRepositories();
    
    // Register BLoCs
    await _registerBlocs();
    
    // Register other services
    await _registerOtherServices();
  }
  
  /// Register core services
  static Future<void> _registerCoreServices() async {
    // TODO: Register API client, database, etc.
  }
  
  /// Register repositories
  static Future<void> _registerRepositories() async {
    // TODO: Register repositories
  }
  
  /// Register BLoCs
  static Future<void> _registerBlocs() async {
    // TODO: Register BLoCs
  }
  
  /// Register other services
  static Future<void> _registerOtherServices() async {
    // TODO: Register other services
  }
  
  /// Get list of BLoC providers for the app
  static List<BlocProvider> get blocProviders {
    return [
      // TODO: Add BLoC providers
    ];
  }
  
  /// App router configuration
  static GoRouter get router {
    return GoRouter(
      initialLocation: '/splash',
      routes: [
        GoRoute(
          path: '/splash',
          builder: (context, state) => const SplashScreen(),
        ),
        // TODO: Add more routes
      ],
    );
  }
}
