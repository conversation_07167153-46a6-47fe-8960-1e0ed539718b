/// Application-wide constants
class AppConstants {

  static const String baseUrl = 'https://tailor.batuk.space';

  // Route Names
  static const String splashRoute = '/splash';
  static const String onboardingRoute = '/onboarding';
  static const String authRoute = '/auth';
  static const String loginRoute = '/auth/login';
  static const String registerRoute = '/auth/register';
  static const String forgotPasswordRoute = '/auth/forgot-password';
  static const String homeRoute = '/home';
  static const String discoverRoute = '/discover';
  static const String ordersRoute = '/orders';
  static const String orderDetailsRoute = '/orders/:orderId';
  static const String profileRoute = '/profile';
  static const String settingsRoute = '/settings';
  static const String walletRoute = '/wallet';
  static const String chatRoute = '/chat';
  static const String notificationsRoute = '/notifications';
  
  // Storage Keys
  static const String accessTokenKey = 'access_token';
  static const String refreshTokenKey = 'refresh_token';
  static const String userDataKey = 'user_data';
  static const String onboardingCompletedKey = 'onboarding_completed';
  static const String biometricEnabledKey = 'biometric_enabled';
  static const String themeKey = 'theme_mode';
  static const String languageKey = 'language_code';
  static const String notificationsEnabledKey = 'notifications_enabled';
  static const String locationPermissionKey = 'location_permission';
  static const String cameraPermissionKey = 'camera_permission';
  
  // API Endpoints
  static const String authEndpoint = '/auth';
  static const String loginEndpoint = '/auth/login';
  static const String registerEndpoint = '/auth/register';
  static const String refreshTokenEndpoint = '/auth/refresh';
  static const String logoutEndpoint = '/auth/logout';
  static const String forgotPasswordEndpoint = '/auth/forgot-password';
  static const String resetPasswordEndpoint = '/auth/reset-password';
  static const String verifyEmailEndpoint = '/auth/verify-email';
  static const String resendVerificationEndpoint = '/auth/resend-verification';
  
  static const String usersEndpoint = '/users';
  static const String profileEndpoint = '/users/profile';
  static const String updateProfileEndpoint = '/users/profile';
  static const String uploadAvatarEndpoint = '/users/avatar';
  
  static const String tailorsEndpoint = '/tailors';
  static const String tailorProfileEndpoint = '/tailors/:id';
  static const String tailorPortfolioEndpoint = '/tailors/:id/portfolio';
  static const String tailorReviewsEndpoint = '/tailors/:id/reviews';
  static const String tailorServicesEndpoint = '/tailors/:id/services';
  
  static const String ordersEndpoint = '/orders';
  static const String createOrderEndpoint = '/orders';
  static const String orderDetailsEndpoint = '/orders/:id';
  static const String updateOrderStatusEndpoint = '/orders/:id/status';
  static const String cancelOrderEndpoint = '/orders/:id/cancel';
  
  static const String paymentsEndpoint = '/payments';
  static const String walletEndpoint = '/wallet';
  static const String transactionsEndpoint = '/transactions';
  static const String topUpWalletEndpoint = '/wallet/top-up';
  static const String withdrawEndpoint = '/wallet/withdraw';
  
  static const String chatEndpoint = '/chat';
  static const String messagesEndpoint = '/chat/:chatId/messages';
  static const String sendMessageEndpoint = '/chat/:chatId/messages';
  
  static const String notificationsEndpoint = '/notifications';
  static const String markNotificationReadEndpoint = '/notifications/:id/read';
  static const String fcmTokenEndpoint = '/notifications/fcm-token';
  
  static const String uploadEndpoint = '/upload';
  static const String uploadImageEndpoint = '/upload/image';
  static const String uploadDocumentEndpoint = '/upload/document';
  
  // Error Messages
  static const String networkErrorMessage = 'Network connection error. Please check your internet connection.';
  static const String serverErrorMessage = 'Server error occurred. Please try again later.';
  static const String unauthorizedErrorMessage = 'Session expired. Please login again.';
  static const String validationErrorMessage = 'Please check your input and try again.';
  static const String notFoundErrorMessage = 'Requested resource not found.';
  static const String timeoutErrorMessage = 'Request timeout. Please try again.';
  static const String unknownErrorMessage = 'An unexpected error occurred. Please try again.';
  
  // Success Messages
  static const String loginSuccessMessage = 'Welcome back!';
  static const String registerSuccessMessage = 'Account created successfully!';
  static const String profileUpdatedMessage = 'Profile updated successfully!';
  static const String orderCreatedMessage = 'Order placed successfully!';
  static const String orderCancelledMessage = 'Order cancelled successfully!';
  static const String paymentSuccessMessage = 'Payment processed successfully!';
  static const String messageSentMessage = 'Message sent!';
  
  // Validation Messages
  static const String emailRequiredMessage = 'Email is required';
  static const String emailInvalidMessage = 'Please enter a valid email address';
  static const String passwordRequiredMessage = 'Password is required';
  static const String passwordTooShortMessage = 'Password must be at least 8 characters';
  static const String passwordMismatchMessage = 'Passwords do not match';
  static const String nameRequiredMessage = 'Name is required';
  static const String phoneRequiredMessage = 'Phone number is required';
  static const String phoneInvalidMessage = 'Please enter a valid phone number';
  
  // User Roles
  static const String customerRole = 'customer';
  static const String tailorRole = 'tailor';
  static const String adminRole = 'admin';
  
  // Order Status
  static const String orderPending = 'pending';
  static const String orderConfirmed = 'confirmed';
  static const String orderInProgress = 'in_progress';
  static const String orderCompleted = 'completed';
  static const String orderCancelled = 'cancelled';
  static const String orderDelivered = 'delivered';
  
  // Payment Status
  static const String paymentPending = 'pending';
  static const String paymentCompleted = 'completed';
  static const String paymentFailed = 'failed';
  static const String paymentRefunded = 'refunded';
  
  // Notification Types
  static const String orderNotification = 'order';
  static const String messageNotification = 'message';
  static const String paymentNotification = 'payment';
  static const String promotionNotification = 'promotion';
  static const String systemNotification = 'system';
  
  // File Types
  static const String imageFileType = 'image';
  static const String documentFileType = 'document';
  static const String videoFileType = 'video';
  
  // Social Login Providers
  static const String googleProvider = 'google';
  static const String appleProvider = 'apple';
  static const String facebookProvider = 'facebook';
  
  // Theme Modes
  static const String lightTheme = 'light';
  static const String darkTheme = 'dark';
  static const String systemTheme = 'system';
  
  // Languages
  static const String englishLanguage = 'en';
  static const String mongolianLanguage = 'mn';
  
  // Measurement Units
  static const String centimetersUnit = 'cm';
  static const String inchesUnit = 'in';
  
  // Currency
  static const String defaultCurrency = 'MNT';
  static const String usdCurrency = 'USD';
  
  // Date Formats
  static const String dateFormat = 'yyyy-MM-dd';
  static const String timeFormat = 'HH:mm';
  static const String dateTimeFormat = 'yyyy-MM-dd HH:mm';
  static const String displayDateFormat = 'MMM dd, yyyy';
  static const String displayTimeFormat = 'h:mm a';
  
  // Regular Expressions
  static const String emailRegex = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$';
  static const String phoneRegex = r'^\+?[1-9]\d{1,14}$';
  static const String passwordRegex = r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)[a-zA-Z\d@$!%*?&]{8,}$';
  
  // Animation Tags
  static const String heroTagProfile = 'profile_hero';
  static const String heroTagOrder = 'order_hero';
  static const String heroTagTailor = 'tailor_hero';
  
  // Shared Preferences Keys
  static const String firstLaunchKey = 'first_launch';
  static const String lastSyncKey = 'last_sync';
  static const String offlineModeKey = 'offline_mode';
  static const String autoSyncKey = 'auto_sync';
}
