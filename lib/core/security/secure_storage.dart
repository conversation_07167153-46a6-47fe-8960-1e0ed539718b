import 'package:flutter_secure_storage/flutter_secure_storage.dart';

/// Secure storage service for sensitive data
class SecureStorageService {
  static const _storage = FlutterSecureStorage(
    aOptions: AndroidOptions(
      encryptedSharedPreferences: true,
      keyCipherAlgorithm: KeyCipherAlgorithm.RSA_ECB_OAEPwithSHA_256andMGF1Padding,
      storageCipherAlgorithm: StorageCipherAlgorithm.AES_GCM_NoPadding,
    ),
    iOptions: IOSOptions(
      accessibility: KeychainAccessibility.first_unlock_this_device,
    ),
  );

  // Keys for secure storage
  static const String _accessTokenKey = 'access_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userDataKey = 'user_data';
  static const String _biometricEnabledKey = 'biometric_enabled';

  /// Store access token securely
  static Future<void> storeAccessToken(String token) async {
    await _storage.write(key: _accessTokenKey, value: token);
  }

  /// Get access token
  static Future<String?> getAccessToken() async {
    return await _storage.read(key: _accessTokenKey);
  }

  /// Store refresh token securely
  static Future<void> storeRefreshToken(String token) async {
    await _storage.write(key: _refreshTokenKey, value: token);
  }

  /// Get refresh token
  static Future<String?> getRefreshToken() async {
    return await _storage.read(key: _refreshTokenKey);
  }

  /// Store user data securely
  static Future<void> storeUserData(String userData) async {
    await _storage.write(key: _userDataKey, value: userData);
  }

  /// Get user data
  static Future<String?> getUserData() async {
    return await _storage.read(key: _userDataKey);
  }

  /// Store biometric preference
  static Future<void> setBiometricEnabled(bool enabled) async {
    await _storage.write(key: _biometricEnabledKey, value: enabled.toString());
  }

  /// Get biometric preference
  static Future<bool> isBiometricEnabled() async {
    final value = await _storage.read(key: _biometricEnabledKey);
    return value == 'true';
  }

  /// Clear all stored data
  static Future<void> clearAll() async {
    await _storage.deleteAll();
  }

  /// Clear authentication data only
  static Future<void> clearAuthData() async {
    await _storage.delete(key: _accessTokenKey);
    await _storage.delete(key: _refreshTokenKey);
    await _storage.delete(key: _userDataKey);
  }

  /// Check if storage contains key
  static Future<bool> containsKey(String key) async {
    return await _storage.containsKey(key: key);
  }

  /// Get all stored keys
  static Future<Map<String, String>> getAllData() async {
    return await _storage.readAll();
  }
}

/// Input validation utilities
class InputValidator {
  /// Phone validation regex (international format)
  static final RegExp _phoneRegex = RegExp(
    r'^\+?[1-9]\d{1,14}$',
  );

  /// Validate phone number (legacy method - use isValidPhoneNumber instead)
  static bool isValidPhone(String phone) {
    return _phoneRegex.hasMatch(phone.trim());
  }

  /// Sanitize input string
  /// Sanitizes user input to prevent XSS and injection attacks
  ///
  /// This method provides comprehensive input sanitization by:
  /// - Trimming whitespace
  /// - Removing/escaping dangerous characters
  /// - Normalizing whitespace
  /// - Limiting input length
  /// - Handling null/empty inputs safely
  static String sanitizeInput(String? input, {
    int maxLength = 1000,
    bool allowHtml = false,
    bool preserveNewlines = false,
  }) {
    if (input == null || input.isEmpty) {
      return '';
    }

    String sanitized = input.trim();

    // Limit input length to prevent DoS attacks
    if (sanitized.length > maxLength) {
      sanitized = sanitized.substring(0, maxLength);
    }

    if (!allowHtml) {
      // More comprehensive XSS prevention
      sanitized = sanitized
          .replaceAll(RegExp(r'[<>]'), '') // Remove HTML tags
          .replaceAll(RegExp(r'["\x27]'), '') // Remove quotes (using hex for single quote)
          .replaceAll(RegExp(r'&'), '&amp;') // Escape ampersands
          .replaceAll(RegExp(r'javascript:', caseSensitive: false), '') // Remove javascript: URLs
          .replaceAll(RegExp(r'data:', caseSensitive: false), '') // Remove data: URLs
          .replaceAll(RegExp(r'vbscript:', caseSensitive: false), '') // Remove vbscript: URLs
          .replaceAll(RegExp(r'on\w+\s*=', caseSensitive: false), ''); // Remove event handlers
    }

    // Normalize whitespace
    if (preserveNewlines) {
      sanitized = sanitized.replaceAll(RegExp(r'[ \t]+'), ' '); // Normalize spaces and tabs only
    } else {
      sanitized = sanitized.replaceAll(RegExp(r'\s+'), ' '); // Normalize all whitespace
    }

    // Remove null bytes and other control characters
    sanitized = sanitized.replaceAll(RegExp(r'[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]'), '');

    return sanitized.trim();
  }

  /// Sanitizes input specifically for SQL queries (additional protection)
  static String sanitizeForSql(String? input) {
    if (input == null || input.isEmpty) {
      return '';
    }

    return input
        .replaceAll("'", "''") // Escape single quotes
        .replaceAll(';', '') // Remove semicolons
        .replaceAll('--', '') // Remove SQL comments
        .replaceAll('/*', '') // Remove block comment start
        .replaceAll('*/', '') // Remove block comment end
        .replaceAll(RegExp(r'\b(DROP|DELETE|INSERT|UPDATE|CREATE|ALTER|EXEC|EXECUTE)\b',
                    caseSensitive: false), ''); // Remove dangerous SQL keywords
  }

  /// Validates email format with comprehensive checks
  static bool isValidEmail(String? email) {
    if (email == null || email.isEmpty) {
      return false;
    }

    // RFC 5322 compliant email regex (simplified but robust)
    final emailRegex = RegExp(
      r'^[a-zA-Z0-9.!#$%&*+/=?^_`{|}~-]+@[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(?:\.[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$'
    );

    return emailRegex.hasMatch(email) &&
           email.length <= 254 && // RFC limit
           !email.contains('..') && // No consecutive dots
           !email.startsWith('.') && // No leading dot
           !email.endsWith('.'); // No trailing dot
  }

  /// Validates phone number format
  static bool isValidPhoneNumber(String? phone) {
    if (phone == null || phone.isEmpty) {
      return false;
    }

    // Remove common formatting characters
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)\+]'), '');

    // Check if it's all digits and reasonable length
    return RegExp(r'^\d{8,15}$').hasMatch(cleanPhone);
  }

  /// Validates password strength
  static bool isStrongPassword(String? password) {
    if (password == null || password.length < 8) {
      return false;
    }

    // Check for at least one uppercase, lowercase, digit, and special character
    return RegExp(r'^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]').hasMatch(password) &&
           password.length >= 8 &&
           password.length <= 128; // Reasonable upper limit
  }

  /// Validate and sanitize email
  static String? validateEmail(String? value) {
    if (value == null || value.isEmpty) {
      return 'Email is required';
    }
    
    final sanitized = sanitizeInput(value);
    if (!isValidEmail(sanitized)) {
      return 'Please enter a valid email address';
    }
    
    return null;
  }

  /// Validate and sanitize password
  static String? validatePassword(String? value) {
    if (value == null || value.isEmpty) {
      return 'Password is required';
    }
    
    if (value.length < 8) {
      return 'Password must be at least 8 characters';
    }
    
    if (!isStrongPassword(value)) {
      return 'Password must contain uppercase, lowercase, number and special character';
    }
    
    return null;
  }
}
