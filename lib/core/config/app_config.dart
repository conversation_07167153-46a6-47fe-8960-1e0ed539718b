/// Application configuration and environment settings
class AppConfig {
  static const String appName = 'TailorLink';
  static const String appVersion = '1.0.0';
  
  // API Configuration
  static const String baseUrl = 'https://tailor.batuk.space';
  static const String apiVersion = 'v1';
  static const String apiBaseUrl = '$baseUrl/api/$apiVersion';
  static const String websocketUrl = 'wss://tailor.batuk.space/ws';
  
  // Environment
  static const Environment environment = Environment.development;
  
  // Timeouts
  static const Duration connectTimeout = Duration(seconds: 30);
  static const Duration receiveTimeout = Duration(seconds: 30);
  static const Duration sendTimeout = Duration(seconds: 30);
  
  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;
  
  // Cache
  static const Duration cacheExpiration = Duration(hours: 24);
  static const int maxCacheSize = 100; // MB
  
  // File Upload
  static const int maxFileSize = 10 * 1024 * 1024; // 10MB
  static const List<String> allowedImageTypes = [
    'image/jpeg',
    'image/png',
    'image/gif',
    'image/webp'
  ];
  
  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 300);
  static const Duration longAnimation = Duration(milliseconds: 500);
  
  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 2.0;
  
  // Instagram-inspired UI Constants
  static const double storySize = 80.0;
  static const double profileImageSize = 40.0;
  static const double cardImageHeight = 300.0;
  static const double bottomNavHeight = 60.0;
  
  // Feature Flags
  static const bool enableBiometricAuth = true;
  static const bool enablePushNotifications = true;
  static const bool enableOfflineMode = true;
  static const bool enableAnalytics = true;
  static const bool enableCrashReporting = true;
  
  // Social Login
  static const bool enableGoogleLogin = true;
  static const bool enableAppleLogin = true;
  static const bool enableFacebookLogin = false;
  
  // Map Configuration
  static const double defaultLatitude = 47.9184;
  static const double defaultLongitude = 106.9177; // Ulaanbaatar, Mongolia
  static const double defaultZoom = 12.0;
  static const double searchRadius = 50.0; // km
  
  // Notification Settings
  static const String notificationChannelId = 'tailor_link_notifications';
  static const String notificationChannelName = 'TailorLink Notifications';
  static const String notificationChannelDescription = 
      'Notifications for orders, messages, and updates';
  
  // Security
  static const int maxLoginAttempts = 5;
  static const Duration lockoutDuration = Duration(minutes: 15);
  static const int otpLength = 6;
  static const Duration otpExpiration = Duration(minutes: 5);
  
  // Rating & Review
  static const int maxReviewLength = 500;
  static const int maxReviewImages = 5;
  static const double minRating = 1.0;
  static const double maxRating = 5.0;
  
  // Order Management
  static const Duration orderTimeout = Duration(hours: 24);
  static const Duration deliveryWindow = Duration(days: 7);
  static const int maxOrderItems = 10;
  
  // Chat & Communication
  static const int maxMessageLength = 1000;
  static const int maxChatImages = 10;
  static const Duration typingIndicatorTimeout = Duration(seconds: 3);
  
  // Wallet & Payments
  static const double minWalletBalance = 0.0;
  static const double maxWalletBalance = 1000000.0;
  static const double minTransactionAmount = 1.0;
  static const double maxTransactionAmount = 100000.0;
  
  // Performance
  static const int imageMemoryCacheSize = 100;
  static const int imageDiskCacheSize = 200; // MB
  static const int maxConcurrentRequests = 6;
  
  // Development Settings
  static const bool enableDebugMode = true;
  static const bool enableLogging = true;
  static const bool enableNetworkLogging = true;
  static const bool enablePerformanceMonitoring = true;
}

enum Environment {
  development,
  staging,
  production,
}

extension EnvironmentExtension on Environment {
  bool get isDevelopment => this == Environment.development;
  bool get isStaging => this == Environment.staging;
  bool get isProduction => this == Environment.production;
  
  String get name {
    switch (this) {
      case Environment.development:
        return 'Development';
      case Environment.staging:
        return 'Staging';
      case Environment.production:
        return 'Production';
    }
  }
}
