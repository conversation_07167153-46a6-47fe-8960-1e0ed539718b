/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TailorLink app.
/// To add a new string, add it to the appropriate .arb file in lib/l10n/
/// and run `flutter gen-l10n` to regenerate this file.


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for English (`en`).
class AppLocalizationsEn extends AppLocalizations {
  AppLocalizationsEn([String locale = 'en']) : super(locale);

  @override
  String get appName => 'TailorLink';

  @override
  String get goodMorning => 'Good Morning! 👋';

  @override
  String get findPerfectTailor => 'Find your perfect tailor';

  @override
  String get email => 'Email';

  @override
  String get emailHint => 'Enter your email address';

  @override
  String get password => 'Password';

  @override
  String get passwordHint => 'Enter your password';

  @override
  String get login => 'Login';

  @override
  String get register => 'Register';

  @override
  String get emailRequired => 'Email is required';

  @override
  String get emailInvalid => 'Please enter a valid email address';

  @override
  String get passwordRequired => 'Password is required';

  @override
  String get passwordTooShort => 'Password must be at least 8 characters';

  @override
  String get loginFailed => 'Login failed. Please try again.';

  @override
  String get networkError => 'Network connection error. Please check your internet connection.';

  @override
  String get search => 'Search';

  @override
  String get searchButton => 'Search button';

  @override
  String get profileButton => 'Profile button';

  @override
  String get notificationsButton => 'Notifications button';
}
