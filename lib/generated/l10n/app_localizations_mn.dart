/// Generated file. Do not edit.
///
/// This file contains the localized strings for the TailorLink app.
/// To add a new string, add it to the appropriate .arb file in lib/l10n/
/// and run `flutter gen-l10n` to regenerate this file.


// ignore: unused_import
import 'package:intl/intl.dart' as intl;
import 'app_localizations.dart';

// ignore_for_file: type=lint

/// The translations for Mongolian (`mn`).
class AppLocalizationsMn extends AppLocalizations {
  AppLocalizationsMn([String locale = 'mn']) : super(locale);

  @override
  String get appName => 'TailorLink';

  @override
  String get goodMorning => 'Өглөөний мэнд! 👋';

  @override
  String get findPerfectTailor => 'Төгс оёдолчоо олоорой';

  @override
  String get email => 'И-мэйл';

  @override
  String get emailHint => 'И-мэйл хаягаа оруулна уу';

  @override
  String get password => 'Нууц үг';

  @override
  String get passwordHint => 'Нууц үгээ оруулна уу';

  @override
  String get login => 'Нэвтрэх';

  @override
  String get register => 'Бүртгүүлэх';

  @override
  String get emailRequired => 'И-мэйл шаардлагатай';

  @override
  String get emailInvalid => 'Зөв и-мэйл хаяг оруулна уу';

  @override
  String get passwordRequired => 'Нууц үг шаардлагатай';

  @override
  String get passwordTooShort => 'Нууц үг дор хаяж 8 тэмдэгт байх ёстой';

  @override
  String get loginFailed => 'Нэвтрэх амжилтгүй. Дахин оролдоно уу.';

  @override
  String get networkError => 'Сүлжээний алдаа. Интернет холболтоо шалгана уу.';

  @override
  String get search => 'Хайх';

  @override
  String get searchButton => 'Хайх товчлуур';

  @override
  String get profileButton => 'Профайл товчлуур';

  @override
  String get notificationsButton => 'Мэдэгдэл товчлуур';
}
