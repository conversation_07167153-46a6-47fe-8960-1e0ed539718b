import 'package:flutter_secure_storage/flutter_secure_storage.dart';

import 'api_service.dart';
import '../models/tailor_model.dart';
import '../models/api_response.dart';
import '../../core/config/app_config.dart';

/// Centralized API service manager
/// 
/// This class provides a single point of access to all API services
/// and handles authentication token management across the app.
class ApiServiceManager {
  static ApiServiceManager? _instance;
  static const _storage = FlutterSecureStorage();

  late final ApiService _apiService;
  late final TailorApiService _tailorService;
  late final AuthApiService _authService;
  late final OrderApiService _orderService;
  late final ChatApiService _chatService;
  late final UserApiService _userService;

  ApiServiceManager._internal() {
    _apiService = ApiService();
    _tailorService = TailorApiService(_apiService);
    _authService = AuthApiService(_apiService);
    _orderService = OrderApiService(_apiService);
    _chatService = ChatApiService(_apiService);
    _userService = UserApiService(_apiService);
    
    _initializeAuth();
  }

  /// Singleton instance
  static ApiServiceManager get instance {
    _instance ??= ApiServiceManager._internal();
    return _instance!;
  }

  /// Initialize authentication from stored token
  Future<void> _initializeAuth() async {
    try {
      final token = await _storage.read(key: 'auth_token');
      if (token != null) {
        _apiService.setAuthToken(token);
      }
    } catch (e) {
      if (AppConfig.environment.isDevelopment) {
        print('Failed to initialize auth: $e');
      }
    }
  }

  /// Set authentication token
  Future<void> setAuthToken(String token) async {
    _apiService.setAuthToken(token);
    await _storage.write(key: 'auth_token', value: token);
  }

  /// Clear authentication token
  Future<void> clearAuthToken() async {
    _apiService.clearAuthToken();
    await _storage.delete(key: 'auth_token');
    await _storage.delete(key: 'refresh_token');
  }

  /// Check if user is authenticated
  Future<bool> isAuthenticated() async {
    final token = await _storage.read(key: 'auth_token');
    return token != null;
  }

  // Service Getters
  TailorApiService get tailors => _tailorService;
  AuthApiService get auth => _authService;
  OrderApiService get orders => _orderService;
  ChatApiService get chats => _chatService;
  UserApiService get user => _userService;

  /// Dispose all services
  void dispose() {
    _apiService.dispose();
  }
}

/// Extension for easy access to API services
extension ApiServiceExtension on ApiServiceManager {
  /// Quick access to search tailors
  Future<List<TailorModel>> searchTailors({
    String? query,
    List<String>? categories,
    double? minRating,
    int? maxDistance,
    bool? isVerifiedOnly,
    double? latitude,
    double? longitude,
  }) async {
    final filters = TailorSearchFilters(
      query: query,
      categories: categories,
      minRating: minRating,
      maxDistance: maxDistance,
      isVerifiedOnly: isVerifiedOnly,
      latitude: latitude,
      longitude: longitude,
    );

    final response = await tailors.searchTailors(filters: filters);
    return response.data ?? [];
  }

  /// Quick access to get featured tailors
  Future<List<TailorModel>> getFeaturedTailors({
    double? latitude,
    double? longitude,
  }) async {
    final response = await tailors.getFeaturedTailors();
    return response.data ?? [];
  }

  /// Quick access to get nearby tailors
  Future<List<TailorModel>> getNearbyTailors({
    required double latitude,
    required double longitude,
    int radius = 10,
  }) async {
    final response = await tailors.getNearbyTailors(
      latitude: latitude,
      longitude: longitude,
      radius: radius,
    );
    return response.data ?? [];
  }

  /// Quick login
  Future<bool> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await auth.login(email: email, password: password);
      
      if (response.success && response.data != null) {
        final token = response.data!['token'] as String?;
        if (token != null) {
          await setAuthToken(token);
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Quick register
  Future<bool> register({
    required String name,
    required String email,
    required String password,
    required String phone,
    String userType = 'customer',
  }) async {
    try {
      final response = await auth.register(
        name: name,
        email: email,
        password: password,
        phone: phone,
        userType: userType,
      );
      
      if (response.success && response.data != null) {
        final token = response.data!['token'] as String?;
        if (token != null) {
          await setAuthToken(token);
          return true;
        }
      }
      return false;
    } catch (e) {
      return false;
    }
  }

  /// Quick logout
  Future<void> logout() async {
    try {
      await auth.logout();
    } catch (e) {
      // Continue with logout even if API call fails
    } finally {
      await clearAuthToken();
    }
  }
}

/// Global API service instance
final apiService = ApiServiceManager.instance;
