import 'dart:convert';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/api_response.dart';
import '../models/user_model.dart';
import 'api_service.dart';

/// Authentication service for handling login, register, and token management
class AuthService {
  final ApiService _apiService;
  static const String _tokenKey = 'auth_token';
  static const String _refreshTokenKey = 'refresh_token';
  static const String _userKey = 'user_data';

  AuthService(this._apiService);

  /// Login with email and password
  Future<ApiResponse<AuthResponse>> login({
    required String email,
    required String password,
  }) async {
    try {
      final response = await _apiService.post<AuthResponse>(
        '/auth/login',
        body: {
          'email': email,
          'password': password,
        },
        fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveAuthData(response.data!);
        _apiService.setAuthToken(response.data!.accessToken);
      }

      return response;
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'LOGIN_ERROR',
          message: 'Login failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Register new user
  Future<ApiResponse<AuthResponse>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    try {
      final response = await _apiService.post<AuthResponse>(
        '/auth/register',
        body: {
          'email': email,
          'password': password,
          'first_name': firstName,
          'last_name': lastName,
          if (phone != null) 'phone': phone,
        },
        fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveAuthData(response.data!);
        _apiService.setAuthToken(response.data!.accessToken);
      }

      return response;
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'REGISTER_ERROR',
          message: 'Registration failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Social login (Google, Facebook, Apple)
  Future<ApiResponse<AuthResponse>> socialLogin({
    required String provider,
    required String token,
  }) async {
    try {
      final response = await _apiService.post<AuthResponse>(
        '/auth/social',
        body: {
          'provider': provider,
          'token': token,
        },
        fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveAuthData(response.data!);
        _apiService.setAuthToken(response.data!.accessToken);
      }

      return response;
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'SOCIAL_LOGIN_ERROR',
          message: 'Social login failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    try {
      await _apiService.post<void>('/auth/logout');
      await _clearAuthData();
      _apiService.clearAuthToken();

      return const ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
      );
    } catch (e) {
      // Clear local data even if API call fails
      await _clearAuthData();
      _apiService.clearAuthToken();

      return const ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
      );
    }
  }

  /// Refresh access token
  Future<ApiResponse<AuthResponse>> refreshToken() async {
    try {
      final refreshToken = await _getRefreshToken();
      if (refreshToken == null) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'NO_REFRESH_TOKEN',
            message: 'No refresh token available',
          ),
        );
      }

      final response = await _apiService.post<AuthResponse>(
        '/auth/refresh',
        body: {'refresh_token': refreshToken},
        fromJson: (data) => AuthResponse.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveAuthData(response.data!);
        _apiService.setAuthToken(response.data!.accessToken);
      }

      return response;
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'REFRESH_ERROR',
          message: 'Token refresh failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Get current user
  Future<ApiResponse<UserModel>> getCurrentUser() async {
    try {
      final response = await _apiService.get<UserModel>(
        '/auth/me',
        fromJson: (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveUser(response.data!);
      }

      return response;
    } catch (e) {
      return ApiResponse<UserModel>(
        success: false,
        error: ApiError(
          code: 'GET_USER_ERROR',
          message: 'Failed to get user: ${e.toString()}',
        ),
      );
    }
  }

  /// Update user profile
  Future<ApiResponse<UserModel>> updateProfile({
    String? firstName,
    String? lastName,
    String? phone,
    String? profileImageUrl,
  }) async {
    try {
      final body = <String, dynamic>{};
      if (firstName != null) body['first_name'] = firstName;
      if (lastName != null) body['last_name'] = lastName;
      if (phone != null) body['phone'] = phone;
      if (profileImageUrl != null) body['profile_image_url'] = profileImageUrl;

      final response = await _apiService.put<UserModel>(
        '/auth/profile',
        body: body,
        fromJson: (data) => UserModel.fromJson(data as Map<String, dynamic>),
      );

      if (response.success && response.data != null) {
        await _saveUser(response.data!);
      }

      return response;
    } catch (e) {
      return ApiResponse<UserModel>(
        success: false,
        error: ApiError(
          code: 'UPDATE_PROFILE_ERROR',
          message: 'Failed to update profile: ${e.toString()}',
        ),
      );
    }
  }

  /// Change password
  Future<ApiResponse<void>> changePassword({
    required String currentPassword,
    required String newPassword,
  }) async {
    try {
      final response = await _apiService.put<void>(
        '/auth/password',
        body: {
          'current_password': currentPassword,
          'new_password': newPassword,
        },
      );

      return response;
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: ApiError(
          code: 'CHANGE_PASSWORD_ERROR',
          message: 'Failed to change password: ${e.toString()}',
        ),
      );
    }
  }

  /// Request password reset
  Future<ApiResponse<void>> requestPasswordReset(String email) async {
    try {
      final response = await _apiService.post<void>(
        '/auth/password-reset',
        body: {'email': email},
      );

      return response;
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: ApiError(
          code: 'PASSWORD_RESET_ERROR',
          message: 'Failed to request password reset: ${e.toString()}',
        ),
      );
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _getAccessToken();
    return token != null;
  }

  /// Get stored user data
  Future<UserModel?> getStoredUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      }
    } catch (e) {
      // Handle error silently
    }
    return null;
  }

  /// Initialize auth service (call on app start)
  Future<void> initialize() async {
    final token = await _getAccessToken();
    if (token != null) {
      _apiService.setAuthToken(token);
    }
  }

  /// Save authentication data to local storage
  Future<void> _saveAuthData(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, authResponse.accessToken);
    await prefs.setString(_refreshTokenKey, authResponse.refreshToken);
    await prefs.setString(_userKey, jsonEncode(authResponse.user.toJson()));
  }

  /// Save user data to local storage
  Future<void> _saveUser(UserModel user) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_userKey, jsonEncode(user.toJson()));
  }

  /// Clear authentication data from local storage
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userKey);
  }

  /// Get access token from local storage
  Future<String?> _getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }

  /// Get refresh token from local storage
  Future<String?> _getRefreshToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_refreshTokenKey);
  }
}
