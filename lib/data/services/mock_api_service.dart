import 'dart:math';

import '../models/api_response.dart';
import '../models/tailor_model.dart';

/// Mock API service for development and testing
class MockApiService {
  static const Duration _mockDelay = Duration(milliseconds: 800);

  /// Get featured tailors (mock data)
  Future<ApiResponse<List<TailorModel>>> getFeaturedTailors({
    int limit = 10,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      final tailors = _generateMockTailors(limit, isFeatured: true);
      
      return ApiResponse<List<TailorModel>>(
        success: true,
        message: 'Featured tailors retrieved successfully',
        data: tailors,
        meta: ApiMeta(
          currentPage: 1,
          totalPages: 1,
          totalItems: tailors.length,
          itemsPerPage: limit,
          hasNextPage: false,
          hasPreviousPage: false,
        ),
      );
    } catch (e) {
      return ApiResponse<List<TailorModel>>(
        success: false,
        error: ApiError(
          code: 'MOCK_ERROR',
          message: 'Failed to fetch featured tailors: ${e.toString()}',
        ),
      );
    }
  }

  /// Get nearby tailors (mock data)
  Future<ApiResponse<List<TailorModel>>> getNearbyTailors({
    required double latitude,
    required double longitude,
    int radius = 10,
    int limit = 20,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      final tailors = _generateMockTailors(limit, isNearby: true);
      
      return ApiResponse<List<TailorModel>>(
        success: true,
        message: 'Nearby tailors retrieved successfully',
        data: tailors,
        meta: ApiMeta(
          currentPage: 1,
          totalPages: 2,
          totalItems: limit + 5,
          itemsPerPage: limit,
          hasNextPage: true,
          hasPreviousPage: false,
        ),
      );
    } catch (e) {
      return ApiResponse<List<TailorModel>>(
        success: false,
        error: ApiError(
          code: 'MOCK_ERROR',
          message: 'Failed to fetch nearby tailors: ${e.toString()}',
        ),
      );
    }
  }

  /// Search tailors (mock data)
  Future<ApiResponse<List<TailorModel>>> searchTailors({
    required TailorSearchFilters filters,
    int page = 1,
    int limit = 20,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      final tailors = _generateMockTailors(limit);
      
      return ApiResponse<List<TailorModel>>(
        success: true,
        message: 'Search results retrieved successfully',
        data: tailors,
        meta: ApiMeta(
          currentPage: page,
          totalPages: 3,
          totalItems: limit * 3,
          itemsPerPage: limit,
          hasNextPage: page < 3,
          hasPreviousPage: page > 1,
        ),
      );
    } catch (e) {
      return ApiResponse<List<TailorModel>>(
        success: false,
        error: ApiError(
          code: 'MOCK_ERROR',
          message: 'Failed to search tailors: ${e.toString()}',
        ),
      );
    }
  }

  /// Get categories (mock data)
  Future<ApiResponse<List<String>>> getCategories() async {
    await Future.delayed(_mockDelay);

    const categories = [
      'All',
      'Shirts',
      'Suits',
      'Dresses',
      'Pants',
      'Jackets',
      'Traditional',
      'Formal',
      'Casual',
      'Wedding',
      'Business',
      'Evening Wear',
    ];

    return const ApiResponse<List<String>>(
      success: true,
      message: 'Categories retrieved successfully',
      data: categories,
    );
  }

  /// Get services (mock data)
  Future<ApiResponse<List<String>>> getServices() async {
    await Future.delayed(_mockDelay);

    const services = [
      'Custom Tailoring',
      'Alterations',
      'Repairs',
      'Dry Cleaning',
      'Embroidery',
      'Hemming',
      'Resizing',
      'Button Replacement',
      'Zipper Repair',
      'Lining Replacement',
    ];

    return const ApiResponse<List<String>>(
      success: true,
      message: 'Services retrieved successfully',
      data: services,
    );
  }

  /// Generate mock tailor data
  List<TailorModel> _generateMockTailors(
    int count, {
    bool isFeatured = false,
    bool isNearby = false,
  }) {
    final random = Random();
    final tailors = <TailorModel>[];

    final names = [
      'Master Chen',
      'Sarah Johnson',
      'Ahmed Hassan',
      'Maria Rodriguez',
      'David Kim',
      'Elena Petrov',
      'James Wilson',
      'Priya Sharma',
      'Mohammed Ali',
      'Anna Kowalski',
    ];

    final specialties = [
      'Traditional Suits',
      'Wedding Dresses',
      'Casual Wear',
      'Formal Attire',
      'Business Suits',
      'Evening Gowns',
      'Traditional Clothing',
      'Modern Fashion',
      'Luxury Garments',
      'Vintage Restoration',
    ];

    final services = [
      'Custom Tailoring',
      'Alterations',
      'Repairs',
      'Dry Cleaning',
      'Embroidery',
    ];

    final categories = [
      'Shirts',
      'Suits',
      'Dresses',
      'Pants',
      'Jackets',
    ];

    for (int i = 0; i < count; i++) {
      final name = names[i % names.length];
      final specialty = specialties[i % specialties.length];
      
      tailors.add(TailorModel(
        id: 'tailor_${i + 1}',
        name: name,
        email: '${name.toLowerCase().replaceAll(' ', '.')}@example.com',
        phone: '+976 ${random.nextInt(90000000) + 10000000}',
        specialty: specialty,
        rating: 3.5 + random.nextDouble() * 1.5, // 3.5 - 5.0
        reviewCount: random.nextInt(200) + 10,
        imageUrl: _getRandomImageUrl(i),
        isVerified: random.nextBool(),
        isOnline: random.nextBool(),
        bio: 'Experienced tailor specializing in $specialty with over ${random.nextInt(15) + 5} years of experience.',
        location: TailorLocation(
          latitude: 47.9184 + (random.nextDouble() - 0.5) * 0.1,
          longitude: 106.9177 + (random.nextDouble() - 0.5) * 0.1,
          address: '${random.nextInt(999) + 1} ${_getRandomStreet()} Street',
          city: 'Ulaanbaatar',
          state: 'Ulaanbaatar',
          country: 'Mongolia',
          postalCode: '${random.nextInt(90000) + 10000}',
          distance: isNearby ? random.nextDouble() * 5 : random.nextDouble() * 20,
        ),
        services: services.take(random.nextInt(3) + 2).toList(),
        categories: categories.take(random.nextInt(3) + 2).toList(),
        priceRange: PriceRange(
          level: random.nextInt(4) + 1,
          minPrice: (random.nextInt(50) + 10).toDouble(),
          maxPrice: (random.nextInt(200) + 100).toDouble(),
        ),
        workingHours: _generateWorkingHours(),
        createdAt: DateTime.now().subtract(Duration(days: random.nextInt(365))),
        updatedAt: DateTime.now().subtract(Duration(days: random.nextInt(30))),
      ));
    }

    return tailors;
  }

  String _getRandomImageUrl(int index) {
    final imageIds = [
      '507003211169-0a1dd7228f2d',
      '494790108755-2616b612b786',
      '472099645785-5658abf4ff4e',
      '438761580879-e46e8bbcd13d',
      '500648020814-d60eaa4c7d2e',
      '463453671221-6a4f9c4a4a44',
      '507003211169-0a1dd7228f2d',
      '494790108755-2616b612b786',
      '472099645785-5658abf4ff4e',
      '438761580879-e46e8bbcd13d',
    ];
    
    final imageId = imageIds[index % imageIds.length];
    return 'https://images.unsplash.com/photo-$imageId?w=300&h=300&fit=crop&crop=face';
  }

  String _getRandomStreet() {
    final streets = [
      'Peace',
      'Sukhbaatar',
      'Chinggis',
      'Ikh Toiruu',
      'Seoul',
      'Tokyo',
      'Beijing',
      'Zaisan',
      'Bayangol',
      'Khan-Uul',
    ];
    
    return streets[Random().nextInt(streets.length)];
  }

  WorkingHours _generateWorkingHours() {
    final schedule = <int, DaySchedule>{};
    
    for (int day = 1; day <= 7; day++) {
      // Most tailors are closed on Sunday (day 7)
      final isOpen = day != 7 || Random().nextBool();
      
      schedule[day] = DaySchedule(
        isOpen: isOpen,
        openTime: const TimeOfDay(hour: 9, minute: 0),
        closeTime: const TimeOfDay(hour: 18, minute: 0),
      );
    }
    
    return WorkingHours(schedule: schedule);
  }
}
