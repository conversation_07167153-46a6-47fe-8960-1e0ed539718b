import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import '../../core/config/app_config.dart';
import '../../core/constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/tailor_model.dart';

/// Main API service for handling HTTP requests
class ApiService {
  static const String _baseUrl = AppConstants.baseUrl;
  static const Duration _timeout = Duration(seconds: 30);

  final http.Client _client;
  String? _authToken;

  ApiService({http.Client? client}) : _client = client ?? http.Client();

  /// Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  /// Get default headers
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  /// Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final uriWithParams = queryParams != null
          ? uri.replace(queryParameters: queryParams)
          : uri;

      final response = await _client
          .get(uriWithParams, headers: _headers)
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .post(
            uri,
            headers: _headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .put(
            uri,
            headers: _headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .delete(uri, headers: _headers)
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Handle HTTP response
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse<T>.fromJson(jsonData, fromJson);
      } else {
        return ApiResponse<T>(
          success: false,
          error: ApiError.fromJson(jsonData['error'] as Map<String, dynamic>),
        );
      }
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        error: ApiError(
          code: 'PARSE_ERROR',
          message: 'Failed to parse response: ${e.toString()}',
        ),
      );
    }
  }

  /// Handle errors
  ApiResponse<T> _handleError<T>(dynamic error) {
    String message;
    String code;

    if (error is SocketException) {
      message = 'No internet connection';
      code = 'NO_INTERNET';
    } else if (error is HttpException) {
      message = 'HTTP error: ${error.message}';
      code = 'HTTP_ERROR';
    } else if (error.toString().contains('TimeoutException')) {
      message = 'Request timeout';
      code = 'TIMEOUT';
    } else {
      message = 'An unexpected error occurred: ${error.toString()}';
      code = 'UNKNOWN_ERROR';
    }

    return ApiResponse<T>(
      success: false,
      error: ApiError(code: code, message: message),
    );
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Tailor-specific API service
class TailorApiService {
  final ApiService _apiService;

  TailorApiService(this._apiService);

  /// Get featured tailors
  Future<ApiResponse<List<TailorModel>>> getFeaturedTailors({
    int limit = 10,
  }) async {
    return await _apiService.get<List<TailorModel>>(
      '/tailors/featured',
      queryParams: {'limit': limit.toString()},
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get nearby tailors
  Future<ApiResponse<List<TailorModel>>> getNearbyTailors({
    required double latitude,
    required double longitude,
    int radius = 10, // km
    int limit = 20,
  }) async {
    return await _apiService.get<List<TailorModel>>(
      '/tailors/nearby',
      queryParams: {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'radius': radius.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Search tailors
  Future<ApiResponse<List<TailorModel>>> searchTailors({
    required TailorSearchFilters filters,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
      ...filters.toJson().map((key, value) => MapEntry(key, value.toString())),
    };

    return await _apiService.get<List<TailorModel>>(
      '/tailors/search',
      queryParams: queryParams,
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get tailor by ID
  Future<ApiResponse<TailorModel>> getTailorById(String id) async {
    return await _apiService.get<TailorModel>(
      '/tailors/$id',
      fromJson: (data) => TailorModel.fromJson(data as Map<String, dynamic>),
    );
  }

  /// Get tailor categories
  Future<ApiResponse<List<String>>> getCategories() async {
    return await _apiService.get<List<String>>(
      '/tailors/categories',
      fromJson: (data) => List<String>.from(data as List),
    );
  }

  /// Get tailor services
  Future<ApiResponse<List<String>>> getServices() async {
    return await _apiService.get<List<String>>(
      '/tailors/services',
      fromJson: (data) => List<String>.from(data as List),
    );
  }

  /// Get tailor reviews
  Future<ApiResponse<List<Map<String, dynamic>>>> getTailorReviews({
    required String tailorId,
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/tailors/$tailorId/reviews',
      queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Add review for tailor
  Future<ApiResponse<Map<String, dynamic>>> addReview({
    required String tailorId,
    required double rating,
    required String comment,
    List<String>? images,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/tailors/$tailorId/reviews',
      body: {
        'rating': rating,
        'comment': comment,
        'images': images,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get tailor portfolio
  Future<ApiResponse<List<Map<String, dynamic>>>> getTailorPortfolio({
    required String tailorId,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/tailors/$tailorId/portfolio',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get tailor availability
  Future<ApiResponse<Map<String, dynamic>>> getTailorAvailability({
    required String tailorId,
    DateTime? date,
  }) async {
    final queryParams = <String, String>{};
    if (date != null) {
      queryParams['date'] = date.toIso8601String();
    }

    return await _apiService.get<Map<String, dynamic>>(
      '/tailors/$tailorId/availability',
      queryParams: queryParams,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Book appointment with tailor
  Future<ApiResponse<Map<String, dynamic>>> bookAppointment({
    required String tailorId,
    required DateTime dateTime,
    required String serviceType,
    String? notes,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/tailors/$tailorId/appointments',
      body: {
        'date_time': dateTime.toIso8601String(),
        'service_type': serviceType,
        'notes': notes,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }
}

/// Authentication API service
class AuthApiService {
  final ApiService _apiService;

  AuthApiService(this._apiService);

  /// Login user
  Future<ApiResponse<Map<String, dynamic>>> login({
    required String email,
    required String password,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/auth/login',
      body: {
        'email': email,
        'password': password,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Register user
  Future<ApiResponse<Map<String, dynamic>>> register({
    required String name,
    required String email,
    required String password,
    required String phone,
    String userType = 'customer',
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/auth/register',
      body: {
        'name': name,
        'email': email,
        'password': password,
        'phone': phone,
        'user_type': userType,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Logout user
  Future<ApiResponse<void>> logout() async {
    return await _apiService.post<void>('/auth/logout');
  }

  /// Refresh token
  Future<ApiResponse<Map<String, dynamic>>> refreshToken({
    required String refreshToken,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/auth/refresh',
      body: {'refresh_token': refreshToken},
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Verify email
  Future<ApiResponse<void>> verifyEmail({
    required String token,
  }) async {
    return await _apiService.post<void>(
      '/auth/verify-email',
      body: {'token': token},
    );
  }

  /// Request password reset
  Future<ApiResponse<void>> requestPasswordReset({
    required String email,
  }) async {
    return await _apiService.post<void>(
      '/auth/forgot-password',
      body: {'email': email},
    );
  }

  /// Reset password
  Future<ApiResponse<void>> resetPassword({
    required String token,
    required String password,
  }) async {
    return await _apiService.post<void>(
      '/auth/reset-password',
      body: {
        'token': token,
        'password': password,
      },
    );
  }
}

/// Order API service
class OrderApiService {
  final ApiService _apiService;

  OrderApiService(this._apiService);

  /// Create new order
  Future<ApiResponse<Map<String, dynamic>>> createOrder({
    required String tailorId,
    required String serviceType,
    required Map<String, dynamic> measurements,
    required Map<String, dynamic> preferences,
    String? notes,
    DateTime? deadline,
    List<String>? attachments,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/orders',
      body: {
        'tailor_id': tailorId,
        'service_type': serviceType,
        'measurements': measurements,
        'preferences': preferences,
        'notes': notes,
        'deadline': deadline?.toIso8601String(),
        'attachments': attachments,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user orders
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserOrders({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };

    if (status != null) {
      queryParams['status'] = status;
    }

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/orders',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get order by ID
  Future<ApiResponse<Map<String, dynamic>>> getOrderById(String orderId) async {
    return await _apiService.get<Map<String, dynamic>>(
      '/orders/$orderId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update order status
  Future<ApiResponse<Map<String, dynamic>>> updateOrderStatus({
    required String orderId,
    required String status,
    String? notes,
  }) async {
    return await _apiService.put<Map<String, dynamic>>(
      '/orders/$orderId/status',
      body: {
        'status': status,
        'notes': notes,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Cancel order
  Future<ApiResponse<void>> cancelOrder({
    required String orderId,
    String? reason,
  }) async {
    return await _apiService.put<void>(
      '/orders/$orderId/cancel',
      body: {
        'reason': reason,
      },
    );
  }

  /// Rate order
  Future<ApiResponse<void>> rateOrder({
    required String orderId,
    required double rating,
    String? review,
  }) async {
    return await _apiService.post<void>(
      '/orders/$orderId/rate',
      body: {
        'rating': rating,
        'review': review,
      },
    );
  }
}

/// Chat API service
class ChatApiService {
  final ApiService _apiService;

  ChatApiService(this._apiService);

  /// Get user chats
  Future<ApiResponse<List<Map<String, dynamic>>>> getUserChats({
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/chats',
      queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get chat messages
  Future<ApiResponse<List<Map<String, dynamic>>>> getChatMessages({
    required String chatId,
    int page = 1,
    int limit = 50,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/chats/$chatId/messages',
      queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Send message
  Future<ApiResponse<Map<String, dynamic>>> sendMessage({
    required String chatId,
    required String message,
    List<String>? attachments,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/chats/$chatId/messages',
      body: {
        'message': message,
        'attachments': attachments,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Mark messages as read
  Future<ApiResponse<void>> markMessagesAsRead({
    required String chatId,
    required List<String> messageIds,
  }) async {
    return await _apiService.put<void>(
      '/chats/$chatId/read',
      body: {
        'message_ids': messageIds,
      },
    );
  }

  /// Create or get chat with tailor
  Future<ApiResponse<Map<String, dynamic>>> createChatWithTailor({
    required String tailorId,
    String? orderId,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/chats',
      body: {
        'tailor_id': tailorId,
        'order_id': orderId,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }
}

/// User Profile API service
class UserApiService {
  final ApiService _apiService;

  UserApiService(this._apiService);

  /// Get current user profile
  Future<ApiResponse<Map<String, dynamic>>> getCurrentUser() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/user/profile',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update user profile
  Future<ApiResponse<Map<String, dynamic>>> updateProfile({
    String? name,
    String? email,
    String? phone,
    String? bio,
    Map<String, dynamic>? address,
  }) async {
    final body = <String, dynamic>{};
    if (name != null) body['name'] = name;
    if (email != null) body['email'] = email;
    if (phone != null) body['phone'] = phone;
    if (bio != null) body['bio'] = bio;
    if (address != null) body['address'] = address;

    return await _apiService.put<Map<String, dynamic>>(
      '/user/profile',
      body: body,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Upload profile image
  Future<ApiResponse<Map<String, dynamic>>> uploadProfileImage({
    required String imagePath,
  }) async {
    // This would typically use multipart/form-data
    // For now, we'll use a simple approach
    return await _apiService.post<Map<String, dynamic>>(
      '/user/profile/image',
      body: {
        'image_path': imagePath,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user measurements
  Future<ApiResponse<Map<String, dynamic>>> getMeasurements() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/user/measurements',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update user measurements
  Future<ApiResponse<Map<String, dynamic>>> updateMeasurements({
    required Map<String, dynamic> measurements,
  }) async {
    return await _apiService.put<Map<String, dynamic>>(
      '/user/measurements',
      body: {'measurements': measurements},
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user preferences
  Future<ApiResponse<Map<String, dynamic>>> getPreferences() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/user/preferences',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update user preferences
  Future<ApiResponse<Map<String, dynamic>>> updatePreferences({
    required Map<String, dynamic> preferences,
  }) async {
    return await _apiService.put<Map<String, dynamic>>(
      '/user/preferences',
      body: {'preferences': preferences},
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Delete user account
  Future<ApiResponse<void>> deleteAccount({
    required String password,
    String? reason,
  }) async {
    return await _apiService.delete<void>(
      '/user/account',
    );
  }
}

/// Training API service
class TrainingApiService {
  final ApiService _apiService;

  TrainingApiService(this._apiService);

  /// Get course categories
  Future<ApiResponse<List<Map<String, dynamic>>>> getCategories() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/categories',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get featured courses
  Future<ApiResponse<List<Map<String, dynamic>>>> getFeaturedCourses({
    int limit = 10,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/courses/featured',
      queryParams: {'limit': limit.toString()},
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get all courses
  Future<ApiResponse<List<Map<String, dynamic>>>> getAllCourses({
    String? category,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (category != null) queryParams['category'] = category;

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/courses',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get user's enrolled courses
  Future<ApiResponse<List<Map<String, dynamic>>>> getMyCourses() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/enrollments',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Enroll in course
  Future<ApiResponse<Map<String, dynamic>>> enrollInCourse({
    required String courseId,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/training/courses/$courseId/enroll',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update course progress
  Future<ApiResponse<void>> updateProgress({
    required String courseId,
    required double progress,
  }) async {
    return await _apiService.put<void>(
      '/training/courses/$courseId/progress',
      body: {'progress': progress},
    );
  }

  /// Complete course
  Future<ApiResponse<Map<String, dynamic>>> completeCourse({
    required String courseId,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/training/courses/$courseId/complete',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get user certificates
  Future<ApiResponse<List<Map<String, dynamic>>>> getCertificates() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/certificates',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get available certifications
  Future<ApiResponse<List<Map<String, dynamic>>>> getAvailableCertifications() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/training/certifications',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }
}

/// Supply Chain API service
class SupplyChainApiService {
  final ApiService _apiService;

  SupplyChainApiService(this._apiService);

  /// Get supplier categories
  Future<ApiResponse<List<Map<String, dynamic>>>> getCategories() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/supply-chain/categories',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get suppliers
  Future<ApiResponse<List<Map<String, dynamic>>>> getSuppliers({
    String? category,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (category != null) queryParams['category'] = category;

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/supply-chain/suppliers',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get featured suppliers
  Future<ApiResponse<List<Map<String, dynamic>>>> getFeaturedSuppliers() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/supply-chain/suppliers/featured',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Create supply order
  Future<ApiResponse<Map<String, dynamic>>> createOrder({
    required String supplierId,
    required List<Map<String, dynamic>> items,
    required String deliveryAddress,
    String? notes,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/supply-chain/orders',
      body: {
        'supplier_id': supplierId,
        'items': items,
        'delivery_address': deliveryAddress,
        'notes': notes,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get supply orders
  Future<ApiResponse<List<Map<String, dynamic>>>> getOrders({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (status != null) queryParams['status'] = status;

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/supply-chain/orders',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get inventory
  Future<ApiResponse<List<Map<String, dynamic>>>> getInventory() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/supply-chain/inventory',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Update inventory item
  Future<ApiResponse<void>> updateInventoryItem({
    required String itemId,
    required int quantity,
  }) async {
    return await _apiService.put<void>(
      '/supply-chain/inventory/$itemId',
      body: {'quantity': quantity},
    );
  }

  /// Get price comparison data
  Future<ApiResponse<Map<String, dynamic>>> getPriceComparison() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/supply-chain/analytics/price-comparison',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }
}

/// Notification API service
class NotificationApiService {
  final ApiService _apiService;

  NotificationApiService(this._apiService);

  /// Get user notifications
  Future<ApiResponse<List<Map<String, dynamic>>>> getNotifications({
    bool? unreadOnly,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (unreadOnly != null) queryParams['unread_only'] = unreadOnly.toString();

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/notifications',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Mark notification as read
  Future<ApiResponse<void>> markAsRead({
    required String notificationId,
  }) async {
    return await _apiService.put<void>(
      '/notifications/$notificationId/read',
    );
  }

  /// Mark all notifications as read
  Future<ApiResponse<void>> markAllAsRead() async {
    return await _apiService.put<void>(
      '/notifications/mark-all-read',
    );
  }

  /// Update notification preferences
  Future<ApiResponse<void>> updatePreferences({
    required Map<String, bool> preferences,
  }) async {
    return await _apiService.put<void>(
      '/notifications/preferences',
      body: preferences,
    );
  }
}

/// File Upload API service
class FileUploadApiService {
  final ApiService _apiService;

  FileUploadApiService(this._apiService);

  /// Upload single file
  Future<ApiResponse<Map<String, dynamic>>> uploadFile({
    required String filePath,
    required String fileType,
    String? folder,
  }) async {
    // This would typically use multipart/form-data
    // For now, we'll use a simple approach
    return await _apiService.post<Map<String, dynamic>>(
      '/files/upload',
      body: {
        'file_path': filePath,
        'file_type': fileType,
        'folder': folder,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Upload multiple files
  Future<ApiResponse<List<Map<String, dynamic>>>> uploadFiles({
    required List<String> filePaths,
    required String fileType,
    String? folder,
  }) async {
    return await _apiService.post<List<Map<String, dynamic>>>(
      '/files/upload-multiple',
      body: {
        'file_paths': filePaths,
        'file_type': fileType,
        'folder': folder,
      },
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Delete file
  Future<ApiResponse<void>> deleteFile({
    required String fileId,
  }) async {
    return await _apiService.delete<void>('/files/$fileId');
  }
}

/// Wallet API service
class WalletApiService {
  final ApiService _apiService;

  WalletApiService(this._apiService);

  /// Get user wallet
  Future<ApiResponse<Map<String, dynamic>>> getWallet() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/wallet',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get wallet balance
  Future<ApiResponse<Map<String, dynamic>>> getBalance() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/wallet/balance',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Top up wallet
  Future<ApiResponse<Map<String, dynamic>>> topUp({
    required double amount,
    required String paymentMethod,
    String? description,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/wallet/top-up',
      body: {
        'amount': amount,
        'payment_method': paymentMethod,
        'description': description,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Withdraw from wallet
  Future<ApiResponse<Map<String, dynamic>>> withdraw({
    required double amount,
    required String bankAccount,
    String? description,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/wallet/withdraw',
      body: {
        'amount': amount,
        'bank_account': bankAccount,
        'description': description,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Transfer money
  Future<ApiResponse<Map<String, dynamic>>> transfer({
    required String recipientId,
    required double amount,
    String? description,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/wallet/transfer',
      body: {
        'recipient_id': recipientId,
        'amount': amount,
        'description': description,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get transaction history
  Future<ApiResponse<List<Map<String, dynamic>>>> getTransactions({
    int page = 1,
    int limit = 20,
    String? type,
    String? status,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (type != null) queryParams['type'] = type;
    if (status != null) queryParams['status'] = status;
    if (startDate != null) queryParams['start_date'] = startDate.toIso8601String();
    if (endDate != null) queryParams['end_date'] = endDate.toIso8601String();

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/wallet/transactions',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get transaction by ID
  Future<ApiResponse<Map<String, dynamic>>> getTransaction({
    required String transactionId,
  }) async {
    return await _apiService.get<Map<String, dynamic>>(
      '/wallet/transactions/$transactionId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get wallet limits
  Future<ApiResponse<Map<String, dynamic>>> getLimits() async {
    return await _apiService.get<Map<String, dynamic>>(
      '/wallet/limits',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Update wallet limits
  Future<ApiResponse<void>> updateLimits({
    required Map<String, double> limits,
  }) async {
    return await _apiService.put<void>(
      '/wallet/limits',
      body: limits,
    );
  }
}

/// Loan API service
class LoanApiService {
  final ApiService _apiService;

  LoanApiService(this._apiService);

  /// Get user loans
  Future<ApiResponse<List<Map<String, dynamic>>>> getLoans({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (status != null) queryParams['status'] = status;

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/loans',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get loan by ID
  Future<ApiResponse<Map<String, dynamic>>> getLoan({
    required String loanId,
  }) async {
    return await _apiService.get<Map<String, dynamic>>(
      '/loans/$loanId',
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Apply for loan
  Future<ApiResponse<Map<String, dynamic>>> applyForLoan({
    required Map<String, dynamic> application,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/loans/apply',
      body: application,
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get loan applications
  Future<ApiResponse<List<Map<String, dynamic>>>> getApplications({
    String? status,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
    };
    if (status != null) queryParams['status'] = status;

    return await _apiService.get<List<Map<String, dynamic>>>(
      '/loans/applications',
      queryParams: queryParams,
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get loan eligibility
  Future<ApiResponse<Map<String, dynamic>>> checkEligibility({
    required double amount,
    required String type,
    required int termMonths,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/loans/eligibility',
      body: {
        'amount': amount,
        'type': type,
        'term_months': termMonths,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Make loan payment
  Future<ApiResponse<Map<String, dynamic>>> makePayment({
    required String loanId,
    required double amount,
    String? paymentMethod,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/loans/$loanId/payments',
      body: {
        'amount': amount,
        'payment_method': paymentMethod,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get loan payments
  Future<ApiResponse<List<Map<String, dynamic>>>> getPayments({
    required String loanId,
    int page = 1,
    int limit = 20,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/loans/$loanId/payments',
      queryParams: {
        'page': page.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Get loan schedule
  Future<ApiResponse<List<Map<String, dynamic>>>> getPaymentSchedule({
    required String loanId,
  }) async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/loans/$loanId/schedule',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }

  /// Calculate loan terms
  Future<ApiResponse<Map<String, dynamic>>> calculateLoan({
    required double amount,
    required double interestRate,
    required int termMonths,
  }) async {
    return await _apiService.post<Map<String, dynamic>>(
      '/loans/calculate',
      body: {
        'amount': amount,
        'interest_rate': interestRate,
        'term_months': termMonths,
      },
      fromJson: (data) => data as Map<String, dynamic>,
    );
  }

  /// Get loan offers
  Future<ApiResponse<List<Map<String, dynamic>>>> getLoanOffers() async {
    return await _apiService.get<List<Map<String, dynamic>>>(
      '/loans/offers',
      fromJson: (data) => List<Map<String, dynamic>>.from(data as List),
    );
  }
}
