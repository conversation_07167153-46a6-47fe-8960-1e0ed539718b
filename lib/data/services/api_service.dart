import 'dart:convert';
import 'dart:io';
import 'package:http/http.dart' as http;

import '../../core/config/app_config.dart';
import '../../core/constants/app_constants.dart';
import '../models/api_response.dart';
import '../models/tailor_model.dart';

/// Main API service for handling HTTP requests
class ApiService {
  static const String _baseUrl = AppConstants.baseUrl;
  static const Duration _timeout = Duration(seconds: 30);

  final http.Client _client;
  String? _authToken;

  ApiService({http.Client? client}) : _client = client ?? http.Client();

  /// Set authentication token
  void setAuthToken(String token) {
    _authToken = token;
  }

  /// Clear authentication token
  void clearAuthToken() {
    _authToken = null;
  }

  /// Get default headers
  Map<String, String> get _headers {
    final headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    };

    if (_authToken != null) {
      headers['Authorization'] = 'Bearer $_authToken';
    }

    return headers;
  }

  /// Generic GET request
  Future<ApiResponse<T>> get<T>(
    String endpoint, {
    Map<String, dynamic>? queryParams,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final uriWithParams = queryParams != null
          ? uri.replace(queryParameters: queryParams)
          : uri;

      final response = await _client
          .get(uriWithParams, headers: _headers)
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic POST request
  Future<ApiResponse<T>> post<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .post(
            uri,
            headers: _headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic PUT request
  Future<ApiResponse<T>> put<T>(
    String endpoint, {
    Map<String, dynamic>? body,
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .put(
            uri,
            headers: _headers,
            body: body != null ? jsonEncode(body) : null,
          )
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Generic DELETE request
  Future<ApiResponse<T>> delete<T>(
    String endpoint, {
    T Function(dynamic)? fromJson,
  }) async {
    try {
      final uri = Uri.parse('$_baseUrl$endpoint');
      final response = await _client
          .delete(uri, headers: _headers)
          .timeout(_timeout);

      return _handleResponse<T>(response, fromJson);
    } catch (e) {
      return _handleError<T>(e);
    }
  }

  /// Handle HTTP response
  ApiResponse<T> _handleResponse<T>(
    http.Response response,
    T Function(dynamic)? fromJson,
  ) {
    try {
      final jsonData = jsonDecode(response.body) as Map<String, dynamic>;

      if (response.statusCode >= 200 && response.statusCode < 300) {
        return ApiResponse<T>.fromJson(jsonData, fromJson);
      } else {
        return ApiResponse<T>(
          success: false,
          error: ApiError.fromJson(jsonData['error'] as Map<String, dynamic>),
        );
      }
    } catch (e) {
      return ApiResponse<T>(
        success: false,
        error: ApiError(
          code: 'PARSE_ERROR',
          message: 'Failed to parse response: ${e.toString()}',
        ),
      );
    }
  }

  /// Handle errors
  ApiResponse<T> _handleError<T>(dynamic error) {
    String message;
    String code;

    if (error is SocketException) {
      message = 'No internet connection';
      code = 'NO_INTERNET';
    } else if (error is HttpException) {
      message = 'HTTP error: ${error.message}';
      code = 'HTTP_ERROR';
    } else if (error.toString().contains('TimeoutException')) {
      message = 'Request timeout';
      code = 'TIMEOUT';
    } else {
      message = 'An unexpected error occurred: ${error.toString()}';
      code = 'UNKNOWN_ERROR';
    }

    return ApiResponse<T>(
      success: false,
      error: ApiError(code: code, message: message),
    );
  }

  /// Dispose resources
  void dispose() {
    _client.close();
  }
}

/// Tailor-specific API service
class TailorApiService {
  final ApiService _apiService;

  TailorApiService(this._apiService);

  /// Get featured tailors
  Future<ApiResponse<List<TailorModel>>> getFeaturedTailors({
    int limit = 10,
  }) async {
    return await _apiService.get<List<TailorModel>>(
      '/tailors/featured',
      queryParams: {'limit': limit.toString()},
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get nearby tailors
  Future<ApiResponse<List<TailorModel>>> getNearbyTailors({
    required double latitude,
    required double longitude,
    int radius = 10, // km
    int limit = 20,
  }) async {
    return await _apiService.get<List<TailorModel>>(
      '/tailors/nearby',
      queryParams: {
        'latitude': latitude.toString(),
        'longitude': longitude.toString(),
        'radius': radius.toString(),
        'limit': limit.toString(),
      },
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Search tailors
  Future<ApiResponse<List<TailorModel>>> searchTailors({
    required TailorSearchFilters filters,
    int page = 1,
    int limit = 20,
  }) async {
    final queryParams = {
      'page': page.toString(),
      'limit': limit.toString(),
      ...filters.toJson().map((key, value) => MapEntry(key, value.toString())),
    };

    return await _apiService.get<List<TailorModel>>(
      '/tailors/search',
      queryParams: queryParams,
      fromJson: (data) => (data as List)
          .map((item) => TailorModel.fromJson(item as Map<String, dynamic>))
          .toList(),
    );
  }

  /// Get tailor by ID
  Future<ApiResponse<TailorModel>> getTailorById(String id) async {
    return await _apiService.get<TailorModel>(
      '/tailors/$id',
      fromJson: (data) => TailorModel.fromJson(data as Map<String, dynamic>),
    );
  }

  /// Get tailor categories
  Future<ApiResponse<List<String>>> getCategories() async {
    return await _apiService.get<List<String>>(
      '/tailors/categories',
      fromJson: (data) => List<String>.from(data as List),
    );
  }

  /// Get tailor services
  Future<ApiResponse<List<String>>> getServices() async {
    return await _apiService.get<List<String>>(
      '/tailors/services',
      fromJson: (data) => List<String>.from(data as List),
    );
  }
}
