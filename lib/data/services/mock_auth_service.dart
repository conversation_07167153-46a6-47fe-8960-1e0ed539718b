import 'dart:convert';
import 'dart:math';
import 'package:shared_preferences/shared_preferences.dart';

import '../models/api_response.dart';
import '../models/user_model.dart';

/// Mock authentication service for development and testing
class MockAuthService {
  static const Duration _mockDelay = Duration(milliseconds: 1500);
  static const String _tokenKey = 'mock_auth_token';
  static const String _refreshTokenKey = 'mock_refresh_token';
  static const String _userKey = 'mock_user_data';

  /// Mock login
  Future<ApiResponse<AuthResponse>> login({
    required String email,
    required String password,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      // Simulate validation
      if (email.isEmpty || password.isEmpty) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'VALIDATION_ERROR',
            message: 'Email and password are required',
          ),
        );
      }

      if (!email.contains('@')) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'INVALID_EMAIL',
            message: 'Please enter a valid email address',
          ),
        );
      }

      if (password.length < 6) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'WEAK_PASSWORD',
            message: 'Password must be at least 6 characters',
          ),
        );
      }

      // Simulate wrong credentials (for demo purposes)
      if (email == '<EMAIL>') {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'INVALID_CREDENTIALS',
            message: 'Invalid email or password',
          ),
        );
      }

      // Create mock user and auth response
      final user = _createMockUser(email);
      final authResponse = _createMockAuthResponse(user);

      await _saveAuthData(authResponse);

      return ApiResponse<AuthResponse>(
        success: true,
        message: 'Login successful',
        data: authResponse,
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'LOGIN_ERROR',
          message: 'Login failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Mock register
  Future<ApiResponse<AuthResponse>> register({
    required String email,
    required String password,
    required String firstName,
    required String lastName,
    String? phone,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      // Simulate validation
      if (email.isEmpty || password.isEmpty || firstName.isEmpty || lastName.isEmpty) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'VALIDATION_ERROR',
            message: 'All fields are required',
          ),
        );
      }

      if (!email.contains('@')) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'INVALID_EMAIL',
            message: 'Please enter a valid email address',
          ),
        );
      }

      if (password.length < 6) {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'WEAK_PASSWORD',
            message: 'Password must be at least 6 characters',
          ),
        );
      }

      // Simulate email already exists (for demo purposes)
      if (email == '<EMAIL>') {
        return const ApiResponse<AuthResponse>(
          success: false,
          error: ApiError(
            code: 'EMAIL_EXISTS',
            message: 'An account with this email already exists',
          ),
        );
      }

      // Create mock user and auth response
      final user = _createMockUser(email, firstName: firstName, lastName: lastName, phone: phone);
      final authResponse = _createMockAuthResponse(user);

      await _saveAuthData(authResponse);

      return ApiResponse<AuthResponse>(
        success: true,
        message: 'Registration successful',
        data: authResponse,
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'REGISTER_ERROR',
          message: 'Registration failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Mock social login
  Future<ApiResponse<AuthResponse>> socialLogin({
    required String provider,
    required String token,
  }) async {
    await Future.delayed(_mockDelay);

    try {
      // Create mock user based on provider
      final email = '${provider.toLowerCase()}.<EMAIL>';
      final user = _createMockUser(
        email,
        firstName: '${provider} User',
        lastName: 'Demo',
      );
      final authResponse = _createMockAuthResponse(user);

      await _saveAuthData(authResponse);

      return ApiResponse<AuthResponse>(
        success: true,
        message: '$provider login successful',
        data: authResponse,
      );
    } catch (e) {
      return ApiResponse<AuthResponse>(
        success: false,
        error: ApiError(
          code: 'SOCIAL_LOGIN_ERROR',
          message: '$provider login failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Mock logout
  Future<ApiResponse<void>> logout() async {
    await Future.delayed(const Duration(milliseconds: 500));

    try {
      await _clearAuthData();

      return const ApiResponse<void>(
        success: true,
        message: 'Logged out successfully',
      );
    } catch (e) {
      return ApiResponse<void>(
        success: false,
        error: ApiError(
          code: 'LOGOUT_ERROR',
          message: 'Logout failed: ${e.toString()}',
        ),
      );
    }
  }

  /// Mock get current user
  Future<ApiResponse<UserModel>> getCurrentUser() async {
    await Future.delayed(const Duration(milliseconds: 800));

    try {
      final user = await getStoredUser();
      if (user != null) {
        return ApiResponse<UserModel>(
          success: true,
          message: 'User retrieved successfully',
          data: user,
        );
      } else {
        return const ApiResponse<UserModel>(
          success: false,
          error: ApiError(
            code: 'USER_NOT_FOUND',
            message: 'User not found',
          ),
        );
      }
    } catch (e) {
      return ApiResponse<UserModel>(
        success: false,
        error: ApiError(
          code: 'GET_USER_ERROR',
          message: 'Failed to get user: ${e.toString()}',
        ),
      );
    }
  }

  /// Check if user is logged in
  Future<bool> isLoggedIn() async {
    final token = await _getAccessToken();
    return token != null;
  }

  /// Get stored user data
  Future<UserModel?> getStoredUser() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final userJson = prefs.getString(_userKey);
      if (userJson != null) {
        final userData = jsonDecode(userJson) as Map<String, dynamic>;
        return UserModel.fromJson(userData);
      }
    } catch (e) {
      // Handle error silently
    }
    return null;
  }

  /// Initialize mock auth service
  Future<void> initialize() async {
    // Mock initialization - nothing to do
  }

  /// Create mock user
  UserModel _createMockUser(
    String email, {
    String? firstName,
    String? lastName,
    String? phone,
  }) {
    final random = Random();
    final now = DateTime.now();

    return UserModel(
      id: 'user_${random.nextInt(999999)}',
      email: email,
      firstName: firstName ?? email.split('@').first,
      lastName: lastName ?? 'User',
      phone: phone,
      profileImageUrl: _getRandomAvatarUrl(),
      role: UserRole.customer,
      isEmailVerified: true,
      isPhoneVerified: phone != null,
      createdAt: now.subtract(Duration(days: random.nextInt(365))),
      updatedAt: now,
      preferences: const UserPreferences(
        notificationsEnabled: true,
        emailNotifications: true,
        pushNotifications: true,
        language: 'en',
        currency: 'USD',
        darkMode: false,
      ),
    );
  }

  /// Create mock auth response
  AuthResponse _createMockAuthResponse(UserModel user) {
    final random = Random();
    final now = DateTime.now();

    return AuthResponse(
      user: user,
      accessToken: 'mock_access_token_${random.nextInt(999999)}',
      refreshToken: 'mock_refresh_token_${random.nextInt(999999)}',
      expiresAt: now.add(const Duration(hours: 24)),
    );
  }

  /// Get random avatar URL
  String _getRandomAvatarUrl() {
    final random = Random();
    final seed = random.nextInt(1000);
    return 'https://api.dicebear.com/7.x/avataaars/png?seed=$seed&size=200';
  }

  /// Save authentication data to local storage
  Future<void> _saveAuthData(AuthResponse authResponse) async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.setString(_tokenKey, authResponse.accessToken);
    await prefs.setString(_refreshTokenKey, authResponse.refreshToken);
    await prefs.setString(_userKey, jsonEncode(authResponse.user.toJson()));
  }

  /// Clear authentication data from local storage
  Future<void> _clearAuthData() async {
    final prefs = await SharedPreferences.getInstance();
    await prefs.remove(_tokenKey);
    await prefs.remove(_refreshTokenKey);
    await prefs.remove(_userKey);
  }

  /// Get access token from local storage
  Future<String?> _getAccessToken() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getString(_tokenKey);
  }
}
