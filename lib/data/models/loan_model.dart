import 'package:equatable/equatable.dart';

/// Loan model for financial services
class LoanModel extends Equatable {
  final String id;
  final String userId;
  final LoanType type;
  final double amount;
  final double interestRate;
  final int termMonths;
  final LoanStatus status;
  final DateTime applicationDate;
  final DateTime? approvalDate;
  final DateTime? disbursementDate;
  final DateTime? dueDate;
  final double monthlyPayment;
  final double totalInterest;
  final double remainingBalance;
  final int remainingPayments;
  final List<LoanPaymentModel> payments;
  final LoanPurpose purpose;
  final String? collateral;
  final Map<String, dynamic>? documents;

  const LoanModel({
    required this.id,
    required this.userId,
    required this.type,
    required this.amount,
    required this.interestRate,
    required this.termMonths,
    required this.status,
    required this.applicationDate,
    this.approvalDate,
    this.disbursementDate,
    this.dueDate,
    required this.monthlyPayment,
    required this.totalInterest,
    required this.remainingBalance,
    required this.remainingPayments,
    required this.payments,
    required this.purpose,
    this.collateral,
    this.documents,
  });

  factory LoanModel.fromJson(Map<String, dynamic> json) {
    return LoanModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      type: LoanType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => LoanType.personal,
      ),
      amount: (json['amount'] as num).toDouble(),
      interestRate: (json['interest_rate'] as num).toDouble(),
      termMonths: json['term_months'] as int,
      status: LoanStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => LoanStatus.pending,
      ),
      applicationDate: DateTime.parse(json['application_date'] as String),
      approvalDate: json['approval_date'] != null
          ? DateTime.parse(json['approval_date'] as String)
          : null,
      disbursementDate: json['disbursement_date'] != null
          ? DateTime.parse(json['disbursement_date'] as String)
          : null,
      dueDate: json['due_date'] != null
          ? DateTime.parse(json['due_date'] as String)
          : null,
      monthlyPayment: (json['monthly_payment'] as num).toDouble(),
      totalInterest: (json['total_interest'] as num).toDouble(),
      remainingBalance: (json['remaining_balance'] as num).toDouble(),
      remainingPayments: json['remaining_payments'] as int,
      payments: (json['payments'] as List<dynamic>?)
              ?.map((e) => LoanPaymentModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      purpose: LoanPurpose.values.firstWhere(
        (e) => e.name == json['purpose'],
        orElse: () => LoanPurpose.business,
      ),
      collateral: json['collateral'] as String?,
      documents: json['documents'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.name,
      'amount': amount,
      'interest_rate': interestRate,
      'term_months': termMonths,
      'status': status.name,
      'application_date': applicationDate.toIso8601String(),
      'approval_date': approvalDate?.toIso8601String(),
      'disbursement_date': disbursementDate?.toIso8601String(),
      'due_date': dueDate?.toIso8601String(),
      'monthly_payment': monthlyPayment,
      'total_interest': totalInterest,
      'remaining_balance': remainingBalance,
      'remaining_payments': remainingPayments,
      'payments': payments.map((e) => e.toJson()).toList(),
      'purpose': purpose.name,
      'collateral': collateral,
      'documents': documents,
    };
  }

  LoanModel copyWith({
    String? id,
    String? userId,
    LoanType? type,
    double? amount,
    double? interestRate,
    int? termMonths,
    LoanStatus? status,
    DateTime? applicationDate,
    DateTime? approvalDate,
    DateTime? disbursementDate,
    DateTime? dueDate,
    double? monthlyPayment,
    double? totalInterest,
    double? remainingBalance,
    int? remainingPayments,
    List<LoanPaymentModel>? payments,
    LoanPurpose? purpose,
    String? collateral,
    Map<String, dynamic>? documents,
  }) {
    return LoanModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      amount: amount ?? this.amount,
      interestRate: interestRate ?? this.interestRate,
      termMonths: termMonths ?? this.termMonths,
      status: status ?? this.status,
      applicationDate: applicationDate ?? this.applicationDate,
      approvalDate: approvalDate ?? this.approvalDate,
      disbursementDate: disbursementDate ?? this.disbursementDate,
      dueDate: dueDate ?? this.dueDate,
      monthlyPayment: monthlyPayment ?? this.monthlyPayment,
      totalInterest: totalInterest ?? this.totalInterest,
      remainingBalance: remainingBalance ?? this.remainingBalance,
      remainingPayments: remainingPayments ?? this.remainingPayments,
      payments: payments ?? this.payments,
      purpose: purpose ?? this.purpose,
      collateral: collateral ?? this.collateral,
      documents: documents ?? this.documents,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        amount,
        interestRate,
        termMonths,
        status,
        applicationDate,
        approvalDate,
        disbursementDate,
        dueDate,
        monthlyPayment,
        totalInterest,
        remainingBalance,
        remainingPayments,
        payments,
        purpose,
        collateral,
        documents,
      ];
}

/// Loan payment model
class LoanPaymentModel extends Equatable {
  final String id;
  final String loanId;
  final double amount;
  final double principalAmount;
  final double interestAmount;
  final DateTime dueDate;
  final DateTime? paidDate;
  final PaymentStatus status;
  final String? transactionId;

  const LoanPaymentModel({
    required this.id,
    required this.loanId,
    required this.amount,
    required this.principalAmount,
    required this.interestAmount,
    required this.dueDate,
    this.paidDate,
    required this.status,
    this.transactionId,
  });

  factory LoanPaymentModel.fromJson(Map<String, dynamic> json) {
    return LoanPaymentModel(
      id: json['id'] as String,
      loanId: json['loan_id'] as String,
      amount: (json['amount'] as num).toDouble(),
      principalAmount: (json['principal_amount'] as num).toDouble(),
      interestAmount: (json['interest_amount'] as num).toDouble(),
      dueDate: DateTime.parse(json['due_date'] as String),
      paidDate: json['paid_date'] != null
          ? DateTime.parse(json['paid_date'] as String)
          : null,
      status: PaymentStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => PaymentStatus.pending,
      ),
      transactionId: json['transaction_id'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'loan_id': loanId,
      'amount': amount,
      'principal_amount': principalAmount,
      'interest_amount': interestAmount,
      'due_date': dueDate.toIso8601String(),
      'paid_date': paidDate?.toIso8601String(),
      'status': status.name,
      'transaction_id': transactionId,
    };
  }

  @override
  List<Object?> get props => [
        id,
        loanId,
        amount,
        principalAmount,
        interestAmount,
        dueDate,
        paidDate,
        status,
        transactionId,
      ];
}

/// Loan application model
class LoanApplicationModel extends Equatable {
  final String? id;
  final String userId;
  final LoanType type;
  final double requestedAmount;
  final int termMonths;
  final LoanPurpose purpose;
  final String businessDescription;
  final double monthlyIncome;
  final double monthlyExpenses;
  final String? collateral;
  final List<String> documents;
  final ApplicationStatus status;
  final DateTime createdAt;
  final String? rejectionReason;

  const LoanApplicationModel({
    this.id,
    required this.userId,
    required this.type,
    required this.requestedAmount,
    required this.termMonths,
    required this.purpose,
    required this.businessDescription,
    required this.monthlyIncome,
    required this.monthlyExpenses,
    this.collateral,
    required this.documents,
    required this.status,
    required this.createdAt,
    this.rejectionReason,
  });

  factory LoanApplicationModel.fromJson(Map<String, dynamic> json) {
    return LoanApplicationModel(
      id: json['id'] as String?,
      userId: json['user_id'] as String,
      type: LoanType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => LoanType.personal,
      ),
      requestedAmount: (json['requested_amount'] as num).toDouble(),
      termMonths: json['term_months'] as int,
      purpose: LoanPurpose.values.firstWhere(
        (e) => e.name == json['purpose'],
        orElse: () => LoanPurpose.business,
      ),
      businessDescription: json['business_description'] as String,
      monthlyIncome: (json['monthly_income'] as num).toDouble(),
      monthlyExpenses: (json['monthly_expenses'] as num).toDouble(),
      collateral: json['collateral'] as String?,
      documents: List<String>.from(json['documents'] as List? ?? []),
      status: ApplicationStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => ApplicationStatus.draft,
      ),
      createdAt: DateTime.parse(json['created_at'] as String),
      rejectionReason: json['rejection_reason'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'type': type.name,
      'requested_amount': requestedAmount,
      'term_months': termMonths,
      'purpose': purpose.name,
      'business_description': businessDescription,
      'monthly_income': monthlyIncome,
      'monthly_expenses': monthlyExpenses,
      'collateral': collateral,
      'documents': documents,
      'status': status.name,
      'created_at': createdAt.toIso8601String(),
      'rejection_reason': rejectionReason,
    };
  }

  LoanApplicationModel copyWith({
    String? id,
    String? userId,
    LoanType? type,
    double? requestedAmount,
    int? termMonths,
    LoanPurpose? purpose,
    String? businessDescription,
    double? monthlyIncome,
    double? monthlyExpenses,
    String? collateral,
    List<String>? documents,
    ApplicationStatus? status,
    DateTime? createdAt,
    String? rejectionReason,
  }) {
    return LoanApplicationModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      type: type ?? this.type,
      requestedAmount: requestedAmount ?? this.requestedAmount,
      termMonths: termMonths ?? this.termMonths,
      purpose: purpose ?? this.purpose,
      businessDescription: businessDescription ?? this.businessDescription,
      monthlyIncome: monthlyIncome ?? this.monthlyIncome,
      monthlyExpenses: monthlyExpenses ?? this.monthlyExpenses,
      collateral: collateral ?? this.collateral,
      documents: documents ?? this.documents,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      rejectionReason: rejectionReason ?? this.rejectionReason,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        type,
        requestedAmount,
        termMonths,
        purpose,
        businessDescription,
        monthlyIncome,
        monthlyExpenses,
        collateral,
        documents,
        status,
        createdAt,
        rejectionReason,
      ];
}

/// Loan types
enum LoanType {
  personal,
  business,
  equipment,
  inventory,
  emergency,
}

/// Loan status
enum LoanStatus {
  pending,
  approved,
  rejected,
  disbursed,
  active,
  completed,
  defaulted,
  cancelled,
}

/// Payment status
enum PaymentStatus {
  pending,
  paid,
  overdue,
  partial,
  failed,
}

/// Loan purpose
enum LoanPurpose {
  business,
  equipment,
  inventory,
  expansion,
  emergency,
  education,
  personal,
}

/// Application status
enum ApplicationStatus {
  draft,
  submitted,
  underReview,
  approved,
  rejected,
  cancelled,
}
