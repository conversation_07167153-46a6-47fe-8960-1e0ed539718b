import 'package:equatable/equatable.dart';

/// Wallet model for financial management
class WalletModel extends Equatable {
  final String id;
  final String userId;
  final double balance;
  final String currency;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final List<TransactionModel> recentTransactions;
  final WalletLimits limits;

  const WalletModel({
    required this.id,
    required this.userId,
    required this.balance,
    required this.currency,
    required this.isActive,
    required this.createdAt,
    required this.updatedAt,
    required this.recentTransactions,
    required this.limits,
  });

  factory WalletModel.fromJson(Map<String, dynamic> json) {
    return WalletModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      balance: (json['balance'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'MNT',
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
      recentTransactions: (json['recent_transactions'] as List<dynamic>?)
              ?.map((e) => TransactionModel.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      limits: WalletLimits.fromJson(json['limits'] as Map<String, dynamic>? ?? {}),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'balance': balance,
      'currency': currency,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
      'recent_transactions': recentTransactions.map((e) => e.toJson()).toList(),
      'limits': limits.toJson(),
    };
  }

  WalletModel copyWith({
    String? id,
    String? userId,
    double? balance,
    String? currency,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    List<TransactionModel>? recentTransactions,
    WalletLimits? limits,
  }) {
    return WalletModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      balance: balance ?? this.balance,
      currency: currency ?? this.currency,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      recentTransactions: recentTransactions ?? this.recentTransactions,
      limits: limits ?? this.limits,
    );
  }

  @override
  List<Object?> get props => [
        id,
        userId,
        balance,
        currency,
        isActive,
        createdAt,
        updatedAt,
        recentTransactions,
        limits,
      ];
}

/// Transaction model for wallet transactions
class TransactionModel extends Equatable {
  final String id;
  final String walletId;
  final TransactionType type;
  final double amount;
  final String currency;
  final String description;
  final TransactionStatus status;
  final String? referenceId;
  final String? orderId;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const TransactionModel({
    required this.id,
    required this.walletId,
    required this.type,
    required this.amount,
    required this.currency,
    required this.description,
    required this.status,
    this.referenceId,
    this.orderId,
    required this.createdAt,
    this.metadata,
  });

  factory TransactionModel.fromJson(Map<String, dynamic> json) {
    return TransactionModel(
      id: json['id'] as String,
      walletId: json['wallet_id'] as String,
      type: TransactionType.values.firstWhere(
        (e) => e.name == json['type'],
        orElse: () => TransactionType.payment,
      ),
      amount: (json['amount'] as num).toDouble(),
      currency: json['currency'] as String? ?? 'MNT',
      description: json['description'] as String,
      status: TransactionStatus.values.firstWhere(
        (e) => e.name == json['status'],
        orElse: () => TransactionStatus.pending,
      ),
      referenceId: json['reference_id'] as String?,
      orderId: json['order_id'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      metadata: json['metadata'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'wallet_id': walletId,
      'type': type.name,
      'amount': amount,
      'currency': currency,
      'description': description,
      'status': status.name,
      'reference_id': referenceId,
      'order_id': orderId,
      'created_at': createdAt.toIso8601String(),
      'metadata': metadata,
    };
  }

  @override
  List<Object?> get props => [
        id,
        walletId,
        type,
        amount,
        currency,
        description,
        status,
        referenceId,
        orderId,
        createdAt,
        metadata,
      ];
}

/// Wallet limits and restrictions
class WalletLimits extends Equatable {
  final double dailyLimit;
  final double monthlyLimit;
  final double maxBalance;
  final double minTopUp;
  final double maxTopUp;

  const WalletLimits({
    required this.dailyLimit,
    required this.monthlyLimit,
    required this.maxBalance,
    required this.minTopUp,
    required this.maxTopUp,
  });

  factory WalletLimits.fromJson(Map<String, dynamic> json) {
    return WalletLimits(
      dailyLimit: (json['daily_limit'] as num?)?.toDouble() ?? 1000000.0,
      monthlyLimit: (json['monthly_limit'] as num?)?.toDouble() ?? 10000000.0,
      maxBalance: (json['max_balance'] as num?)?.toDouble() ?? 50000000.0,
      minTopUp: (json['min_top_up'] as num?)?.toDouble() ?? 10000.0,
      maxTopUp: (json['max_top_up'] as num?)?.toDouble() ?? 5000000.0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'daily_limit': dailyLimit,
      'monthly_limit': monthlyLimit,
      'max_balance': maxBalance,
      'min_top_up': minTopUp,
      'max_top_up': maxTopUp,
    };
  }

  @override
  List<Object?> get props => [
        dailyLimit,
        monthlyLimit,
        maxBalance,
        minTopUp,
        maxTopUp,
      ];
}

/// Transaction types
enum TransactionType {
  topUp,
  payment,
  refund,
  withdrawal,
  transfer,
  loanDisbursement,
  loanRepayment,
  fee,
  bonus,
}

/// Transaction status
enum TransactionStatus {
  pending,
  completed,
  failed,
  cancelled,
  processing,
}
