import 'package:equatable/equatable.dart';

import '../../presentation/order/bloc/order_creation_bloc.dart';

/// Order status enumeration
enum OrderStatus {
  pending,
  confirmed,
  inProgress,
  readyForFitting,
  completed,
  cancelled,
  refunded,
}

/// Order model for tailoring services
class OrderModel extends Equatable {
  final String id;
  final String tailorId;
  final String customerId;
  final ServiceModel service;
  final Map<String, double> measurements;
  final FabricModel? fabric;
  final DeliveryOption deliveryOption;
  final String? specialInstructions;
  final double totalPrice;
  final OrderStatus status;
  final DateTime createdAt;
  final DateTime? updatedAt;
  final DateTime estimatedDelivery;
  final DateTime? actualDelivery;
  final List<OrderUpdate> updates;
  final PaymentInfo? paymentInfo;

  const OrderModel({
    required this.id,
    required this.tailorId,
    required this.customerId,
    required this.service,
    required this.measurements,
    this.fabric,
    required this.deliveryOption,
    this.specialInstructions,
    required this.totalPrice,
    required this.status,
    required this.createdAt,
    this.updatedAt,
    required this.estimatedDelivery,
    this.actualDelivery,
    this.updates = const [],
    this.paymentInfo,
  });

  @override
  List<Object?> get props => [
        id,
        tailorId,
        customerId,
        service,
        measurements,
        fabric,
        deliveryOption,
        specialInstructions,
        totalPrice,
        status,
        createdAt,
        updatedAt,
        estimatedDelivery,
        actualDelivery,
        updates,
        paymentInfo,
      ];

  OrderModel copyWith({
    String? id,
    String? tailorId,
    String? customerId,
    ServiceModel? service,
    Map<String, double>? measurements,
    FabricModel? fabric,
    DeliveryOption? deliveryOption,
    String? specialInstructions,
    double? totalPrice,
    OrderStatus? status,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? estimatedDelivery,
    DateTime? actualDelivery,
    List<OrderUpdate>? updates,
    PaymentInfo? paymentInfo,
  }) {
    return OrderModel(
      id: id ?? this.id,
      tailorId: tailorId ?? this.tailorId,
      customerId: customerId ?? this.customerId,
      service: service ?? this.service,
      measurements: measurements ?? this.measurements,
      fabric: fabric ?? this.fabric,
      deliveryOption: deliveryOption ?? this.deliveryOption,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      estimatedDelivery: estimatedDelivery ?? this.estimatedDelivery,
      actualDelivery: actualDelivery ?? this.actualDelivery,
      updates: updates ?? this.updates,
      paymentInfo: paymentInfo ?? this.paymentInfo,
    );
  }

  /// Get status display text
  String get statusDisplayText {
    switch (status) {
      case OrderStatus.pending:
        return 'Pending Confirmation';
      case OrderStatus.confirmed:
        return 'Confirmed';
      case OrderStatus.inProgress:
        return 'In Progress';
      case OrderStatus.readyForFitting:
        return 'Ready for Fitting';
      case OrderStatus.completed:
        return 'Completed';
      case OrderStatus.cancelled:
        return 'Cancelled';
      case OrderStatus.refunded:
        return 'Refunded';
    }
  }

  /// Get status color
  String get statusColor {
    switch (status) {
      case OrderStatus.pending:
        return '#FFA726'; // Orange
      case OrderStatus.confirmed:
        return '#42A5F5'; // Blue
      case OrderStatus.inProgress:
        return '#AB47BC'; // Purple
      case OrderStatus.readyForFitting:
        return '#26C6DA'; // Cyan
      case OrderStatus.completed:
        return '#66BB6A'; // Green
      case OrderStatus.cancelled:
        return '#EF5350'; // Red
      case OrderStatus.refunded:
        return '#78909C'; // Blue Grey
    }
  }

  /// Check if order can be cancelled
  bool get canBeCancelled {
    return status == OrderStatus.pending || status == OrderStatus.confirmed;
  }

  /// Check if order is active
  bool get isActive {
    return status != OrderStatus.completed &&
           status != OrderStatus.cancelled &&
           status != OrderStatus.refunded;
  }

  /// Get progress percentage
  double get progressPercentage {
    switch (status) {
      case OrderStatus.pending:
        return 0.1;
      case OrderStatus.confirmed:
        return 0.3;
      case OrderStatus.inProgress:
        return 0.6;
      case OrderStatus.readyForFitting:
        return 0.8;
      case OrderStatus.completed:
        return 1.0;
      case OrderStatus.cancelled:
      case OrderStatus.refunded:
        return 0.0;
    }
  }

  /// Convert to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'tailorId': tailorId,
      'customerId': customerId,
      'service': {
        'id': service.id,
        'name': service.name,
        'description': service.description,
        'basePrice': service.basePrice,
        'estimatedDays': service.estimatedDays,
        'category': service.category,
      },
      'measurements': measurements,
      'fabric': fabric != null
          ? {
              'id': fabric!.id,
              'name': fabric!.name,
              'description': fabric!.description,
              'additionalCost': fabric!.additionalCost,
              'imageUrl': fabric!.imageUrl,
            }
          : null,
      'deliveryOption': {
        'id': deliveryOption.id,
        'name': deliveryOption.name,
        'description': deliveryOption.description,
        'estimatedDays': deliveryOption.estimatedDays,
        'cost': deliveryOption.cost,
      },
      'specialInstructions': specialInstructions,
      'totalPrice': totalPrice,
      'status': status.toString().split('.').last,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt?.toIso8601String(),
      'estimatedDelivery': estimatedDelivery.toIso8601String(),
      'actualDelivery': actualDelivery?.toIso8601String(),
      'updates': updates.map((update) => update.toJson()).toList(),
      'paymentInfo': paymentInfo?.toJson(),
    };
  }

  /// Create from JSON
  factory OrderModel.fromJson(Map<String, dynamic> json) {
    return OrderModel(
      id: json['id'],
      tailorId: json['tailorId'],
      customerId: json['customerId'],
      service: ServiceModel(
        id: json['service']['id'],
        name: json['service']['name'],
        description: json['service']['description'],
        basePrice: json['service']['basePrice'].toDouble(),
        estimatedDays: json['service']['estimatedDays'],
        category: json['service']['category'],
      ),
      measurements: Map<String, double>.from(json['measurements']),
      fabric: json['fabric'] != null
          ? FabricModel(
              id: json['fabric']['id'],
              name: json['fabric']['name'],
              description: json['fabric']['description'],
              additionalCost: json['fabric']['additionalCost'].toDouble(),
              imageUrl: json['fabric']['imageUrl'],
            )
          : null,
      deliveryOption: DeliveryOption(
        id: json['deliveryOption']['id'],
        name: json['deliveryOption']['name'],
        description: json['deliveryOption']['description'],
        estimatedDays: json['deliveryOption']['estimatedDays'],
        cost: json['deliveryOption']['cost'].toDouble(),
      ),
      specialInstructions: json['specialInstructions'],
      totalPrice: json['totalPrice'].toDouble(),
      status: OrderStatus.values.firstWhere(
        (e) => e.toString().split('.').last == json['status'],
      ),
      createdAt: DateTime.parse(json['createdAt']),
      updatedAt: json['updatedAt'] != null ? DateTime.parse(json['updatedAt']) : null,
      estimatedDelivery: DateTime.parse(json['estimatedDelivery']),
      actualDelivery: json['actualDelivery'] != null ? DateTime.parse(json['actualDelivery']) : null,
      updates: (json['updates'] as List?)?.map((update) => OrderUpdate.fromJson(update)).toList() ?? [],
      paymentInfo: json['paymentInfo'] != null ? PaymentInfo.fromJson(json['paymentInfo']) : null,
    );
  }
}

/// Order update/timeline entry
class OrderUpdate extends Equatable {
  final String id;
  final String title;
  final String description;
  final DateTime timestamp;
  final String? imageUrl;

  const OrderUpdate({
    required this.id,
    required this.title,
    required this.description,
    required this.timestamp,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [id, title, description, timestamp, imageUrl];

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'timestamp': timestamp.toIso8601String(),
      'imageUrl': imageUrl,
    };
  }

  factory OrderUpdate.fromJson(Map<String, dynamic> json) {
    return OrderUpdate(
      id: json['id'],
      title: json['title'],
      description: json['description'],
      timestamp: DateTime.parse(json['timestamp']),
      imageUrl: json['imageUrl'],
    );
  }
}

/// Payment information
class PaymentInfo extends Equatable {
  final String method;
  final String status;
  final double amount;
  final DateTime? paidAt;
  final String? transactionId;

  const PaymentInfo({
    required this.method,
    required this.status,
    required this.amount,
    this.paidAt,
    this.transactionId,
  });

  @override
  List<Object?> get props => [method, status, amount, paidAt, transactionId];

  Map<String, dynamic> toJson() {
    return {
      'method': method,
      'status': status,
      'amount': amount,
      'paidAt': paidAt?.toIso8601String(),
      'transactionId': transactionId,
    };
  }

  factory PaymentInfo.fromJson(Map<String, dynamic> json) {
    return PaymentInfo(
      method: json['method'],
      status: json['status'],
      amount: json['amount'].toDouble(),
      paidAt: json['paidAt'] != null ? DateTime.parse(json['paidAt']) : null,
      transactionId: json['transactionId'],
    );
  }
}
