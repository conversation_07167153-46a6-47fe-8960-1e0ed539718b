import 'package:equatable/equatable.dart';

/// Generic API response wrapper
class ApiResponse<T> extends Equatable {
  final bool success;
  final String? message;
  final T? data;
  final ApiError? error;
  final ApiMeta? meta;

  const ApiResponse({
    required this.success,
    this.message,
    this.data,
    this.error,
    this.meta,
  });

  factory ApiResponse.fromJson(
    Map<String, dynamic> json,
    T Function(dynamic)? fromJsonT,
  ) {
    return ApiResponse<T>(
      success: json['success'] as bool,
      message: json['message'] as String?,
      data: json['data'] != null && fromJsonT != null
          ? fromJsonT(json['data'])
          : json['data'] as T?,
      error: json['error'] != null
          ? ApiError.fromJson(json['error'] as Map<String, dynamic>)
          : null,
      meta: json['meta'] != null
          ? ApiMeta.fromJson(json['meta'] as Map<String, dynamic>)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
      'error': error?.toJson(),
      'meta': meta?.toJson(),
    };
  }

  @override
  List<Object?> get props => [success, message, data, error, meta];
}

/// API error model
class ApiError extends Equatable {
  final String code;
  final String message;
  final Map<String, dynamic>? details;

  const ApiError({
    required this.code,
    required this.message,
    this.details,
  });

  factory ApiError.fromJson(Map<String, dynamic> json) {
    return ApiError(
      code: json['code'] as String,
      message: json['message'] as String,
      details: json['details'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'code': code,
      'message': message,
      'details': details,
    };
  }

  @override
  List<Object?> get props => [code, message, details];
}

/// API metadata for pagination and other info
class ApiMeta extends Equatable {
  final int? currentPage;
  final int? totalPages;
  final int? totalItems;
  final int? itemsPerPage;
  final bool? hasNextPage;
  final bool? hasPreviousPage;

  const ApiMeta({
    this.currentPage,
    this.totalPages,
    this.totalItems,
    this.itemsPerPage,
    this.hasNextPage,
    this.hasPreviousPage,
  });

  factory ApiMeta.fromJson(Map<String, dynamic> json) {
    return ApiMeta(
      currentPage: json['current_page'] as int?,
      totalPages: json['total_pages'] as int?,
      totalItems: json['total_items'] as int?,
      itemsPerPage: json['items_per_page'] as int?,
      hasNextPage: json['has_next_page'] as bool?,
      hasPreviousPage: json['has_previous_page'] as bool?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'current_page': currentPage,
      'total_pages': totalPages,
      'total_items': totalItems,
      'items_per_page': itemsPerPage,
      'has_next_page': hasNextPage,
      'has_previous_page': hasPreviousPage,
    };
  }

  @override
  List<Object?> get props => [
        currentPage,
        totalPages,
        totalItems,
        itemsPerPage,
        hasNextPage,
        hasPreviousPage,
      ];
}

/// Tailor search filters
class TailorSearchFilters extends Equatable {
  final String? query;
  final List<String>? categories;
  final List<String>? services;
  final double? minRating;
  final int? maxDistance; // in km
  final List<int>? priceRanges; // 1-4
  final bool? isVerifiedOnly;
  final bool? isOnlineOnly;
  final double? latitude;
  final double? longitude;
  final String? sortBy; // rating, distance, price, name
  final String? sortOrder; // asc, desc

  const TailorSearchFilters({
    this.query,
    this.categories,
    this.services,
    this.minRating,
    this.maxDistance,
    this.priceRanges,
    this.isVerifiedOnly,
    this.isOnlineOnly,
    this.latitude,
    this.longitude,
    this.sortBy,
    this.sortOrder,
  });

  factory TailorSearchFilters.fromJson(Map<String, dynamic> json) {
    return TailorSearchFilters(
      query: json['query'] as String?,
      categories: json['categories'] != null
          ? List<String>.from(json['categories'] as List)
          : null,
      services: json['services'] != null
          ? List<String>.from(json['services'] as List)
          : null,
      minRating: json['min_rating'] != null
          ? (json['min_rating'] as num).toDouble()
          : null,
      maxDistance: json['max_distance'] as int?,
      priceRanges: json['price_ranges'] != null
          ? List<int>.from(json['price_ranges'] as List)
          : null,
      isVerifiedOnly: json['is_verified_only'] as bool?,
      isOnlineOnly: json['is_online_only'] as bool?,
      latitude: json['latitude'] != null
          ? (json['latitude'] as num).toDouble()
          : null,
      longitude: json['longitude'] != null
          ? (json['longitude'] as num).toDouble()
          : null,
      sortBy: json['sort_by'] as String?,
      sortOrder: json['sort_order'] as String?,
    );
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    
    if (query != null) json['query'] = query;
    if (categories != null) json['categories'] = categories;
    if (services != null) json['services'] = services;
    if (minRating != null) json['min_rating'] = minRating;
    if (maxDistance != null) json['max_distance'] = maxDistance;
    if (priceRanges != null) json['price_ranges'] = priceRanges;
    if (isVerifiedOnly != null) json['is_verified_only'] = isVerifiedOnly;
    if (isOnlineOnly != null) json['is_online_only'] = isOnlineOnly;
    if (latitude != null) json['latitude'] = latitude;
    if (longitude != null) json['longitude'] = longitude;
    if (sortBy != null) json['sort_by'] = sortBy;
    if (sortOrder != null) json['sort_order'] = sortOrder;
    
    return json;
  }

  TailorSearchFilters copyWith({
    String? query,
    List<String>? categories,
    List<String>? services,
    double? minRating,
    int? maxDistance,
    List<int>? priceRanges,
    bool? isVerifiedOnly,
    bool? isOnlineOnly,
    double? latitude,
    double? longitude,
    String? sortBy,
    String? sortOrder,
  }) {
    return TailorSearchFilters(
      query: query ?? this.query,
      categories: categories ?? this.categories,
      services: services ?? this.services,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      priceRanges: priceRanges ?? this.priceRanges,
      isVerifiedOnly: isVerifiedOnly ?? this.isVerifiedOnly,
      isOnlineOnly: isOnlineOnly ?? this.isOnlineOnly,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      sortBy: sortBy ?? this.sortBy,
      sortOrder: sortOrder ?? this.sortOrder,
    );
  }

  @override
  List<Object?> get props => [
        query,
        categories,
        services,
        minRating,
        maxDistance,
        priceRanges,
        isVerifiedOnly,
        isOnlineOnly,
        latitude,
        longitude,
        sortBy,
        sortOrder,
      ];
}
