import 'package:equatable/equatable.dart';
import 'package:flutter/material.dart';

/// Tailor model for API responses
class TailorModel extends Equatable {
  final String id;
  final String name;
  final String email;
  final String? phone;
  final String specialty;
  final double rating;
  final int reviewCount;
  final String? imageUrl;
  final bool isVerified;
  final bool isOnline;
  final String? bio;
  final TailorLocation? location;
  final List<String> services;
  final List<String> categories;
  final PriceRange priceRange;
  final WorkingHours? workingHours;
  final DateTime createdAt;
  final DateTime updatedAt;

  const TailorModel({
    required this.id,
    required this.name,
    required this.email,
    this.phone,
    required this.specialty,
    required this.rating,
    required this.reviewCount,
    this.imageUrl,
    required this.isVerified,
    required this.isOnline,
    this.bio,
    this.location,
    required this.services,
    required this.categories,
    required this.priceRange,
    this.workingHours,
    required this.createdAt,
    required this.updatedAt,
  });

  factory TailorModel.fromJson(Map<String, dynamic> json) {
    return TailorModel(
      id: json['id'] as String,
      name: json['name'] as String,
      email: json['email'] as String,
      phone: json['phone'] as String?,
      specialty: json['specialty'] as String,
      rating: (json['rating'] as num).toDouble(),
      reviewCount: json['review_count'] as int,
      imageUrl: json['image_url'] as String?,
      isVerified: json['is_verified'] as bool,
      isOnline: json['is_online'] as bool,
      bio: json['bio'] as String?,
      location: json['location'] != null
          ? TailorLocation.fromJson(json['location'] as Map<String, dynamic>)
          : null,
      services: List<String>.from(json['services'] as List),
      categories: List<String>.from(json['categories'] as List),
      priceRange: PriceRange.fromJson(json['price_range'] as Map<String, dynamic>),
      workingHours: json['working_hours'] != null
          ? WorkingHours.fromJson(json['working_hours'] as Map<String, dynamic>)
          : null,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': name,
      'email': email,
      'phone': phone,
      'specialty': specialty,
      'rating': rating,
      'review_count': reviewCount,
      'image_url': imageUrl,
      'is_verified': isVerified,
      'is_online': isOnline,
      'bio': bio,
      'location': location?.toJson(),
      'services': services,
      'categories': categories,
      'price_range': priceRange.toJson(),
      'working_hours': workingHours?.toJson(),
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  /// Get distance string for display
  String get distanceString {
    if (location?.distance != null) {
      final distance = location!.distance!;
      if (distance < 1) {
        return '${(distance * 1000).round()}m';
      } else {
        return '${distance.toStringAsFixed(1)}km';
      }
    }
    return 'Unknown';
  }

  /// Get price range string for display
  String get priceRangeString {
    return '\$' * priceRange.level;
  }

  /// Check if tailor is currently open
  bool get isCurrentlyOpen {
    if (workingHours == null) return false;
    final now = DateTime.now();
    final currentDay = now.weekday;
    final currentTime = TimeOfDay.fromDateTime(now);
    
    return workingHours!.isOpenAt(currentDay, currentTime);
  }

  @override
  List<Object?> get props => [
        id,
        name,
        email,
        phone,
        specialty,
        rating,
        reviewCount,
        imageUrl,
        isVerified,
        isOnline,
        bio,
        location,
        services,
        categories,
        priceRange,
        workingHours,
        createdAt,
        updatedAt,
      ];
}

/// Tailor location model
class TailorLocation extends Equatable {
  final double latitude;
  final double longitude;
  final String address;
  final String? city;
  final String? state;
  final String? country;
  final String? postalCode;
  final double? distance; // Distance from user in km

  const TailorLocation({
    required this.latitude,
    required this.longitude,
    required this.address,
    this.city,
    this.state,
    this.country,
    this.postalCode,
    this.distance,
  });

  factory TailorLocation.fromJson(Map<String, dynamic> json) {
    return TailorLocation(
      latitude: (json['latitude'] as num).toDouble(),
      longitude: (json['longitude'] as num).toDouble(),
      address: json['address'] as String,
      city: json['city'] as String?,
      state: json['state'] as String?,
      country: json['country'] as String?,
      postalCode: json['postal_code'] as String?,
      distance: json['distance'] != null 
          ? (json['distance'] as num).toDouble() 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'latitude': latitude,
      'longitude': longitude,
      'address': address,
      'city': city,
      'state': state,
      'country': country,
      'postal_code': postalCode,
      'distance': distance,
    };
  }

  @override
  List<Object?> get props => [
        latitude,
        longitude,
        address,
        city,
        state,
        country,
        postalCode,
        distance,
      ];
}

/// Price range model
class PriceRange extends Equatable {
  final int level; // 1-4 ($, $$, $$$, $$$$)
  final double? minPrice;
  final double? maxPrice;

  const PriceRange({
    required this.level,
    this.minPrice,
    this.maxPrice,
  });

  factory PriceRange.fromJson(Map<String, dynamic> json) {
    return PriceRange(
      level: json['level'] as int,
      minPrice: json['min_price'] != null 
          ? (json['min_price'] as num).toDouble() 
          : null,
      maxPrice: json['max_price'] != null 
          ? (json['max_price'] as num).toDouble() 
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'level': level,
      'min_price': minPrice,
      'max_price': maxPrice,
    };
  }

  @override
  List<Object?> get props => [level, minPrice, maxPrice];
}

/// Working hours model
class WorkingHours extends Equatable {
  final Map<int, DaySchedule> schedule; // 1-7 for Monday-Sunday

  const WorkingHours({required this.schedule});

  factory WorkingHours.fromJson(Map<String, dynamic> json) {
    final schedule = <int, DaySchedule>{};
    for (final entry in json.entries) {
      final day = int.parse(entry.key);
      schedule[day] = DaySchedule.fromJson(entry.value as Map<String, dynamic>);
    }
    return WorkingHours(schedule: schedule);
  }

  Map<String, dynamic> toJson() {
    final json = <String, dynamic>{};
    for (final entry in schedule.entries) {
      json[entry.key.toString()] = entry.value.toJson();
    }
    return json;
  }

  bool isOpenAt(int day, TimeOfDay time) {
    final daySchedule = schedule[day];
    if (daySchedule == null || !daySchedule.isOpen) return false;
    
    final timeMinutes = time.hour * 60 + time.minute;
    final openMinutes = daySchedule.openTime.hour * 60 + daySchedule.openTime.minute;
    final closeMinutes = daySchedule.closeTime.hour * 60 + daySchedule.closeTime.minute;
    
    return timeMinutes >= openMinutes && timeMinutes <= closeMinutes;
  }

  @override
  List<Object?> get props => [schedule];
}

/// Day schedule model
class DaySchedule extends Equatable {
  final bool isOpen;
  final TimeOfDay openTime;
  final TimeOfDay closeTime;

  const DaySchedule({
    required this.isOpen,
    required this.openTime,
    required this.closeTime,
  });

  factory DaySchedule.fromJson(Map<String, dynamic> json) {
    return DaySchedule(
      isOpen: json['is_open'] as bool,
      openTime: _timeFromString(json['open_time'] as String),
      closeTime: _timeFromString(json['close_time'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'is_open': isOpen,
      'open_time': _timeToString(openTime),
      'close_time': _timeToString(closeTime),
    };
  }

  static TimeOfDay _timeFromString(String timeString) {
    final parts = timeString.split(':');
    return TimeOfDay(
      hour: int.parse(parts[0]),
      minute: int.parse(parts[1]),
    );
  }

  static String _timeToString(TimeOfDay time) {
    return '${time.hour.toString().padLeft(2, '0')}:${time.minute.toString().padLeft(2, '0')}';
  }

  @override
  List<Object?> get props => [isOpen, openTime, closeTime];
}
