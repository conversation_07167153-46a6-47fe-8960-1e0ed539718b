import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:tailor_link/data/models/api_response.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/logging/api_logger.dart';
import '../../data/services/api_service_manager.dart';

/// Demo screen to showcase beautiful API logging
class ApiDemoScreen extends StatefulWidget {
  const ApiDemoScreen({super.key});

  @override
  State<ApiDemoScreen> createState() => _ApiDemoScreenState();
}

class _ApiDemoScreenState extends State<ApiDemoScreen> {
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('API Logging Demo'),
        backgroundColor: AppTheme.primaryColor,
        foregroundColor: Colors.white,
        elevation: 0,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              decoration: BoxDecoration(
                gradient: AppTheme.instagramGradient,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  const Icon(
                    Icons.bug_report,
                    color: Colors.white,
                    size: 32,
                  ),
                  const SizedBox(height: 8),
                  const Text(
                    'Beautiful API Logging',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Test API calls and see beautiful logs in the console',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 14,
                    ),
                  ),
                ],
              ),
            ).animate().fadeIn(duration: 600.ms),
            
            const SizedBox(height: AppConfig.largePadding),
            
            // Info Card
            Container(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              decoration: BoxDecoration(
                color: Colors.blue.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
              ),
              child: Row(
                children: [
                  const Icon(
                    Icons.info_outline,
                    color: Colors.blue,
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        const Text(
                          'Check Console Output',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: Colors.blue,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          'Open your IDE console or debug output to see the beautiful API logs with colors, formatting, and detailed information.',
                          style: TextStyle(
                            color: Colors.blue.shade700,
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ),
                  ),
                ],
              ),
            ).animate().fadeIn(delay: 200.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.largePadding),
            
            // Demo Buttons
            Text(
              'Test API Calls',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            
            const SizedBox(height: AppConfig.defaultPadding),
            
            // GET Request Demo
            _buildDemoButton(
              context,
              title: 'GET Request Demo',
              subtitle: 'Test a successful GET request',
              icon: Icons.download,
              color: Colors.green,
              onTap: _testGetRequest,
            ).animate().fadeIn(delay: 300.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.defaultPadding),
            
            // POST Request Demo
            _buildDemoButton(
              context,
              title: 'POST Request Demo',
              subtitle: 'Test a POST request with body',
              icon: Icons.upload,
              color: Colors.blue,
              onTap: _testPostRequest,
            ).animate().fadeIn(delay: 400.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.defaultPadding),
            
            // Error Request Demo
            _buildDemoButton(
              context,
              title: 'Error Request Demo',
              subtitle: 'Test an API error response',
              icon: Icons.error,
              color: Colors.red,
              onTap: _testErrorRequest,
            ).animate().fadeIn(delay: 500.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.defaultPadding),
            
            // Timeout Demo
            _buildDemoButton(
              context,
              title: 'Timeout Demo',
              subtitle: 'Test a request timeout',
              icon: Icons.timer,
              color: Colors.orange,
              onTap: _testTimeoutRequest,
            ).animate().fadeIn(delay: 600.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.largePadding),
            
            // Manual Log Demo
            Text(
              'Manual Logging',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            
            const SizedBox(height: AppConfig.defaultPadding),
            
            _buildDemoButton(
              context,
              title: 'Log Info Message',
              subtitle: 'Test manual info logging',
              icon: Icons.info,
              color: Colors.cyan,
              onTap: _testInfoLog,
            ).animate().fadeIn(delay: 700.ms, duration: 600.ms),
            
            const SizedBox(height: AppConfig.largePadding),
            
            if (_isLoading)
              const Center(
                child: CircularProgressIndicator(),
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildDemoButton(
    BuildContext context, {
    required String title,
    required String subtitle,
    required IconData icon,
    required Color color,
    required VoidCallback onTap,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: _isLoading ? null : onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(color: AppTheme.dividerColor),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Row(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: color,
                  size: 24,
                ),
              ),
              
              const SizedBox(width: AppConfig.defaultPadding),
              
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      title,
                      style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      subtitle,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              
              Icon(
                Icons.arrow_forward_ios,
                color: AppTheme.dividerColor,
                size: 16,
              ),
            ],
          ),
        ),
      ),
    );
  }

  Future<void> _testGetRequest() async {
    setState(() => _isLoading = true);
    
    try {
      // Test GET request with the tailor service
      await ApiServiceManager.instance.tailors.getFeaturedTailors(limit: 5);
      
      _showSnackBar('GET request completed! Check console for logs.', Colors.green);
    } catch (e) {
      _showSnackBar('GET request failed! Check console for error logs.', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testPostRequest() async {
    setState(() => _isLoading = true);
    
    try {
      // Test POST request with auth service
      await ApiServiceManager.instance.auth.login(
        email: '<EMAIL>',
        password: 'password123',
      );
      
      _showSnackBar('POST request completed! Check console for logs.', Colors.blue);
    } catch (e) {
      _showSnackBar('POST request failed! Check console for error logs.', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testErrorRequest() async {
    setState(() => _isLoading = true);
    
    try {
      // Test request that will likely fail
      await ApiServiceManager.instance.tailors.getTailorById('invalid-id-12345');
      
      _showSnackBar('Error request completed! Check console for logs.', Colors.orange);
    } catch (e) {
      _showSnackBar('Error request failed as expected! Check console for error logs.', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testTimeoutRequest() async {
    setState(() => _isLoading = true);
    
    try {
      // This will likely timeout or fail
      await ApiServiceManager.instance.tailors.searchTailors(
        filters: TailorSearchFilters(
          query: 'test-timeout-request'
        ),
      );
      
      _showSnackBar('Timeout request completed! Check console for logs.', Colors.orange);
    } catch (e) {
      _showSnackBar('Timeout request failed! Check console for error logs.', Colors.red);
    } finally {
      setState(() => _isLoading = false);
    }
  }

  void _testInfoLog() {
    ApiLogger.logInfo(
      'This is a manual info log message with beautiful formatting! 🎉',
      tag: 'DEMO',
    );
    
    _showSnackBar('Info log sent! Check console output.', Colors.cyan);
  }

  void _showSnackBar(String message, Color color) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(message),
        backgroundColor: color,
        behavior: SnackBarBehavior.floating,
      ),
    );
  }
}
