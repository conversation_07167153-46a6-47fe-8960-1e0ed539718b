import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';
import 'bloc/wallet_bloc.dart';
import 'widgets/wallet_header.dart';
import 'widgets/wallet_actions.dart';
import 'widgets/transaction_list.dart';
import 'widgets/wallet_stats.dart';

/// Instagram-inspired wallet screen for financial management
class WalletScreen extends StatefulWidget {
  const WalletScreen({super.key});

  @override
  State<WalletScreen> createState() => _WalletScreenState();
}

class _WalletScreenState extends State<WalletScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    if (_scrollController.position.pixels ==
        _scrollController.position.maxScrollExtent) {
      // Load more transactions
      context.read<WalletBloc>().add(const LoadTransactions(page: 2));
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => WalletBloc()..add(const LoadWallet()),
      child: Scaffold(
        body: BlocConsumer<WalletBloc, WalletState>(
          listener: (context, state) {
            if (state is WalletError) {
              ScaffoldMessenger.of(context).showSnackBar(
                AccessibilityHelper.accessibleSnackBar(
                  message: state.message,
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is WalletOperationSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                AccessibilityHelper.accessibleSnackBar(
                  message: state.message,
                  backgroundColor: AppTheme.successColor,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is WalletLoading) {
              return _buildLoadingState();
            } else if (state is WalletLoaded) {
              return _buildLoadedState(context, state);
            } else if (state is WalletError) {
              return _buildErrorState(context, state);
            }
            return _buildInitialState();
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppConfig.defaultPadding),
          Text('Loading wallet...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, WalletError state) {
    return AccessibilityHelper.accessibleErrorMessage(
      message: state.message,
      onRetry: () {
        context.read<WalletBloc>().add(const LoadWallet());
      },
    );
  }

  Widget _buildInitialState() {
    return const Center(
      child: Text('Welcome to your wallet'),
    );
  }

  Widget _buildLoadedState(BuildContext context, WalletLoaded state) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // Custom App Bar with Wallet Header
        SliverAppBar(
          expandedHeight: 280,
          floating: false,
          pinned: true,
          backgroundColor: AppTheme.primaryColor,
          flexibleSpace: FlexibleSpaceBar(
            background: WalletHeader(
              user: state.user,
              wallet: state.wallet,
            ).animate().fadeIn(duration: 600.ms),
          ),
          title: Text(
            'My Wallet',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                context.read<WalletBloc>().add(const RefreshWallet());
              },
              icon: const Icon(
                Icons.refresh,
                color: Colors.white,
              ),
              tooltip: 'Refresh wallet',
            ),
            IconButton(
              onPressed: () {
                _showWalletMenu(context);
              },
              icon: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
              tooltip: 'Wallet options',
            ),
          ],
        ),

        // Wallet Stats
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: WalletStats(
              wallet: state.wallet,
              transactions: state.transactions,
            ),
          ).animate().fadeIn(delay: 200.ms, duration: 600.ms),
        ),

        // Quick Actions
        SliverToBoxAdapter(
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConfig.defaultPadding,
            ),
            child: WalletActions(
              wallet: state.wallet,
              onTopUp: () => _showTopUpDialog(context),
              onWithdraw: () => _showWithdrawDialog(context),
              onTransfer: () => _showTransferDialog(context),
              onPayBills: () => _showPayBillsDialog(context),
            ),
          ).animate().fadeIn(delay: 300.ms, duration: 600.ms),
        ),

        // Tab Bar
        SliverToBoxAdapter(
          child: Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: AppTheme.secondaryTextColor,
              indicatorColor: AppTheme.primaryColor,
              tabs: const [
                Tab(text: 'All'),
                Tab(text: 'Income'),
                Tab(text: 'Expenses'),
              ],
            ),
          ),
        ),

        // Transaction List
        SliverFillRemaining(
          child: TabBarView(
            controller: _tabController,
            children: [
              TransactionList(
                transactions: state.transactions,
                isLoadingMore: state.isLoadingMore,
              ),
              TransactionList(
                transactions: state.transactions
                    .where((t) => t.amount > 0)
                    .toList(),
                isLoadingMore: state.isLoadingMore,
              ),
              TransactionList(
                transactions: state.transactions
                    .where((t) => t.amount < 0)
                    .toList(),
                isLoadingMore: state.isLoadingMore,
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showWalletMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Transaction History'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/wallet/history');
              },
            ),
            ListTile(
              leading: const Icon(Icons.settings),
              title: const Text('Wallet Settings'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/wallet/settings');
              },
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('Security'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/wallet/security');
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('Help & Support'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/help');
              },
            ),
            const SizedBox(height: AppConfig.defaultPadding),
          ],
        ),
      ),
    );
  }

  void _showTopUpDialog(BuildContext context) {
    Navigator.pushNamed(context, '/wallet/top-up');
  }

  void _showWithdrawDialog(BuildContext context) {
    Navigator.pushNamed(context, '/wallet/withdraw');
  }

  void _showTransferDialog(BuildContext context) {
    Navigator.pushNamed(context, '/wallet/transfer');
  }

  void _showPayBillsDialog(BuildContext context) {
    Navigator.pushNamed(context, '/wallet/pay-bills');
  }
}
