import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/wallet_model.dart';

/// Instagram-inspired wallet statistics widget
class WalletStats extends StatelessWidget {
  final WalletModel wallet;
  final List<TransactionModel> transactions;

  const WalletStats({
    super.key,
    required this.wallet,
    required this.transactions,
  });

  @override
  Widget build(BuildContext context) {
    final stats = _calculateStats();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'This Month',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                title: 'Income',
                amount: stats['income']!,
                icon: Icons.trending_up,
                color: Colors.green,
                isPositive: true,
              ).animate().scale(delay: 100.ms, duration: 400.ms),
            ),
            
            const SizedBox(width: AppConfig.defaultPadding),
            
            Expanded(
              child: _buildStatCard(
                context,
                title: 'Expenses',
                amount: stats['expenses']!,
                icon: Icons.trending_down,
                color: Colors.red,
                isPositive: false,
              ).animate().scale(delay: 200.ms, duration: 400.ms),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        Row(
          children: [
            Expanded(
              child: _buildStatCard(
                context,
                title: 'Savings',
                amount: stats['savings']!,
                icon: Icons.savings,
                color: Colors.blue,
                isPositive: true,
              ).animate().scale(delay: 300.ms, duration: 400.ms),
            ),
            
            const SizedBox(width: AppConfig.defaultPadding),
            
            Expanded(
              child: _buildStatCard(
                context,
                title: 'Transactions',
                amount: stats['transactionCount']!,
                icon: Icons.receipt_long,
                color: AppTheme.primaryColor,
                isCount: true,
              ).animate().scale(delay: 400.ms, duration: 400.ms),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.largePadding),
        
        // Spending Categories
        _buildSpendingCategories(context),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context, {
    required String title,
    required double amount,
    required IconData icon,
    required Color color,
    bool isPositive = true,
    bool isCount = false,
  }) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Icon(
                icon,
                color: color,
                size: 24,
              ),
              
              if (!isCount)
                Icon(
                  isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                  color: isPositive ? Colors.green : Colors.red,
                  size: 16,
                ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          Text(
            title,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          
          const SizedBox(height: 4),
          
          Text(
            isCount 
                ? amount.toInt().toString()
                : '₮${_formatAmount(amount)}',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          
          if (!isCount) ...[
            const SizedBox(height: 4),
            Text(
              '${isPositive ? '+' : ''}${_calculatePercentageChange()}%',
              style: TextStyle(
                color: isPositive ? Colors.green : Colors.red,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildSpendingCategories(BuildContext context) {
    final categories = _getSpendingCategories();

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Spending Categories',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/wallet/analytics');
              },
              child: Text(
                'View All',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        ...categories.asMap().entries.map((entry) {
          final index = entry.key;
          final category = entry.value;
          
          return _buildCategoryItem(
            context,
            category,
          ).animate().fadeIn(delay: (500 + index * 100).ms, duration: 400.ms);
        }),
      ],
    );
  }

  Widget _buildCategoryItem(
    BuildContext context,
    Map<String, dynamic> category,
  ) {
    final percentage = category['percentage'] as double;
    
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: (category['color'] as Color).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              category['icon'] as IconData,
              color: category['color'] as Color,
              size: 20,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      category['name'] as String,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    
                    Text(
                      '₮${_formatAmount(category['amount'] as double)}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                  ],
                ),
                
                const SizedBox(height: 4),
                
                Row(
                  children: [
                    Expanded(
                      child: LinearProgressIndicator(
                        value: percentage / 100,
                        backgroundColor: AppTheme.dividerColor,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          category['color'] as Color,
                        ),
                      ),
                    ),
                    
                    const SizedBox(width: 8),
                    
                    Text(
                      '${percentage.toStringAsFixed(0)}%',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Map<String, double> _calculateStats() {
    final now = DateTime.now();
    final thisMonth = DateTime(now.year, now.month);
    
    final thisMonthTransactions = transactions.where((t) =>
        t.createdAt.isAfter(thisMonth)).toList();
    
    final income = thisMonthTransactions
        .where((t) => t.amount > 0)
        .fold(0.0, (sum, t) => sum + t.amount);
    
    final expenses = thisMonthTransactions
        .where((t) => t.amount < 0)
        .fold(0.0, (sum, t) => sum + t.amount.abs());
    
    final savings = income - expenses;
    
    return {
      'income': income,
      'expenses': expenses,
      'savings': savings,
      'transactionCount': thisMonthTransactions.length.toDouble(),
    };
  }

  List<Map<String, dynamic>> _getSpendingCategories() {
    return [
      {
        'name': 'Tailoring Services',
        'amount': 450000.0,
        'percentage': 45.0,
        'icon': Icons.content_cut,
        'color': AppTheme.primaryColor,
      },
      {
        'name': 'Materials & Supplies',
        'amount': 280000.0,
        'percentage': 28.0,
        'icon': Icons.inventory,
        'color': Colors.orange,
      },
      {
        'name': 'Equipment',
        'amount': 150000.0,
        'percentage': 15.0,
        'icon': Icons.build,
        'color': Colors.blue,
      },
      {
        'name': 'Other',
        'amount': 120000.0,
        'percentage': 12.0,
        'icon': Icons.more_horiz,
        'color': Colors.grey,
      },
    ];
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }

  String _calculatePercentageChange() {
    // Mock calculation - in real app, compare with previous month
    return '12.5';
  }
}
