import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/wallet_model.dart';

/// Instagram-inspired wallet action buttons
class WalletActions extends StatelessWidget {
  final WalletModel wallet;
  final VoidCallback onTopUp;
  final VoidCallback onWithdraw;
  final VoidCallback onTransfer;
  final VoidCallback onPayBills;

  const WalletActions({
    super.key,
    required this.wallet,
    required this.onTopUp,
    required this.onWithdraw,
    required this.onTransfer,
    required this.onPayBills,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        // Primary Actions Row
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                context,
                icon: Icons.add,
                label: 'Top Up',
                color: AppTheme.primaryColor,
                onTap: onTopUp,
              ).animate().scale(delay: 100.ms, duration: 400.ms),
            ),
            
            const SizedBox(width: AppConfig.defaultPadding),
            
            Expanded(
              child: _buildActionButton(
                context,
                icon: Icons.remove,
                label: 'Withdraw',
                color: AppTheme.accentColor,
                onTap: onWithdraw,
              ).animate().scale(delay: 200.ms, duration: 400.ms),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        // Secondary Actions Row
        Row(
          children: [
            Expanded(
              child: _buildActionButton(
                context,
                icon: Icons.send,
                label: 'Transfer',
                color: Colors.blue,
                onTap: onTransfer,
                isSecondary: true,
              ).animate().scale(delay: 300.ms, duration: 400.ms),
            ),
            
            const SizedBox(width: AppConfig.defaultPadding),
            
            Expanded(
              child: _buildActionButton(
                context,
                icon: Icons.receipt_long,
                label: 'Pay Bills',
                color: Colors.orange,
                onTap: onPayBills,
                isSecondary: true,
              ).animate().scale(delay: 400.ms, duration: 400.ms),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.largePadding),
        
        // Quick Transfer Section
        _buildQuickTransfer(context),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required IconData icon,
    required String label,
    required Color color,
    required VoidCallback onTap,
    bool isSecondary = false,
  }) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            color: isSecondary ? Colors.white : color,
            borderRadius: BorderRadius.circular(16),
            border: isSecondary 
                ? Border.all(color: AppTheme.dividerColor)
                : null,
            boxShadow: [
              BoxShadow(
                color: (isSecondary ? Colors.grey : color).withValues(alpha: 0.2),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            children: [
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: isSecondary 
                      ? color.withValues(alpha: 0.1)
                      : Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Icon(
                  icon,
                  color: isSecondary ? color : Colors.white,
                  size: 24,
                ),
              ),
              
              const SizedBox(height: 8),
              
              Text(
                label,
                style: TextStyle(
                  color: isSecondary ? AppTheme.primaryTextColor : Colors.white,
                  fontWeight: FontWeight.w600,
                  fontSize: 14,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildQuickTransfer(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Quick Transfer',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            
            TextButton(
              onPressed: () {
                Navigator.pushNamed(context, '/wallet/contacts');
              },
              child: Text(
                'See All',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
        
        const SizedBox(height: AppConfig.defaultPadding),
        
        SizedBox(
          height: 80,
          child: ListView.separated(
            scrollDirection: Axis.horizontal,
            itemCount: _quickTransferContacts.length,
            separatorBuilder: (context, index) => 
                const SizedBox(width: AppConfig.defaultPadding),
            itemBuilder: (context, index) {
              final contact = _quickTransferContacts[index];
              return _buildQuickTransferContact(context, contact);
            },
          ),
        ),
      ],
    ).animate().fadeIn(delay: 500.ms, duration: 600.ms);
  }

  Widget _buildQuickTransferContact(
    BuildContext context,
    Map<String, dynamic> contact,
  ) {
    return GestureDetector(
      onTap: () {
        Navigator.pushNamed(
          context,
          '/wallet/transfer',
          arguments: contact,
        );
      },
      child: Column(
        children: [
          CircleAvatar(
            radius: 25,
            backgroundImage: contact['image'] != null
                ? NetworkImage(contact['image'])
                : null,
            backgroundColor: AppTheme.primaryColor,
            child: contact['image'] == null
                ? Text(
                    contact['name'][0].toUpperCase(),
                    style: const TextStyle(
                      color: Colors.white,
                      fontWeight: FontWeight.bold,
                    ),
                  )
                : null,
          ),
          
          const SizedBox(height: 4),
          
          Text(
            contact['name'],
            style: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
            maxLines: 1,
            overflow: TextOverflow.ellipsis,
          ),
        ],
      ),
    );
  }

  static final List<Map<String, dynamic>> _quickTransferContacts = [
    {
      'id': 'contact_1',
      'name': 'Batbayar',
      'image': 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=100',
    },
    {
      'id': 'contact_2',
      'name': 'Erdene',
      'image': 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=100',
    },
    {
      'id': 'contact_3',
      'name': 'Munkh',
      'image': null,
    },
    {
      'id': 'contact_4',
      'name': 'Oyunaa',
      'image': 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=100',
    },
    {
      'id': 'contact_5',
      'name': 'Tseveendorj',
      'image': null,
    },
  ];
}
