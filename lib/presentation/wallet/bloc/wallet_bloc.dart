import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/services/api_service_manager.dart';
import '../../../data/models/wallet_model.dart';
import '../../../data/models/user_model.dart';

// Events
abstract class WalletEvent extends Equatable {
  const WalletEvent();

  @override
  List<Object?> get props => [];
}

class LoadWallet extends WalletEvent {
  const LoadWallet();
}

class RefreshWallet extends WalletEvent {
  const RefreshWallet();
}

class TopUpWallet extends WalletEvent {
  final double amount;
  final String paymentMethod;
  final String? description;

  const TopUpWallet({
    required this.amount,
    required this.paymentMethod,
    this.description,
  });

  @override
  List<Object?> get props => [amount, paymentMethod, description];
}

class WithdrawFromWallet extends WalletEvent {
  final double amount;
  final String bankAccount;
  final String? description;

  const WithdrawFromWallet({
    required this.amount,
    required this.bankAccount,
    this.description,
  });

  @override
  List<Object?> get props => [amount, bankAccount, description];
}

class TransferMoney extends WalletEvent {
  final String recipientId;
  final double amount;
  final String? description;

  const TransferMoney({
    required this.recipientId,
    required this.amount,
    this.description,
  });

  @override
  List<Object?> get props => [recipientId, amount, description];
}

class LoadTransactions extends WalletEvent {
  final int page;
  final String? type;
  final String? status;

  const LoadTransactions({
    this.page = 1,
    this.type,
    this.status,
  });

  @override
  List<Object?> get props => [page, type, status];
}

// States
abstract class WalletState extends Equatable {
  const WalletState();

  @override
  List<Object?> get props => [];
}

class WalletInitial extends WalletState {
  const WalletInitial();
}

class WalletLoading extends WalletState {
  const WalletLoading();
}

class WalletLoaded extends WalletState {
  final UserModel user;
  final WalletModel wallet;
  final List<TransactionModel> transactions;
  final bool isLoadingMore;

  const WalletLoaded({
    required this.user,
    required this.wallet,
    required this.transactions,
    this.isLoadingMore = false,
  });

  WalletLoaded copyWith({
    UserModel? user,
    WalletModel? wallet,
    List<TransactionModel>? transactions,
    bool? isLoadingMore,
  }) {
    return WalletLoaded(
      user: user ?? this.user,
      wallet: wallet ?? this.wallet,
      transactions: transactions ?? this.transactions,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
    );
  }

  @override
  List<Object?> get props => [user, wallet, transactions, isLoadingMore];
}

class WalletError extends WalletState {
  final String message;

  const WalletError({required this.message});

  @override
  List<Object?> get props => [message];
}

class WalletOperationSuccess extends WalletState {
  final String message;
  final WalletModel wallet;

  const WalletOperationSuccess({
    required this.message,
    required this.wallet,
  });

  @override
  List<Object?> get props => [message, wallet];
}

// BLoC
class WalletBloc extends Bloc<WalletEvent, WalletState> {
  final ApiServiceManager _apiService;

  WalletBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const WalletInitial()) {
    on<LoadWallet>(_onLoadWallet);
    on<RefreshWallet>(_onRefreshWallet);
    on<TopUpWallet>(_onTopUpWallet);
    on<WithdrawFromWallet>(_onWithdrawFromWallet);
    on<TransferMoney>(_onTransferMoney);
    on<LoadTransactions>(_onLoadTransactions);
  }

  Future<void> _onLoadWallet(
    LoadWallet event,
    Emitter<WalletState> emit,
  ) async {
    emit(const WalletLoading());

    try {
      // For now, use mock data but structure it to be API-ready
      // TODO: Replace with real API calls when backend is ready
      final user = await _getMockUser();
      final wallet = await _getMockWallet();
      final transactions = await _getMockTransactions();

      // Future API integration would look like:
      // try {
      //   final walletResponse = await _apiService.wallet.getWallet();
      //   final transactionsResponse = await _apiService.wallet.getTransactions();
      //   if (walletResponse.success && transactionsResponse.success) {
      //     wallet = WalletModel.fromJson(walletResponse.data!);
      //     transactions = transactionsResponse.data!.map((data) => TransactionModel.fromJson(data)).toList();
      //   }
      // } catch (e) {
      //   // Fallback to mock data
      // }

      emit(WalletLoaded(
        user: user,
        wallet: wallet,
        transactions: transactions,
      ));
    } catch (e) {
      emit(WalletError(message: 'Failed to load wallet: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshWallet(
    RefreshWallet event,
    Emitter<WalletState> emit,
  ) async {
    if (state is WalletLoaded) {
      final currentState = state as WalletLoaded;
      
      try {
        final wallet = await _getMockWallet();
        final transactions = await _getMockTransactions();

        emit(currentState.copyWith(
          wallet: wallet,
          transactions: transactions,
        ));
      } catch (e) {
        emit(WalletError(message: 'Failed to refresh wallet: ${e.toString()}'));
      }
    }
  }

  Future<void> _onTopUpWallet(
    TopUpWallet event,
    Emitter<WalletState> emit,
  ) async {
    if (state is WalletLoaded) {
      final currentState = state as WalletLoaded;
      
      try {
        // TODO: Call real API
        // final response = await _apiService.wallet.topUp(
        //   amount: event.amount,
        //   paymentMethod: event.paymentMethod,
        //   description: event.description,
        // );

        // Simulate successful top-up
        await Future.delayed(const Duration(seconds: 2));
        
        final updatedWallet = currentState.wallet.copyWith(
          balance: currentState.wallet.balance + event.amount,
          updatedAt: DateTime.now(),
        );

        emit(WalletOperationSuccess(
          message: 'Top-up successful! ₮${event.amount.toStringAsFixed(0)} added to your wallet.',
          wallet: updatedWallet,
        ));

        // Reload wallet data
        add(const LoadWallet());
      } catch (e) {
        emit(WalletError(message: 'Top-up failed: ${e.toString()}'));
      }
    }
  }

  Future<void> _onWithdrawFromWallet(
    WithdrawFromWallet event,
    Emitter<WalletState> emit,
  ) async {
    if (state is WalletLoaded) {
      final currentState = state as WalletLoaded;
      
      if (currentState.wallet.balance < event.amount) {
        emit(const WalletError(message: 'Insufficient balance for withdrawal'));
        return;
      }
      
      try {
        // TODO: Call real API
        // final response = await _apiService.wallet.withdraw(
        //   amount: event.amount,
        //   bankAccount: event.bankAccount,
        //   description: event.description,
        // );

        // Simulate successful withdrawal
        await Future.delayed(const Duration(seconds: 2));
        
        final updatedWallet = currentState.wallet.copyWith(
          balance: currentState.wallet.balance - event.amount,
          updatedAt: DateTime.now(),
        );

        emit(WalletOperationSuccess(
          message: 'Withdrawal successful! ₮${event.amount.toStringAsFixed(0)} will be transferred to your bank account.',
          wallet: updatedWallet,
        ));

        // Reload wallet data
        add(const LoadWallet());
      } catch (e) {
        emit(WalletError(message: 'Withdrawal failed: ${e.toString()}'));
      }
    }
  }

  Future<void> _onTransferMoney(
    TransferMoney event,
    Emitter<WalletState> emit,
  ) async {
    if (state is WalletLoaded) {
      final currentState = state as WalletLoaded;
      
      if (currentState.wallet.balance < event.amount) {
        emit(const WalletError(message: 'Insufficient balance for transfer'));
        return;
      }
      
      try {
        // TODO: Call real API
        // final response = await _apiService.wallet.transfer(
        //   recipientId: event.recipientId,
        //   amount: event.amount,
        //   description: event.description,
        // );

        // Simulate successful transfer
        await Future.delayed(const Duration(seconds: 2));
        
        final updatedWallet = currentState.wallet.copyWith(
          balance: currentState.wallet.balance - event.amount,
          updatedAt: DateTime.now(),
        );

        emit(WalletOperationSuccess(
          message: 'Transfer successful! ₮${event.amount.toStringAsFixed(0)} sent.',
          wallet: updatedWallet,
        ));

        // Reload wallet data
        add(const LoadWallet());
      } catch (e) {
        emit(WalletError(message: 'Transfer failed: ${e.toString()}'));
      }
    }
  }

  Future<void> _onLoadTransactions(
    LoadTransactions event,
    Emitter<WalletState> emit,
  ) async {
    if (state is WalletLoaded) {
      final currentState = state as WalletLoaded;
      
      if (event.page == 1) {
        emit(currentState.copyWith(isLoadingMore: false));
      } else {
        emit(currentState.copyWith(isLoadingMore: true));
      }
      
      try {
        final newTransactions = await _getMockTransactions();
        
        final allTransactions = event.page == 1 
            ? newTransactions
            : [...currentState.transactions, ...newTransactions];

        emit(currentState.copyWith(
          transactions: allTransactions,
          isLoadingMore: false,
        ));
      } catch (e) {
        emit(WalletError(message: 'Failed to load transactions: ${e.toString()}'));
      }
    }
  }

  // Mock data methods
  Future<UserModel> _getMockUser() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return UserModel(
      id: 'user_1',
      email: '<EMAIL>',
      firstName: 'Master',
      lastName: 'Tailor',
      phone: '+976 9999 9999',
      role: UserRole.tailor,
      isEmailVerified: true,
      isPhoneVerified: true,
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  Future<WalletModel> _getMockWallet() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return WalletModel(
      id: 'wallet_1',
      userId: 'user_1',
      balance: 2500000.0, // ₮2,500,000
      currency: 'MNT',
      isActive: true,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      recentTransactions: [],
      limits: const WalletLimits(
        dailyLimit: 1000000.0,
        monthlyLimit: 10000000.0,
        maxBalance: 50000000.0,
        minTopUp: 10000.0,
        maxTopUp: 5000000.0,
      ),
    );
  }

  Future<List<TransactionModel>> _getMockTransactions() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      TransactionModel(
        id: 'txn_1',
        walletId: 'wallet_1',
        type: TransactionType.payment,
        amount: -150000.0,
        currency: 'MNT',
        description: 'Payment to Batbayar Tailor',
        status: TransactionStatus.completed,
        orderId: 'order_123',
        createdAt: DateTime.now().subtract(const Duration(hours: 2)),
      ),
      TransactionModel(
        id: 'txn_2',
        walletId: 'wallet_1',
        type: TransactionType.topUp,
        amount: 500000.0,
        currency: 'MNT',
        description: 'Wallet top-up via bank transfer',
        status: TransactionStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 1)),
      ),
      TransactionModel(
        id: 'txn_3',
        walletId: 'wallet_1',
        type: TransactionType.refund,
        amount: 75000.0,
        currency: 'MNT',
        description: 'Refund for cancelled order',
        status: TransactionStatus.completed,
        orderId: 'order_456',
        createdAt: DateTime.now().subtract(const Duration(days: 3)),
      ),
      TransactionModel(
        id: 'txn_4',
        walletId: 'wallet_1',
        type: TransactionType.transfer,
        amount: -200000.0,
        currency: 'MNT',
        description: 'Transfer to Erdene Tailor',
        status: TransactionStatus.completed,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
      TransactionModel(
        id: 'txn_5',
        walletId: 'wallet_1',
        type: TransactionType.withdrawal,
        amount: -300000.0,
        currency: 'MNT',
        description: 'Withdrawal to bank account',
        status: TransactionStatus.processing,
        createdAt: DateTime.now().subtract(const Duration(days: 7)),
      ),
    ];
  }
}
