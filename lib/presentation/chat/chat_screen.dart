import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';

import '../../data/models/tailor_model.dart';
import 'bloc/chat_bloc.dart';
import 'widgets/chat_app_bar.dart';
import 'widgets/chat_message_list.dart';
import 'widgets/chat_input_bar.dart';
import 'widgets/chat_attachment_preview.dart';
import 'widgets/typing_indicator.dart';

/// Instagram-inspired chat screen for tailor-customer communication
class ChatScreen extends StatefulWidget {
  final String chatId;
  final String? orderId;
  final TailorModel? tailor;
  final String? customerId;

  const ChatScreen({
    super.key,
    required this.chatId,
    this.orderId,
    this.tailor,
    this.customerId,
  });

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen>
    with TickerProviderStateMixin, WidgetsBindingObserver {
  late ScrollController _scrollController;
  late TextEditingController _messageController;
  late FocusNode _messageFocusNode;
  late AnimationController _inputAnimationController;
  
  bool _isKeyboardVisible = false;
  List<String> _selectedAttachments = [];

  @override
  void initState() {
    super.initState();
    _scrollController = ScrollController();
    _messageController = TextEditingController();
    _messageFocusNode = FocusNode();
    _inputAnimationController = AnimationController(
      duration: AppConfig.mediumAnimation,
      vsync: this,
    );
    
    WidgetsBinding.instance.addObserver(this);
    _messageFocusNode.addListener(_onFocusChange);
  }

  @override
  void dispose() {
    WidgetsBinding.instance.removeObserver(this);
    _scrollController.dispose();
    _messageController.dispose();
    _messageFocusNode.dispose();
    _inputAnimationController.dispose();
    super.dispose();
  }

  @override
  void didChangeMetrics() {
    super.didChangeMetrics();
    final bottomInset = View.of(context).viewInsets.bottom;
    final isKeyboardVisible = bottomInset > 0;
    
    if (isKeyboardVisible != _isKeyboardVisible) {
      setState(() {
        _isKeyboardVisible = isKeyboardVisible;
      });
      
      if (isKeyboardVisible) {
        _scrollToBottom();
      }
    }
  }

  void _onFocusChange() {
    if (_messageFocusNode.hasFocus) {
      _inputAnimationController.forward();
      _scrollToBottom();
    } else {
      _inputAnimationController.reverse();
    }
  }

  void _scrollToBottom() {
    WidgetsBinding.instance.addPostFrameCallback((_) {
      if (_scrollController.hasClients) {
        _scrollController.animateTo(
          _scrollController.position.maxScrollExtent,
          duration: AppConfig.mediumAnimation,
          curve: Curves.easeOut,
        );
      }
    });
  }

  void _sendMessage() {
    final message = _messageController.text.trim();
    if (message.isNotEmpty || _selectedAttachments.isNotEmpty) {
      context.read<ChatBloc>().add(
        SendMessage(
          chatId: widget.chatId,
          message: message,
          attachments: List.from(_selectedAttachments),
        ),
      );
      
      _messageController.clear();
      _selectedAttachments.clear();
      _scrollToBottom();
    }
  }

  void _addAttachment(String attachment) {
    setState(() {
      _selectedAttachments.add(attachment);
    });
  }

  void _removeAttachment(String attachment) {
    setState(() {
      _selectedAttachments.remove(attachment);
    });
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => ChatBloc()
        ..add(LoadChat(
          chatId: widget.chatId,
          orderId: widget.orderId,
          tailorId: widget.tailor?.id,
          customerId: widget.customerId,
        )),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: BlocBuilder<ChatBloc, ChatState>(
          builder: (context, state) {
            if (state is ChatLoading) {
              return AccessibilityHelper.accessibleLoadingIndicator(
                semanticLabel: 'Loading chat',
              );
            }

            if (state is ChatError) {
              return AccessibilityHelper.accessibleErrorMessage(
                message: state.message,
                onRetry: () {
                  context.read<ChatBloc>().add(
                        LoadChat(
                          chatId: widget.chatId,
                          orderId: widget.orderId,
                          tailorId: widget.tailor?.id,
                          customerId: widget.customerId,
                        ),
                      );
                },
              );
            }

            if (state is ChatLoaded) {
              return _buildChatInterface(context, state);
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildChatInterface(BuildContext context, ChatLoaded state) {
    return Column(
      children: [
        // Chat App Bar
        ChatAppBar(
          chat: state.chat,
          tailor: widget.tailor,
          onCallPressed: () => _makeCall(context),
          onVideoCallPressed: () => _makeVideoCall(context),
          onInfoPressed: () => _showChatInfo(context, state),
        ),

        // Messages Area
        Expanded(
          child: Stack(
            children: [
              // Background Pattern
              _buildChatBackground(),
              
              // Messages List
              ChatMessageList(
                messages: state.messages,
                scrollController: _scrollController,
                currentUserId: 'current_user_id', // TODO: Get from auth
              ),
              
              // Typing Indicator
              if (state.isTyping)
                Positioned(
                  bottom: 16,
                  left: 16,
                  child: TypingIndicator(
                    senderName: widget.tailor?.name ?? 'Tailor',
                  ),
                ),
            ],
          ),
        ),

        // Attachment Preview
        if (_selectedAttachments.isNotEmpty)
          ChatAttachmentPreview(
            attachments: _selectedAttachments,
            onRemove: _removeAttachment,
          ),

        // Chat Input Bar
        AnimatedBuilder(
          animation: _inputAnimationController,
          builder: (context, child) {
            return Transform.translate(
              offset: Offset(
                0,
                (1 - _inputAnimationController.value) * 10,
              ),
              child: ChatInputBar(
                controller: _messageController,
                focusNode: _messageFocusNode,
                onSendPressed: _sendMessage,
                onAttachmentPressed: () => _showAttachmentOptions(context),
                onCameraPressed: () => _openCamera(context),
                isExpanded: _messageFocusNode.hasFocus,
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildChatBackground() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.backgroundColor,
            AppTheme.backgroundColor.withValues(alpha: 0.5),
          ],
        ),
      ),
      child: CustomPaint(
        painter: ChatBackgroundPainter(),
        size: Size.infinite,
      ),
    );
  }

  void _makeCall(BuildContext context) {
    // TODO: Implement voice call
    ScaffoldMessenger.of(context).showSnackBar(
      AccessibilityHelper.accessibleSnackBar(
        message: 'Calling ${widget.tailor?.name ?? 'tailor'}...',
      ),
    );
  }

  void _makeVideoCall(BuildContext context) {
    // TODO: Implement video call
    ScaffoldMessenger.of(context).showSnackBar(
      AccessibilityHelper.accessibleSnackBar(
        message: 'Starting video call with ${widget.tailor?.name ?? 'tailor'}...',
      ),
    );
  }

  void _showChatInfo(BuildContext context, ChatLoaded state) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _ChatInfoSheet(
        chat: state.chat,
        tailor: widget.tailor,
        orderId: widget.orderId,
      ),
    );
  }

  void _showAttachmentOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Add Attachment',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceEvenly,
              children: [
                _buildAttachmentOption(
                  context,
                  icon: Icons.photo_library,
                  label: 'Gallery',
                  onTap: () {
                    Navigator.pop(context);
                    _pickFromGallery();
                  },
                ),
                _buildAttachmentOption(
                  context,
                  icon: Icons.camera_alt,
                  label: 'Camera',
                  onTap: () {
                    Navigator.pop(context);
                    _openCamera(context);
                  },
                ),
                _buildAttachmentOption(
                  context,
                  icon: Icons.insert_drive_file,
                  label: 'Document',
                  onTap: () {
                    Navigator.pop(context);
                    _pickDocument();
                  },
                ),
              ],
            ),
            
            const SizedBox(height: AppConfig.defaultPadding),
          ],
        ),
      ),
    );
  }

  Widget _buildAttachmentOption(
    BuildContext context, {
    required IconData icon,
    required String label,
    required VoidCallback onTap,
  }) {
    return AccessibilityHelper.accessibleButton(
      child: Column(
        children: [
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(30),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 30,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.primaryTextColor,
            ),
          ),
        ],
      ),
      onPressed: onTap,
      semanticLabel: 'Add $label attachment',
    );
  }

  void _pickFromGallery() {
    // TODO: Implement gallery picker
    _addAttachment('https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300');
  }

  void _openCamera(BuildContext context) {
    // TODO: Implement camera
    _addAttachment('https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300');
  }

  void _pickDocument() {
    // TODO: Implement document picker
    _addAttachment('document.pdf');
  }
}

class _ChatInfoSheet extends StatelessWidget {
  final ChatModel chat;
  final TailorModel? tailor;
  final String? orderId;

  const _ChatInfoSheet({
    required this.chat,
    this.tailor,
    this.orderId,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              if (tailor?.imageUrl != null)
                CircleAvatar(
                  radius: 30,
                  backgroundImage: CachedNetworkImageProvider(
                    tailor!.imageUrl!,
                  ),
                )
              else
                CircleAvatar(
                  radius: 30,
                  backgroundColor: AppTheme.primaryColor,
                  child: Text(
                    tailor?.name[0].toUpperCase() ?? 'T',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      tailor?.name ?? 'Tailor',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    if (tailor?.isVerified == true) ...[
                      const SizedBox(height: 4),
                      Row(
                        children: [
                          const Icon(
                            Icons.verified,
                            color: AppTheme.primaryColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            'Verified Tailor',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Chat Actions
          if (orderId != null) ...[
            ListTile(
              leading: const Icon(Icons.receipt_long),
              title: const Text('View Order'),
              trailing: const Icon(Icons.arrow_forward_ios, size: 16),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(
                  context,
                  '/order-details',
                  arguments: orderId,
                );
              },
            ),
            const Divider(),
          ],

          ListTile(
            leading: const Icon(Icons.person),
            title: const Text('View Profile'),
            trailing: const Icon(Icons.arrow_forward_ios, size: 16),
            onTap: () {
              Navigator.pop(context);
              if (tailor != null) {
                Navigator.pushNamed(
                  context,
                  '/tailor-profile',
                  arguments: tailor!.id,
                );
              }
            },
          ),

          ListTile(
            leading: const Icon(Icons.block),
            title: const Text('Block User'),
            onTap: () {
              Navigator.pop(context);
              _showBlockDialog(context);
            },
          ),

          ListTile(
            leading: const Icon(Icons.report),
            title: const Text('Report'),
            onTap: () {
              Navigator.pop(context);
              _showReportDialog(context);
            },
          ),
        ],
      ),
    );
  }

  void _showBlockDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text('Are you sure you want to block ${tailor?.name ?? 'this user'}?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement block functionality
            },
            child: const Text(
              'Block',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showReportDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Report User'),
        content: Text('Report ${tailor?.name ?? 'this user'} for inappropriate behavior?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement report functionality
            },
            child: const Text(
              'Report',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }
}

class ChatBackgroundPainter extends CustomPainter {
  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.dividerColor.withValues(alpha: 0.1)
      ..strokeWidth = 1;

    // Draw subtle pattern
    for (int i = 0; i < size.width; i += 50) {
      for (int j = 0; j < size.height; j += 50) {
        canvas.drawCircle(
          Offset(i.toDouble(), j.toDouble()),
          1,
          paint,
        );
      }
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}
