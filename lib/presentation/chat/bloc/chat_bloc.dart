import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/services/mock_api_service.dart';

// Events
abstract class ChatEvent extends Equatable {
  const ChatEvent();

  @override
  List<Object?> get props => [];
}

class LoadChat extends ChatEvent {
  final String chatId;
  final String? orderId;
  final String? tailorId;
  final String? customerId;

  const LoadChat({
    required this.chatId,
    this.orderId,
    this.tailorId,
    this.customerId,
  });

  @override
  List<Object?> get props => [chatId, orderId, tailorId, customerId];
}

class SendMessage extends ChatEvent {
  final String chatId;
  final String message;
  final List<String> attachments;

  const SendMessage({
    required this.chatId,
    required this.message,
    this.attachments = const [],
  });

  @override
  List<Object?> get props => [chatId, message, attachments];
}

class ReceiveMessage extends ChatEvent {
  final ChatMessage message;

  const ReceiveMessage({required this.message});

  @override
  List<Object?> get props => [message];
}

class StartTyping extends ChatEvent {
  final String chatId;
  final String userId;

  const StartTyping({
    required this.chatId,
    required this.userId,
  });

  @override
  List<Object?> get props => [chatId, userId];
}

class StopTyping extends ChatEvent {
  final String chatId;
  final String userId;

  const StopTyping({
    required this.chatId,
    required this.userId,
  });

  @override
  List<Object?> get props => [chatId, userId];
}

class MarkMessagesAsRead extends ChatEvent {
  final String chatId;
  final List<String> messageIds;

  const MarkMessagesAsRead({
    required this.chatId,
    required this.messageIds,
  });

  @override
  List<Object?> get props => [chatId, messageIds];
}

class DeleteMessage extends ChatEvent {
  final String chatId;
  final String messageId;

  const DeleteMessage({
    required this.chatId,
    required this.messageId,
  });

  @override
  List<Object?> get props => [chatId, messageId];
}

// States
abstract class ChatState extends Equatable {
  const ChatState();

  @override
  List<Object?> get props => [];
}

class ChatInitial extends ChatState {
  const ChatInitial();
}

class ChatLoading extends ChatState {
  const ChatLoading();
}

class ChatLoaded extends ChatState {
  final ChatModel chat;
  final List<ChatMessage> messages;
  final bool isTyping;
  final String? typingUserId;
  final bool isConnected;

  const ChatLoaded({
    required this.chat,
    required this.messages,
    this.isTyping = false,
    this.typingUserId,
    this.isConnected = true,
  });

  @override
  List<Object?> get props => [
        chat,
        messages,
        isTyping,
        typingUserId,
        isConnected,
      ];

  ChatLoaded copyWith({
    ChatModel? chat,
    List<ChatMessage>? messages,
    bool? isTyping,
    String? typingUserId,
    bool? isConnected,
  }) {
    return ChatLoaded(
      chat: chat ?? this.chat,
      messages: messages ?? this.messages,
      isTyping: isTyping ?? this.isTyping,
      typingUserId: typingUserId ?? this.typingUserId,
      isConnected: isConnected ?? this.isConnected,
    );
  }
}

class ChatError extends ChatState {
  final String message;

  const ChatError({required this.message});

  @override
  List<Object?> get props => [message];
}

class MessageSent extends ChatState {
  final ChatMessage message;

  const MessageSent({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class ChatBloc extends Bloc<ChatEvent, ChatState> {
  final MockApiService _apiService;

  ChatBloc({MockApiService? apiService})
      : _apiService = apiService ?? MockApiService(),
        super(const ChatInitial()) {
    on<LoadChat>(_onLoadChat);
    on<SendMessage>(_onSendMessage);
    on<ReceiveMessage>(_onReceiveMessage);
    on<StartTyping>(_onStartTyping);
    on<StopTyping>(_onStopTyping);
    on<MarkMessagesAsRead>(_onMarkMessagesAsRead);
    on<DeleteMessage>(_onDeleteMessage);
  }

  Future<void> _onLoadChat(
    LoadChat event,
    Emitter<ChatState> emit,
  ) async {
    emit(const ChatLoading());

    try {
      // Load chat and messages
      final chat = await _getMockChat(event.chatId);
      final messages = await _getMockMessages(event.chatId);

      emit(ChatLoaded(
        chat: chat,
        messages: messages,
      ));

      // Simulate real-time connection
      _simulateRealTimeUpdates(event.chatId);
    } catch (e) {
      emit(ChatError(
        message: 'Failed to load chat: ${e.toString()}',
      ));
    }
  }

  Future<void> _onSendMessage(
    SendMessage event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;

      try {
        // Create new message
        final newMessage = ChatMessage(
          id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
          chatId: event.chatId,
          senderId: 'current_user_id', // TODO: Get from auth
          senderName: 'You',
          message: event.message,
          attachments: event.attachments,
          timestamp: DateTime.now(),
          isFromCurrentUser: true,
          status: MessageStatus.sending,
        );

        // Add message to list immediately (optimistic update)
        final updatedMessages = [...currentState.messages, newMessage];
        emit(currentState.copyWith(messages: updatedMessages));

        // Simulate API call
        await Future.delayed(const Duration(milliseconds: 500));

        // Update message status to sent
        final sentMessage = newMessage.copyWith(status: MessageStatus.sent);
        final finalMessages = updatedMessages.map((msg) {
          return msg.id == newMessage.id ? sentMessage : msg;
        }).toList();

        emit(currentState.copyWith(messages: finalMessages));

        // Simulate delivery and read status updates
        _simulateMessageStatusUpdates(event.chatId, sentMessage.id);
      } catch (e) {
        emit(ChatError(
          message: 'Failed to send message: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onReceiveMessage(
    ReceiveMessage event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      final updatedMessages = [...currentState.messages, event.message];
      emit(currentState.copyWith(messages: updatedMessages));
    }
  }

  Future<void> _onStartTyping(
    StartTyping event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      emit(currentState.copyWith(
        isTyping: true,
        typingUserId: event.userId,
      ));
    }
  }

  Future<void> _onStopTyping(
    StopTyping event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      emit(currentState.copyWith(
        isTyping: false,
        typingUserId: null,
      ));
    }
  }

  Future<void> _onMarkMessagesAsRead(
    MarkMessagesAsRead event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedMessages = currentState.messages.map((message) {
        if (event.messageIds.contains(message.id)) {
          return message.copyWith(status: MessageStatus.read);
        }
        return message;
      }).toList();

      emit(currentState.copyWith(messages: updatedMessages));
    }
  }

  Future<void> _onDeleteMessage(
    DeleteMessage event,
    Emitter<ChatState> emit,
  ) async {
    if (state is ChatLoaded) {
      final currentState = state as ChatLoaded;
      
      final updatedMessages = currentState.messages
          .where((message) => message.id != event.messageId)
          .toList();

      emit(currentState.copyWith(messages: updatedMessages));
    }
  }

  void _simulateRealTimeUpdates(String chatId) {
    // Simulate receiving messages and typing indicators
    Future.delayed(const Duration(seconds: 3), () {
      if (state is ChatLoaded) {
        add(StartTyping(chatId: chatId, userId: 'tailor_1'));
        
        Future.delayed(const Duration(seconds: 2), () {
          add(StopTyping(chatId: chatId, userId: 'tailor_1'));
          
          final receivedMessage = ChatMessage(
            id: 'msg_received_${DateTime.now().millisecondsSinceEpoch}',
            chatId: chatId,
            senderId: 'tailor_1',
            senderName: 'Master Tailor',
            message: 'Thank you for your message! I\'ll get back to you shortly.',
            timestamp: DateTime.now(),
            isFromCurrentUser: false,
            status: MessageStatus.delivered,
          );
          
          add(ReceiveMessage(message: receivedMessage));
        });
      }
    });
  }

  void _simulateMessageStatusUpdates(String chatId, String messageId) {
    // Simulate delivery status
    Future.delayed(const Duration(seconds: 1), () {
      if (state is ChatLoaded) {
        final currentState = state as ChatLoaded;
        final updatedMessages = currentState.messages.map((msg) {
          return msg.id == messageId 
              ? msg.copyWith(status: MessageStatus.delivered)
              : msg;
        }).toList();
        emit(currentState.copyWith(messages: updatedMessages));
      }
    });

    // Simulate read status
    Future.delayed(const Duration(seconds: 5), () {
      if (state is ChatLoaded) {
        final currentState = state as ChatLoaded;
        final updatedMessages = currentState.messages.map((msg) {
          return msg.id == messageId 
              ? msg.copyWith(status: MessageStatus.read)
              : msg;
        }).toList();
        emit(currentState.copyWith(messages: updatedMessages));
      }
    });
  }

  Future<ChatModel> _getMockChat(String chatId) async {
    await Future.delayed(const Duration(milliseconds: 300));
    
    return ChatModel(
      id: chatId,
      participants: ['current_user_id', 'tailor_1'],
      lastMessage: 'Thank you for your order!',
      lastMessageTime: DateTime.now().subtract(const Duration(minutes: 5)),
      unreadCount: 0,
      isActive: true,
      orderId: 'order_123',
    );
  }

  Future<List<ChatMessage>> _getMockMessages(String chatId) async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      ChatMessage(
        id: 'msg_1',
        chatId: chatId,
        senderId: 'tailor_1',
        senderName: 'Master Tailor',
        message: 'Hello! I\'ve received your order and I\'m excited to work with you.',
        timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        isFromCurrentUser: false,
        status: MessageStatus.read,
      ),
      ChatMessage(
        id: 'msg_2',
        chatId: chatId,
        senderId: 'current_user_id',
        senderName: 'You',
        message: 'Thank you! I\'m looking forward to seeing the final result.',
        timestamp: DateTime.now().subtract(const Duration(hours: 1, minutes: 30)),
        isFromCurrentUser: true,
        status: MessageStatus.read,
      ),
      ChatMessage(
        id: 'msg_3',
        chatId: chatId,
        senderId: 'tailor_1',
        senderName: 'Master Tailor',
        message: 'I\'ll start working on your suit tomorrow. Here\'s the fabric we discussed.',
        attachments: ['https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=300'],
        timestamp: DateTime.now().subtract(const Duration(minutes: 45)),
        isFromCurrentUser: false,
        status: MessageStatus.read,
      ),
      ChatMessage(
        id: 'msg_4',
        chatId: chatId,
        senderId: 'current_user_id',
        senderName: 'You',
        message: 'Perfect! That looks exactly like what I wanted.',
        timestamp: DateTime.now().subtract(const Duration(minutes: 30)),
        isFromCurrentUser: true,
        status: MessageStatus.read,
      ),
    ];
  }
}

// Chat Models
class ChatModel extends Equatable {
  final String id;
  final List<String> participants;
  final String? lastMessage;
  final DateTime? lastMessageTime;
  final int unreadCount;
  final bool isActive;
  final String? orderId;

  const ChatModel({
    required this.id,
    required this.participants,
    this.lastMessage,
    this.lastMessageTime,
    this.unreadCount = 0,
    this.isActive = true,
    this.orderId,
  });

  @override
  List<Object?> get props => [
        id,
        participants,
        lastMessage,
        lastMessageTime,
        unreadCount,
        isActive,
        orderId,
      ];
}

class ChatMessage extends Equatable {
  final String id;
  final String chatId;
  final String senderId;
  final String senderName;
  final String message;
  final List<String> attachments;
  final DateTime timestamp;
  final bool isFromCurrentUser;
  final MessageStatus status;

  const ChatMessage({
    required this.id,
    required this.chatId,
    required this.senderId,
    required this.senderName,
    required this.message,
    this.attachments = const [],
    required this.timestamp,
    required this.isFromCurrentUser,
    this.status = MessageStatus.sent,
  });

  @override
  List<Object?> get props => [
        id,
        chatId,
        senderId,
        senderName,
        message,
        attachments,
        timestamp,
        isFromCurrentUser,
        status,
      ];

  ChatMessage copyWith({
    String? id,
    String? chatId,
    String? senderId,
    String? senderName,
    String? message,
    List<String>? attachments,
    DateTime? timestamp,
    bool? isFromCurrentUser,
    MessageStatus? status,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      chatId: chatId ?? this.chatId,
      senderId: senderId ?? this.senderId,
      senderName: senderName ?? this.senderName,
      message: message ?? this.message,
      attachments: attachments ?? this.attachments,
      timestamp: timestamp ?? this.timestamp,
      isFromCurrentUser: isFromCurrentUser ?? this.isFromCurrentUser,
      status: status ?? this.status,
    );
  }
}

enum MessageStatus {
  sending,
  sent,
  delivered,
  read,
  failed,
}
