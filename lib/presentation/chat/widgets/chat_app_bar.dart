import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';
import '../bloc/chat_bloc.dart';

/// Instagram-inspired chat app bar
class ChatAppBar extends StatelessWidget implements PreferredSizeWidget {
  final ChatModel chat;
  final TailorModel? tailor;
  final VoidCallback? onCallPressed;
  final VoidCallback? onVideoCallPressed;
  final VoidCallback? onInfoPressed;

  const ChatAppBar({
    super.key,
    required this.chat,
    this.tailor,
    this.onCallPressed,
    this.onVideoCallPressed,
    this.onInfoPressed,
  });

  @override
  Size get preferredSize => const Size.fromHeight(kToolbarHeight);

  @override
  Widget build(BuildContext context) {
    return AppBar(
      backgroundColor: Colors.white,
      elevation: 1,
      leading: AccessibilityHelper.accessibleButton(
        child: const Icon(
          Icons.arrow_back_ios,
          color: AppTheme.primaryTextColor,
          size: 20,
        ),
        onPressed: () => Navigator.of(context).pop(),
        semanticLabel: 'Go back',
      ),
      title: _buildTitleSection(context),
      actions: _buildActions(context),
    );
  }

  Widget _buildTitleSection(BuildContext context) {
    return AccessibilityHelper.accessibleButton(
      child: Row(
        children: [
          // Profile Avatar
          Stack(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundImage: tailor?.imageUrl != null
                    ? CachedNetworkImageProvider(tailor!.imageUrl!)
                    : null,
                backgroundColor: AppTheme.primaryColor,
                child: tailor?.imageUrl == null
                    ? Text(
                        tailor?.name[0].toUpperCase() ?? 'T',
                        style: const TextStyle(
                          color: Colors.white,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),
              
              // Online Status Indicator
              if (chat.isActive)
                Positioned(
                  bottom: 0,
                  right: 0,
                  child: Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppTheme.successColor,
                      borderRadius: BorderRadius.circular(6),
                      border: Border.all(
                        color: Colors.white,
                        width: 2,
                      ),
                    ),
                  ),
                ),
            ],
          ),

          const SizedBox(width: 12),

          // Name and Status
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Flexible(
                      child: Text(
                        tailor?.name ?? 'Tailor',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryTextColor,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    if (tailor?.isVerified == true) ...[
                      const SizedBox(width: 4),
                      const Icon(
                        Icons.verified,
                        color: AppTheme.primaryColor,
                        size: 16,
                      ),
                    ],
                  ],
                ),
                Text(
                  _getStatusText(),
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
      onPressed: onInfoPressed,
      semanticLabel: 'View ${tailor?.name ?? 'tailor'} profile',
    )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  List<Widget> _buildActions(BuildContext context) {
    return [
      // Video Call Button
      AccessibilityHelper.accessibleButton(
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Icon(
            Icons.videocam,
            color: AppTheme.primaryColor,
            size: 24,
          ),
        ),
        onPressed: onVideoCallPressed,
        semanticLabel: 'Start video call',
      )
          .animate()
          .fadeIn(delay: 200.ms, duration: 400.ms)
          .scale(begin: const Offset(0.8, 0.8), duration: 400.ms),

      // Voice Call Button
      AccessibilityHelper.accessibleButton(
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Icon(
            Icons.call,
            color: AppTheme.primaryColor,
            size: 24,
          ),
        ),
        onPressed: onCallPressed,
        semanticLabel: 'Start voice call',
      )
          .animate()
          .fadeIn(delay: 300.ms, duration: 400.ms)
          .scale(begin: const Offset(0.8, 0.8), duration: 400.ms),

      // More Options
      AccessibilityHelper.accessibleButton(
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Icon(
            Icons.more_vert,
            color: AppTheme.primaryTextColor,
            size: 24,
          ),
        ),
        onPressed: () => _showMoreOptions(context),
        semanticLabel: 'More options',
      )
          .animate()
          .fadeIn(delay: 400.ms, duration: 400.ms)
          .scale(begin: const Offset(0.8, 0.8), duration: 400.ms),

      const SizedBox(width: 8),
    ];
  }

  String _getStatusText() {
    if (chat.isActive) {
      return 'Online';
    } else if (chat.lastMessageTime != null) {
      final now = DateTime.now();
      final difference = now.difference(chat.lastMessageTime!);
      
      if (difference.inMinutes < 1) {
        return 'Active now';
      } else if (difference.inMinutes < 60) {
        return 'Active ${difference.inMinutes}m ago';
      } else if (difference.inHours < 24) {
        return 'Active ${difference.inHours}h ago';
      } else {
        return 'Active ${difference.inDays}d ago';
      }
    }
    return 'Offline';
  }

  void _showMoreOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Chat Options',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),

            // View Profile
            ListTile(
              leading: const Icon(Icons.person),
              title: const Text('View Profile'),
              onTap: () {
                Navigator.pop(context);
                if (onInfoPressed != null) {
                  onInfoPressed!();
                }
              },
            ),

            // View Order
            if (chat.orderId != null)
              ListTile(
                leading: const Icon(Icons.receipt_long),
                title: const Text('View Order'),
                onTap: () {
                  Navigator.pop(context);
                  Navigator.pushNamed(
                    context,
                    '/order-details',
                    arguments: chat.orderId,
                  );
                },
              ),

            // Search Messages
            ListTile(
              leading: const Icon(Icons.search),
              title: const Text('Search Messages'),
              onTap: () {
                Navigator.pop(context);
                _showSearchDialog(context);
              },
            ),

            // Clear Chat
            ListTile(
              leading: const Icon(Icons.clear_all),
              title: const Text('Clear Chat'),
              onTap: () {
                Navigator.pop(context);
                _showClearChatDialog(context);
              },
            ),

            // Block User
            ListTile(
              leading: const Icon(Icons.block, color: AppTheme.errorColor),
              title: const Text(
                'Block User',
                style: TextStyle(color: AppTheme.errorColor),
              ),
              onTap: () {
                Navigator.pop(context);
                _showBlockDialog(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Messages'),
        content: const TextField(
          decoration: InputDecoration(
            hintText: 'Search in conversation...',
            prefixIcon: Icon(Icons.search),
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement search functionality
            },
            child: const Text('Search'),
          ),
        ],
      ),
    );
  }

  void _showClearChatDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Clear Chat'),
        content: const Text(
          'Are you sure you want to clear all messages in this chat? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement clear chat functionality
            },
            child: const Text(
              'Clear',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _showBlockDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Block User'),
        content: Text(
          'Are you sure you want to block ${tailor?.name ?? 'this user'}? You won\'t receive messages from them anymore.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              // TODO: Implement block functionality
            },
            child: const Text(
              'Block',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }
}
