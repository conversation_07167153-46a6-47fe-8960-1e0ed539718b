import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Instagram-inspired chat input bar
class ChatInputBar extends StatefulWidget {
  final TextEditingController controller;
  final FocusNode focusNode;
  final VoidCallback onSendPressed;
  final VoidCallback? onAttachmentPressed;
  final VoidCallback? onCameraPressed;
  final bool isExpanded;

  const ChatInputBar({
    super.key,
    required this.controller,
    required this.focusNode,
    required this.onSendPressed,
    this.onAttachmentPressed,
    this.onCameraPressed,
    this.isExpanded = false,
  });

  @override
  State<ChatInputBar> createState() => _ChatInputBarState();
}

class _ChatInputBarState extends State<ChatInputBar>
    with TickerProviderStateMixin {
  late AnimationController _sendButtonController;
  late AnimationController _expandController;
  bool _hasText = false;

  @override
  void initState() {
    super.initState();
    _sendButtonController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _expandController = AnimationController(
      duration: AppConfig.mediumAnimation,
      vsync: this,
    );
    
    widget.controller.addListener(_onTextChanged);
  }

  @override
  void dispose() {
    _sendButtonController.dispose();
    _expandController.dispose();
    widget.controller.removeListener(_onTextChanged);
    super.dispose();
  }

  @override
  void didUpdateWidget(ChatInputBar oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (widget.isExpanded != oldWidget.isExpanded) {
      if (widget.isExpanded) {
        _expandController.forward();
      } else {
        _expandController.reverse();
      }
    }
  }

  void _onTextChanged() {
    final hasText = widget.controller.text.trim().isNotEmpty;
    if (hasText != _hasText) {
      setState(() {
        _hasText = hasText;
      });
      
      if (hasText) {
        _sendButtonController.forward();
      } else {
        _sendButtonController.reverse();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        left: AppConfig.defaultPadding,
        right: AppConfig.defaultPadding,
        top: AppConfig.smallPadding,
        bottom: AppConfig.defaultPadding + MediaQuery.of(context).padding.bottom,
      ),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SafeArea(
        child: AnimatedBuilder(
          animation: _expandController,
          builder: (context, child) {
            return Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Quick Actions (when expanded)
                if (widget.isExpanded)
                  Container(
                    height: 60 * _expandController.value,
                    child: _expandController.value > 0.5
                        ? _buildQuickActions()
                        : null,
                  ),

                // Main Input Row
                Row(
                  crossAxisAlignment: CrossAxisAlignment.end,
                  children: [
                    // Attachment Button
                    if (!_hasText)
                      AccessibilityHelper.accessibleButton(
                        child: Container(
                          padding: const EdgeInsets.all(8),
                          child: Icon(
                            Icons.add,
                            color: AppTheme.primaryColor,
                            size: 24,
                          ),
                        ),
                        onPressed: widget.onAttachmentPressed,
                        semanticLabel: 'Add attachment',
                      )
                          .animate()
                          .fadeIn(duration: 200.ms)
                          .scale(begin: const Offset(0.8, 0.8), duration: 200.ms),

                    if (!_hasText) const SizedBox(width: 8),

                    // Message Input Field
                    Expanded(
                      child: Container(
                        constraints: const BoxConstraints(
                          minHeight: 40,
                          maxHeight: 120,
                        ),
                        decoration: BoxDecoration(
                          color: AppTheme.backgroundColor,
                          borderRadius: BorderRadius.circular(20),
                          border: Border.all(
                            color: widget.focusNode.hasFocus
                                ? AppTheme.primaryColor
                                : AppTheme.dividerColor,
                            width: widget.focusNode.hasFocus ? 2 : 1,
                          ),
                        ),
                        child: Row(
                          children: [
                            // Text Input
                            Expanded(
                              child: TextField(
                                controller: widget.controller,
                                focusNode: widget.focusNode,
                                decoration: const InputDecoration(
                                  hintText: 'Type a message...',
                                  border: InputBorder.none,
                                  contentPadding: EdgeInsets.symmetric(
                                    horizontal: 16,
                                    vertical: 10,
                                  ),
                                ),
                                maxLines: null,
                                textCapitalization: TextCapitalization.sentences,
                                onSubmitted: (_) => _handleSend(),
                              ),
                            ),

                            // Emoji Button
                            if (!_hasText)
                              AccessibilityHelper.accessibleButton(
                                child: Container(
                                  padding: const EdgeInsets.all(8),
                                  child: Icon(
                                    Icons.emoji_emotions_outlined,
                                    color: AppTheme.secondaryTextColor,
                                    size: 20,
                                  ),
                                ),
                                onPressed: () {
                                  // TODO: Show emoji picker
                                },
                                semanticLabel: 'Add emoji',
                              ),
                          ],
                        ),
                      ),
                    ),

                    const SizedBox(width: 8),

                    // Send/Camera Button
                    AnimatedBuilder(
                      animation: _sendButtonController,
                      builder: (context, child) {
                        return AccessibilityHelper.accessibleButton(
                          child: Container(
                            width: 40,
                            height: 40,
                            decoration: BoxDecoration(
                              gradient: _hasText ? AppTheme.instagramGradient : null,
                              color: _hasText ? null : AppTheme.backgroundColor,
                              borderRadius: BorderRadius.circular(20),
                              border: _hasText ? null : Border.all(
                                color: AppTheme.dividerColor,
                              ),
                            ),
                            child: Icon(
                              _hasText ? Icons.send : Icons.camera_alt,
                              color: _hasText ? Colors.white : AppTheme.primaryColor,
                              size: 20,
                            ),
                          ),
                          onPressed: _hasText ? _handleSend : widget.onCameraPressed,
                          semanticLabel: _hasText ? 'Send message' : 'Take photo',
                        );
                      },
                    ),
                  ],
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  Widget _buildQuickActions() {
    return Container(
      padding: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceEvenly,
        children: [
          _buildQuickAction(
            icon: Icons.photo_library,
            label: 'Gallery',
            onTap: () {
              // TODO: Open gallery
            },
          ),
          _buildQuickAction(
            icon: Icons.camera_alt,
            label: 'Camera',
            onTap: widget.onCameraPressed,
          ),
          _buildQuickAction(
            icon: Icons.insert_drive_file,
            label: 'Document',
            onTap: () {
              // TODO: Open document picker
            },
          ),
          _buildQuickAction(
            icon: Icons.location_on,
            label: 'Location',
            onTap: () {
              // TODO: Share location
            },
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 0.5, end: 0, duration: 300.ms);
  }

  Widget _buildQuickAction({
    required IconData icon,
    required String label,
    VoidCallback? onTap,
  }) {
    return AccessibilityHelper.accessibleButton(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Icon(
              icon,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
              fontSize: 10,
            ),
          ),
        ],
      ),
      onPressed: onTap,
      semanticLabel: label,
    );
  }

  void _handleSend() {
    if (widget.controller.text.trim().isNotEmpty) {
      widget.onSendPressed();
    }
  }
}
