import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/chat_bloc.dart';

/// Instagram-inspired chat message list
class ChatMessageList extends StatelessWidget {
  final List<ChatMessage> messages;
  final ScrollController scrollController;
  final String currentUserId;

  const ChatMessageList({
    super.key,
    required this.messages,
    required this.scrollController,
    required this.currentUserId,
  });

  @override
  Widget build(BuildContext context) {
    if (messages.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.builder(
      controller: scrollController,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConfig.defaultPadding,
        vertical: AppConfig.smallPadding,
      ),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        final previousMessage = index > 0 ? messages[index - 1] : null;
        final nextMessage = index < messages.length - 1 ? messages[index + 1] : null;
        
        final showAvatar = _shouldShowAvatar(message, nextMessage);
        final showTimestamp = _shouldShowTimestamp(message, previousMessage);
        
        return Column(
          children: [
            if (showTimestamp) _buildTimestamp(context, message.timestamp),
            _buildMessageBubble(
              context,
              message,
              showAvatar,
              index,
            ),
          ],
        );
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No messages yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start the conversation!',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTimestamp(BuildContext context, DateTime timestamp) {
    return Container(
      margin: const EdgeInsets.symmetric(vertical: AppConfig.defaultPadding),
      child: Text(
        _formatTimestamp(timestamp),
        style: Theme.of(context).textTheme.bodySmall?.copyWith(
          color: AppTheme.secondaryTextColor,
          fontWeight: FontWeight.w500,
        ),
        textAlign: TextAlign.center,
      ),
    );
  }

  Widget _buildMessageBubble(
    BuildContext context,
    ChatMessage message,
    bool showAvatar,
    int index,
  ) {
    final isFromCurrentUser = message.isFromCurrentUser;
    
    return Container(
      margin: const EdgeInsets.only(bottom: 4),
      child: Row(
        mainAxisAlignment: isFromCurrentUser 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          // Avatar for other users
          if (!isFromCurrentUser) ...[
            Container(
              width: 32,
              height: 32,
              margin: const EdgeInsets.only(right: 8, bottom: 4),
              child: showAvatar
                  ? CircleAvatar(
                      radius: 16,
                      backgroundColor: AppTheme.primaryColor,
                      child: Text(
                        message.senderName[0].toUpperCase(),
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 12,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    )
                  : const SizedBox.shrink(),
            ),
          ],

          // Message Content
          Flexible(
            child: GestureDetector(
              onLongPress: () => _showMessageOptions(context, message),
              child: Container(
                constraints: BoxConstraints(
                  maxWidth: MediaQuery.of(context).size.width * 0.75,
                ),
                child: Column(
                  crossAxisAlignment: isFromCurrentUser 
                      ? CrossAxisAlignment.end 
                      : CrossAxisAlignment.start,
                  children: [
                    // Message Bubble
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 16,
                        vertical: 12,
                      ),
                      decoration: BoxDecoration(
                        gradient: isFromCurrentUser 
                            ? AppTheme.instagramGradient 
                            : null,
                        color: isFromCurrentUser 
                            ? null 
                            : Colors.white,
                        borderRadius: BorderRadius.only(
                          topLeft: const Radius.circular(18),
                          topRight: const Radius.circular(18),
                          bottomLeft: Radius.circular(
                            isFromCurrentUser || !showAvatar ? 18 : 4,
                          ),
                          bottomRight: Radius.circular(
                            isFromCurrentUser && showAvatar ? 4 : 18,
                          ),
                        ),
                        border: isFromCurrentUser 
                            ? null 
                            : Border.all(
                                color: AppTheme.dividerColor,
                                width: 1,
                              ),
                        boxShadow: [
                          BoxShadow(
                            color: Colors.black.withValues(alpha: 0.05),
                            blurRadius: 4,
                            offset: const Offset(0, 1),
                          ),
                        ],
                      ),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Message Text
                          if (message.message.isNotEmpty)
                            Text(
                              message.message,
                              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                color: isFromCurrentUser 
                                    ? Colors.white 
                                    : AppTheme.primaryTextColor,
                                height: 1.4,
                              ),
                            ),

                          // Attachments
                          if (message.attachments.isNotEmpty) ...[
                            if (message.message.isNotEmpty)
                              const SizedBox(height: 8),
                            _buildAttachments(context, message.attachments),
                          ],
                        ],
                      ),
                    ),

                    // Message Status and Time
                    if (showAvatar)
                      Padding(
                        padding: const EdgeInsets.only(top: 4),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Text(
                              _formatMessageTime(message.timestamp),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.secondaryTextColor,
                                fontSize: 11,
                              ),
                            ),
                            if (isFromCurrentUser) ...[
                              const SizedBox(width: 4),
                              _buildMessageStatusIcon(message.status),
                            ],
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),

          // Spacing for current user messages
          if (isFromCurrentUser) const SizedBox(width: 32),
        ],
      ),
    )
        .animate(delay: (index * 50).ms)
        .fadeIn(duration: 400.ms)
        .slideY(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildAttachments(BuildContext context, List<String> attachments) {
    return SizedBox(
      height: 120,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: attachments.length,
        itemBuilder: (context, index) {
          final attachment = attachments[index];
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: GestureDetector(
              onTap: () => _showAttachmentDetail(context, attachment),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: _isImageAttachment(attachment)
                    ? AccessibilityHelper.accessibleImage(
                        image: CachedNetworkImageProvider(attachment),
                        semanticLabel: 'Attachment image ${index + 1}',
                        width: 120,
                        height: 120,
                        fit: BoxFit.cover,
                      )
                    : Container(
                        width: 120,
                        height: 120,
                        color: AppTheme.backgroundColor,
                        child: Column(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              Icons.insert_drive_file,
                              color: AppTheme.primaryColor,
                              size: 32,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              _getFileName(attachment),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                color: AppTheme.primaryTextColor,
                              ),
                              textAlign: TextAlign.center,
                              maxLines: 2,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ],
                        ),
                      ),
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMessageStatusIcon(MessageStatus status) {
    IconData icon;
    Color color;

    switch (status) {
      case MessageStatus.sending:
        icon = Icons.access_time;
        color = AppTheme.secondaryTextColor;
        break;
      case MessageStatus.sent:
        icon = Icons.check;
        color = AppTheme.secondaryTextColor;
        break;
      case MessageStatus.delivered:
        icon = Icons.done_all;
        color = AppTheme.secondaryTextColor;
        break;
      case MessageStatus.read:
        icon = Icons.done_all;
        color = AppTheme.primaryColor;
        break;
      case MessageStatus.failed:
        icon = Icons.error_outline;
        color = AppTheme.errorColor;
        break;
    }

    return Icon(
      icon,
      size: 12,
      color: color,
    );
  }

  bool _shouldShowAvatar(ChatMessage message, ChatMessage? nextMessage) {
    if (nextMessage == null) return true;
    if (nextMessage.senderId != message.senderId) return true;
    
    final timeDifference = nextMessage.timestamp.difference(message.timestamp);
    return timeDifference.inMinutes > 5;
  }

  bool _shouldShowTimestamp(ChatMessage message, ChatMessage? previousMessage) {
    if (previousMessage == null) return true;
    
    final timeDifference = message.timestamp.difference(previousMessage.timestamp);
    return timeDifference.inHours >= 1;
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays == 0) {
      return 'Today ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays == 1) {
      return 'Yesterday ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else if (difference.inDays < 7) {
      final weekdays = ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'];
      return '${weekdays[timestamp.weekday - 1]} ${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
    } else {
      return '${timestamp.day}/${timestamp.month}/${timestamp.year}';
    }
  }

  String _formatMessageTime(DateTime timestamp) {
    return '${timestamp.hour}:${timestamp.minute.toString().padLeft(2, '0')}';
  }

  bool _isImageAttachment(String attachment) {
    return attachment.toLowerCase().contains('http') && 
           (attachment.toLowerCase().contains('.jpg') ||
            attachment.toLowerCase().contains('.jpeg') ||
            attachment.toLowerCase().contains('.png') ||
            attachment.toLowerCase().contains('images.unsplash.com'));
  }

  String _getFileName(String attachment) {
    if (attachment.contains('/')) {
      return attachment.split('/').last;
    }
    return attachment;
  }

  void _showMessageOptions(BuildContext context, ChatMessage message) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Message Options',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            
            ListTile(
              leading: const Icon(Icons.copy),
              title: const Text('Copy'),
              onTap: () {
                Navigator.pop(context);
                // TODO: Copy message to clipboard
              },
            ),
            
            if (message.isFromCurrentUser)
              ListTile(
                leading: const Icon(Icons.delete, color: AppTheme.errorColor),
                title: const Text(
                  'Delete',
                  style: TextStyle(color: AppTheme.errorColor),
                ),
                onTap: () {
                  Navigator.pop(context);
                  // TODO: Delete message
                },
              ),
          ],
        ),
      ),
    );
  }

  void _showAttachmentDetail(BuildContext context, String attachment) {
    if (_isImageAttachment(attachment)) {
      Navigator.of(context).push(
        PageRouteBuilder(
          opaque: false,
          barrierColor: Colors.black87,
          pageBuilder: (context, animation, secondaryAnimation) {
            return _ImageDetailView(imageUrl: attachment);
          },
          transitionsBuilder: (context, animation, secondaryAnimation, child) {
            return FadeTransition(
              opacity: animation,
              child: ScaleTransition(
                scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                  CurvedAnimation(parent: animation, curve: Curves.easeOut),
                ),
                child: child,
              ),
            );
          },
        ),
      );
    }
  }
}

class _ImageDetailView extends StatelessWidget {
  final String imageUrl;

  const _ImageDetailView({required this.imageUrl});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Center(
          child: Container(
            margin: const EdgeInsets.all(AppConfig.largePadding),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              child: AccessibilityHelper.accessibleImage(
                image: CachedNetworkImageProvider(imageUrl),
                semanticLabel: 'Full size attachment image',
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
      ),
    );
  }
}
