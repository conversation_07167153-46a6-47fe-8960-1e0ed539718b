import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Preview widget for selected attachments before sending
class ChatAttachmentPreview extends StatelessWidget {
  final List<String> attachments;
  final Function(String) onRemove;

  const ChatAttachmentPreview({
    super.key,
    required this.attachments,
    required this.onRemove,
  });

  @override
  Widget build(BuildContext context) {
    if (attachments.isEmpty) {
      return const SizedBox.shrink();
    }

    return Container(
      height: 100,
      padding: const EdgeInsets.symmetric(
        horizontal: AppConfig.defaultPadding,
        vertical: AppConfig.smallPadding,
      ),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          top: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.attach_file,
                color: AppTheme.primaryColor,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(
                '${attachments.length} attachment${attachments.length > 1 ? 's' : ''}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              const Spacer(),
              AccessibilityHelper.accessibleButton(
                child: Icon(
                  Icons.clear_all,
                  color: AppTheme.secondaryTextColor,
                  size: 16,
                ),
                onPressed: () {
                  for (final attachment in List.from(attachments)) {
                    onRemove(attachment);
                  }
                },
                semanticLabel: 'Remove all attachments',
              ),
            ],
          ),

          const SizedBox(height: 8),

          // Attachments List
          Expanded(
            child: ListView.builder(
              scrollDirection: Axis.horizontal,
              itemCount: attachments.length,
              itemBuilder: (context, index) {
                final attachment = attachments[index];
                return _buildAttachmentItem(context, attachment, index);
              },
            ),
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: 1.0, end: 0, duration: 300.ms);
  }

  Widget _buildAttachmentItem(BuildContext context, String attachment, int index) {
    return Container(
      width: 60,
      margin: const EdgeInsets.only(right: 8),
      child: Stack(
        children: [
          // Attachment Content
          Container(
            width: 60,
            height: 60,
            decoration: BoxDecoration(
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.dividerColor,
                width: 1,
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: _isImageAttachment(attachment)
                  ? AccessibilityHelper.accessibleImage(
                      image: CachedNetworkImageProvider(attachment),
                      semanticLabel: 'Attachment preview ${index + 1}',
                      fit: BoxFit.cover,
                    )
                  : _buildDocumentPreview(context, attachment),
            ),
          ),

          // Remove Button
          Positioned(
            top: -4,
            right: -4,
            child: AccessibilityHelper.accessibleButton(
              child: Container(
                width: 20,
                height: 20,
                decoration: BoxDecoration(
                  color: AppTheme.errorColor,
                  borderRadius: BorderRadius.circular(10),
                  border: Border.all(
                    color: Colors.white,
                    width: 2,
                  ),
                ),
                child: const Icon(
                  Icons.close,
                  color: Colors.white,
                  size: 12,
                ),
              ),
              onPressed: () => onRemove(attachment),
              semanticLabel: 'Remove attachment ${index + 1}',
            ),
          ),
        ],
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .scale(begin: const Offset(0.8, 0.8), duration: 400.ms);
  }

  Widget _buildDocumentPreview(BuildContext context, String attachment) {
    final fileName = _getFileName(attachment);
    final fileExtension = _getFileExtension(attachment);

    return Container(
      color: AppTheme.backgroundColor,
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(fileExtension),
            color: AppTheme.primaryColor,
            size: 24,
          ),
          const SizedBox(height: 4),
          Text(
            fileExtension.toUpperCase(),
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.primaryColor,
              fontSize: 8,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  bool _isImageAttachment(String attachment) {
    final lowerCase = attachment.toLowerCase();
    return lowerCase.contains('http') && 
           (lowerCase.contains('.jpg') ||
            lowerCase.contains('.jpeg') ||
            lowerCase.contains('.png') ||
            lowerCase.contains('.gif') ||
            lowerCase.contains('images.unsplash.com'));
  }

  String _getFileName(String attachment) {
    if (attachment.contains('/')) {
      return attachment.split('/').last;
    }
    return attachment;
  }

  String _getFileExtension(String attachment) {
    final fileName = _getFileName(attachment);
    if (fileName.contains('.')) {
      return fileName.split('.').last;
    }
    return 'file';
  }

  IconData _getFileIcon(String extension) {
    switch (extension.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'doc':
      case 'docx':
        return Icons.description;
      case 'xls':
      case 'xlsx':
        return Icons.table_chart;
      case 'ppt':
      case 'pptx':
        return Icons.slideshow;
      case 'zip':
      case 'rar':
        return Icons.archive;
      case 'mp3':
      case 'wav':
        return Icons.audio_file;
      case 'mp4':
      case 'avi':
        return Icons.video_file;
      default:
        return Icons.insert_drive_file;
    }
  }
}
