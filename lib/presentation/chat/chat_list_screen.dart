import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';

/// Chat list screen showing conversations with tailors
class ChatListScreen extends StatefulWidget {
  const ChatListScreen({super.key});

  @override
  State<ChatListScreen> createState() => _ChatListScreenState();
}

class _ChatListScreenState extends State<ChatListScreen> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Messages'),
        backgroundColor: Colors.white,
        elevation: 0,
        scrolledUnderElevation: 1,
        actions: [
          IconButton(
            onPressed: () {
              // TODO: Start new conversation
            },
            icon: const Icon(Icons.edit_outlined),
          ),
        ],
      ),
      body: _buildEmptyState(),
    );
  }

  Widget _buildEmptyState() {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.chat_bubble_outline,
              size: 64,
              color: AppTheme.secondaryTextColor,
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            Text(
              'No Messages Yet',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryTextColor,
                  ),
            ),
            const SizedBox(height: AppConfig.smallPadding),
            Text(
              'Start a conversation with a tailor to see your messages here',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: AppConfig.largePadding),
            ElevatedButton.icon(
              onPressed: () {
                // TODO: Navigate to home or search
              },
              icon: const Icon(Icons.search),
              label: const Text('Find Tailors'),
            ),
          ],
        ),
      ),
    ).animate().fadeIn(duration: 600.ms);
  }
}
