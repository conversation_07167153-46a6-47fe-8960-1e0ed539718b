import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Social login buttons with Instagram-inspired design
class SocialLoginButtons extends StatelessWidget {
  final bool isLoading;
  final ValueChanged<bool> onLoadingChanged;

  const SocialLoginButtons({
    super.key,
    required this.isLoading,
    required this.onLoadingChanged,
  });

  Future<void> _handleGoogleLogin(BuildContext context) async {
    onLoadingChanged(true);

    try {
      // TODO: Implement Google login
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Google login coming soon!'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Google login failed: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      onLoadingChanged(false);
    }
  }

  Future<void> _handleAppleLogin(BuildContext context) async {
    onLoadingChanged(true);

    try {
      // TODO: Implement Apple login
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Apple login coming soon!'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Apple login failed: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      onLoadingChanged(false);
    }
  }

  Future<void> _handleFacebookLogin(BuildContext context) async {
    onLoadingChanged(true);

    try {
      // TODO: Implement Facebook login
      await Future.delayed(const Duration(seconds: 2)); // Simulate API call
      
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Facebook login coming soon!'),
            backgroundColor: AppTheme.primaryColor,
          ),
        );
      }
    } catch (e) {
      if (context.mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Facebook login failed: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    } finally {
      onLoadingChanged(false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Google Login Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: OutlinedButton.icon(
            onPressed: isLoading ? null : () => _handleGoogleLogin(context),
            icon: const Icon(
              Icons.g_mobiledata,
              color: AppTheme.primaryTextColor,
              size: 24,
            ),
            label: const Text(
              'Continue with Google',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryTextColor,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppTheme.dividerColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
            ),
          ),
        )
            .animate()
            .fadeIn(delay: 100.ms, duration: 600.ms)
            .slideY(
              begin: 0.3,
              end: 0,
              duration: 600.ms,
              delay: 100.ms,
            ),

        const SizedBox(height: AppConfig.defaultPadding),

        // Apple Login Button (iOS only)
        if (Theme.of(context).platform == TargetPlatform.iOS) ...[
          SizedBox(
            width: double.infinity,
            height: 56,
            child: OutlinedButton.icon(
              onPressed: isLoading ? null : () => _handleAppleLogin(context),
              icon: const Icon(
                Icons.apple,
                color: AppTheme.primaryTextColor,
                size: 24,
              ),
              label: const Text(
                'Continue with Apple',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              style: OutlinedButton.styleFrom(
                side: const BorderSide(color: AppTheme.dividerColor),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
              ),
            ),
          )
              .animate()
              .fadeIn(delay: 200.ms, duration: 600.ms)
              .slideY(
                begin: 0.3,
                end: 0,
                duration: 600.ms,
                delay: 200.ms,
              ),

          const SizedBox(height: AppConfig.defaultPadding),
        ],

        // Facebook Login Button
        SizedBox(
          width: double.infinity,
          height: 56,
          child: OutlinedButton.icon(
            onPressed: isLoading ? null : () => _handleFacebookLogin(context),
            icon: const Icon(
              Icons.facebook,
              color: Color(0xFF1877F2),
              size: 24,
            ),
            label: const Text(
              'Continue with Facebook',
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryTextColor,
              ),
            ),
            style: OutlinedButton.styleFrom(
              side: const BorderSide(color: AppTheme.dividerColor),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
            ),
          ),
        )
            .animate()
            .fadeIn(delay: 300.ms, duration: 600.ms)
            .slideY(
              begin: 0.3,
              end: 0,
              duration: 600.ms,
              delay: 300.ms,
            ),

        const SizedBox(height: AppConfig.largePadding),

        // Guest Login
        TextButton(
          onPressed: isLoading
              ? null
              : () {
                  // TODO: Navigate to home as guest
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('Guest mode coming soon!'),
                      backgroundColor: AppTheme.primaryColor,
                    ),
                  );
                },
          child: Text(
            'Continue as Guest',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.secondaryTextColor,
                  fontWeight: FontWeight.w600,
                ),
          ),
        )
            .animate()
            .fadeIn(delay: 400.ms, duration: 600.ms)
            .slideY(
              begin: 0.3,
              end: 0,
              duration: 600.ms,
              delay: 400.ms,
            ),
      ],
    );
  }
}
