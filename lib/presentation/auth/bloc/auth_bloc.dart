import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/user_model.dart';
import '../../../data/services/mock_auth_service.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthInitialize extends AuthEvent {
  const AuthInitialize();
}

class AuthLogin extends AuthEvent {
  final String email;
  final String password;

  const AuthLogin({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

class AuthRegister extends AuthEvent {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? phone;

  const AuthRegister({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
  });

  @override
  List<Object?> get props => [email, password, firstName, lastName, phone];
}

class AuthSocialLogin extends AuthEvent {
  final String provider;
  final String token;

  const AuthSocialLogin({
    required this.provider,
    required this.token,
  });

  @override
  List<Object?> get props => [provider, token];
}

class AuthLogout extends AuthEvent {
  const AuthLogout();
}

class AuthRefreshUser extends AuthEvent {
  const AuthRefreshUser();
}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final UserModel user;

  const AuthAuthenticated({required this.user});

  @override
  List<Object?> get props => [user];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

// BLoC
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final MockAuthService _authService;

  AuthBloc({MockAuthService? authService})
      : _authService = authService ?? MockAuthService(),
        super(const AuthInitial()) {
    on<AuthInitialize>(_onInitialize);
    on<AuthLogin>(_onLogin);
    on<AuthRegister>(_onRegister);
    on<AuthSocialLogin>(_onSocialLogin);
    on<AuthLogout>(_onLogout);
    on<AuthRefreshUser>(_onRefreshUser);
  }

  Future<void> _onInitialize(
    AuthInitialize event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      await _authService.initialize();
      
      final isLoggedIn = await _authService.isLoggedIn();
      if (isLoggedIn) {
        final user = await _authService.getStoredUser();
        if (user != null) {
          emit(AuthAuthenticated(user: user));
        } else {
          emit(const AuthUnauthenticated());
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: 'Initialization failed: ${e.toString()}'));
    }
  }

  Future<void> _onLogin(
    AuthLogin event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final response = await _authService.login(
        email: event.email,
        password: event.password,
      );

      if (response.success && response.data != null) {
        emit(AuthAuthenticated(user: response.data!.user));
      } else {
        emit(AuthError(
          message: response.error?.message ?? 'Login failed',
          errorCode: response.error?.code,
        ));
      }
    } catch (e) {
      emit(AuthError(message: 'Login failed: ${e.toString()}'));
    }
  }

  Future<void> _onRegister(
    AuthRegister event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final response = await _authService.register(
        email: event.email,
        password: event.password,
        firstName: event.firstName,
        lastName: event.lastName,
        phone: event.phone,
      );

      if (response.success && response.data != null) {
        emit(AuthAuthenticated(user: response.data!.user));
      } else {
        emit(AuthError(
          message: response.error?.message ?? 'Registration failed',
          errorCode: response.error?.code,
        ));
      }
    } catch (e) {
      emit(AuthError(message: 'Registration failed: ${e.toString()}'));
    }
  }

  Future<void> _onSocialLogin(
    AuthSocialLogin event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final response = await _authService.socialLogin(
        provider: event.provider,
        token: event.token,
      );

      if (response.success && response.data != null) {
        emit(AuthAuthenticated(user: response.data!.user));
      } else {
        emit(AuthError(
          message: response.error?.message ?? '${event.provider} login failed',
          errorCode: response.error?.code,
        ));
      }
    } catch (e) {
      emit(AuthError(message: '${event.provider} login failed: ${e.toString()}'));
    }
  }

  Future<void> _onLogout(
    AuthLogout event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _authService.logout();
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still go to unauthenticated state
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onRefreshUser(
    AuthRefreshUser event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      try {
        final response = await _authService.getCurrentUser();
        if (response.success && response.data != null) {
          emit(AuthAuthenticated(user: response.data!));
        }
        // If refresh fails, keep current state
      } catch (e) {
        // If refresh fails, keep current state
      }
    }
  }
}
