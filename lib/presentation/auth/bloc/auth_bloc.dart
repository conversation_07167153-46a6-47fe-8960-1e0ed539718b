import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/user_model.dart';
import '../../../data/services/api_service_manager.dart';

// Events
abstract class AuthEvent extends Equatable {
  const AuthEvent();

  @override
  List<Object?> get props => [];
}

class AuthInitialize extends AuthEvent {
  const AuthInitialize();
}

class AuthLogin extends AuthEvent {
  final String email;
  final String password;

  const AuthLogin({
    required this.email,
    required this.password,
  });

  @override
  List<Object?> get props => [email, password];
}

class AuthRegister extends AuthEvent {
  final String email;
  final String password;
  final String firstName;
  final String lastName;
  final String? phone;

  const AuthRegister({
    required this.email,
    required this.password,
    required this.firstName,
    required this.lastName,
    this.phone,
  });

  @override
  List<Object?> get props => [email, password, firstName, lastName, phone];
}

class AuthSocialLogin extends AuthEvent {
  final String provider;
  final String token;

  const AuthSocialLogin({
    required this.provider,
    required this.token,
  });

  @override
  List<Object?> get props => [provider, token];
}

class AuthLogout extends AuthEvent {
  const AuthLogout();
}

class AuthRefreshUser extends AuthEvent {
  const AuthRefreshUser();
}

// States
abstract class AuthState extends Equatable {
  const AuthState();

  @override
  List<Object?> get props => [];
}

class AuthInitial extends AuthState {
  const AuthInitial();
}

class AuthLoading extends AuthState {
  const AuthLoading();
}

class AuthAuthenticated extends AuthState {
  final UserModel user;

  const AuthAuthenticated({required this.user});

  @override
  List<Object?> get props => [user];
}

class AuthUnauthenticated extends AuthState {
  const AuthUnauthenticated();
}

class AuthError extends AuthState {
  final String message;
  final String? errorCode;

  const AuthError({
    required this.message,
    this.errorCode,
  });

  @override
  List<Object?> get props => [message, errorCode];
}

// BLoC
class AuthBloc extends Bloc<AuthEvent, AuthState> {
  final ApiServiceManager _apiService;

  AuthBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const AuthInitial()) {
    on<AuthInitialize>(_onInitialize);
    on<AuthLogin>(_onLogin);
    on<AuthRegister>(_onRegister);
    on<AuthSocialLogin>(_onSocialLogin);
    on<AuthLogout>(_onLogout);
    on<AuthRefreshUser>(_onRefreshUser);
  }

  Future<void> _onInitialize(
    AuthInitialize event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final isLoggedIn = await _apiService.isAuthenticated();
      if (isLoggedIn) {
        // Try to get current user from API
        final response = await _apiService.user.getCurrentUser();
        if (response.success && response.data != null) {
          // Convert API response to UserModel
          final user = UserModel.fromJson(response.data!);
          emit(AuthAuthenticated(user: user));
        } else {
          emit(const AuthUnauthenticated());
        }
      } else {
        emit(const AuthUnauthenticated());
      }
    } catch (e) {
      emit(AuthError(message: 'Initialization failed: ${e.toString()}'));
    }
  }

  Future<void> _onLogin(
    AuthLogin event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final success = await _apiService.login(
        email: event.email,
        password: event.password,
      );

      if (success) {
        // Get user data after successful login
        final response = await _apiService.user.getCurrentUser();
        if (response.success && response.data != null) {
          final user = UserModel.fromJson(response.data!);
          emit(AuthAuthenticated(user: user));
        } else {
          emit(const AuthError(message: 'Failed to get user data'));
        }
      } else {
        emit(const AuthError(message: 'Login failed'));
      }
    } catch (e) {
      emit(AuthError(message: 'Login failed: ${e.toString()}'));
    }
  }

  Future<void> _onRegister(
    AuthRegister event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      final success = await _apiService.register(
        name: '${event.firstName} ${event.lastName}',
        email: event.email,
        password: event.password,
        phone: event.phone ?? '',
      );

      if (success) {
        // Get user data after successful registration
        final response = await _apiService.user.getCurrentUser();
        if (response.success && response.data != null) {
          final user = UserModel.fromJson(response.data!);
          emit(AuthAuthenticated(user: user));
        } else {
          emit(const AuthError(message: 'Failed to get user data'));
        }
      } else {
        emit(const AuthError(message: 'Registration failed'));
      }
    } catch (e) {
      emit(AuthError(message: 'Registration failed: ${e.toString()}'));
    }
  }

  Future<void> _onSocialLogin(
    AuthSocialLogin event,
    Emitter<AuthState> emit,
  ) async {
    emit(const AuthLoading());

    try {
      // Social login not implemented in API service manager yet
      // For now, emit error
      emit(AuthError(message: '${event.provider} login not implemented yet'));
    } catch (e) {
      emit(AuthError(message: '${event.provider} login failed: ${e.toString()}'));
    }
  }

  Future<void> _onLogout(
    AuthLogout event,
    Emitter<AuthState> emit,
  ) async {
    try {
      await _apiService.logout();
      emit(const AuthUnauthenticated());
    } catch (e) {
      // Even if logout fails, we should still go to unauthenticated state
      emit(const AuthUnauthenticated());
    }
  }

  Future<void> _onRefreshUser(
    AuthRefreshUser event,
    Emitter<AuthState> emit,
  ) async {
    if (state is AuthAuthenticated) {
      try {
        final response = await _apiService.user.getCurrentUser();
        if (response.success && response.data != null) {
          final user = UserModel.fromJson(response.data!);
          emit(AuthAuthenticated(user: user));
        }
        // If refresh fails, keep current state
      } catch (e) {
        // If refresh fails, keep current state
      }
    }
  }
}
