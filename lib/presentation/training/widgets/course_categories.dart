import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/training_bloc.dart';

/// Course categories horizontal list
class CourseCategories extends StatefulWidget {
  final List<CourseCategory> categories;
  final Function(String) onCategorySelected;

  const CourseCategories({
    super.key,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  State<CourseCategories> createState() => _CourseCategoriesState();
}

class _CourseCategoriesState extends State<CourseCategories> {
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Course Categories',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.categories.length,
            itemBuilder: (context, index) {
              final category = widget.categories[index];
              final isSelected = _selectedCategory == category.id;
              
              return _buildCategoryCard(category, isSelected, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    CourseCategory category,
    bool isSelected,
    int index,
  ) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleButton(
        child: Column(
          children: [
            // Category Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: isSelected ? AppTheme.instagramGradient : null,
                color: isSelected ? null : AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? Colors.transparent 
                      : AppTheme.dividerColor,
                  width: 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: AppTheme.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                _getCategoryIcon(category.icon),
                color: isSelected ? Colors.white : AppTheme.primaryColor,
                size: 24,
              ),
            ),

            const SizedBox(height: 8),

            // Category Name
            Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected 
                    ? AppTheme.primaryColor 
                    : AppTheme.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        onPressed: () {
          setState(() {
            _selectedCategory = isSelected ? null : category.id;
          });
          
          if (!isSelected) {
            widget.onCategorySelected(category.name);
          }
        },
        semanticLabel: 'Select ${category.name} category',
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'scissors':
        return Icons.content_cut;
      case 'star':
        return Icons.star;
      case 'grid':
        return Icons.grid_on;
      case 'business':
        return Icons.business;
      case 'palette':
        return Icons.palette;
      default:
        return Icons.category;
    }
  }
}
