import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/training_bloc.dart';

/// My enrolled courses list
class MyCourses extends StatelessWidget {
  final List<Course> courses;
  final Function(Course) onCourseSelected;
  final Function(Course) onContinueLearning;

  const MyCourses({
    super.key,
    required this.courses,
    required this.onCourseSelected,
    required this.onContinueLearning,
  });

  @override
  Widget build(BuildContext context) {
    if (courses.isEmpty) {
      return _buildEmptyState(context);
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'My Courses',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: courses.length,
          itemBuilder: (context, index) {
            final course = courses[index];
            return _buildMyCourseCard(context, course, index);
          },
        ),
      ],
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.school_outlined,
            size: 64,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No Enrolled Courses',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start learning by enrolling in a course',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/all-courses');
            },
            child: const Text('Browse Courses'),
          ),
        ],
      ),
    );
  }

  Widget _buildMyCourseCard(BuildContext context, Course course, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleButton(
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Row(
              children: [
                // Course Image
                ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: AccessibilityHelper.accessibleImage(
                    image: CachedNetworkImageProvider(course.imageUrl),
                    semanticLabel: 'Course image for ${course.title}',
                    width: 80,
                    height: 80,
                    fit: BoxFit.cover,
                  ),
                ),

                const SizedBox(width: AppConfig.defaultPadding),

                // Course Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Course Title
                      Text(
                        course.title,
                        style: Theme.of(context).textTheme.titleSmall?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                        maxLines: 2,
                        overflow: TextOverflow.ellipsis,
                      ),

                      const SizedBox(height: 4),

                      // Instructor
                      Text(
                        'by ${course.instructor}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),

                      const SizedBox(height: 8),

                      // Progress Bar
                      Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            mainAxisAlignment: MainAxisAlignment.spaceBetween,
                            children: [
                              Text(
                                'Progress',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.secondaryTextColor,
                                ),
                              ),
                              Text(
                                '${(course.progress * 100).toInt()}%',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                  color: AppTheme.primaryColor,
                                ),
                              ),
                            ],
                          ),
                          const SizedBox(height: 4),
                          ClipRRect(
                            borderRadius: BorderRadius.circular(4),
                            child: LinearProgressIndicator(
                              value: course.progress,
                              backgroundColor: AppTheme.dividerColor,
                              valueColor: AlwaysStoppedAnimation<Color>(
                                AppTheme.primaryColor,
                              ),
                              minHeight: 6,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Action Button
                      SizedBox(
                        width: double.infinity,
                        child: ElevatedButton(
                          onPressed: () => onContinueLearning(course),
                          style: ElevatedButton.styleFrom(
                            backgroundColor: course.isCompleted
                                ? AppTheme.successColor
                                : AppTheme.primaryColor,
                            padding: const EdgeInsets.symmetric(vertical: 8),
                          ),
                          child: Text(
                            course.isCompleted ? 'Completed' : 'Continue Learning',
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 12,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),
        ),
        onPressed: () => onCourseSelected(course),
        semanticLabel: 'View course: ${course.title}',
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }
}
