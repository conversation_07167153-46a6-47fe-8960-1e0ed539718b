import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';


import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/training_bloc.dart';

/// Certification badges grid
class CertificationBadges extends StatelessWidget {
  final List<Certificate> certificates;
  final Function(Certificate) onCertificateSelected;

  const CertificationBadges({
    super.key,
    required this.certificates,
    required this.onCertificateSelected,
  });

  @override
  Widget build(BuildContext context) {
    if (certificates.isEmpty) {
      return _buildEmptyState(context);
    }

    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: AppConfig.defaultPadding,
        mainAxisSpacing: AppConfig.defaultPadding,
        childAspectRatio: 1.2,
      ),
      itemCount: certificates.length,
      itemBuilder: (context, index) {
        final certificate = certificates[index];
        return _buildCertificateBadge(context, certificate, index);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largePadding),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: AppTheme.dividerColor,
          style: BorderStyle.solid,
        ),
      ),
      child: Column(
        children: [
          Icon(
            Icons.verified_outlined,
            size: 48,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No Certificates Yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Complete courses to earn certificates',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildCertificateBadge(
    BuildContext context,
    Certificate certificate,
    int index,
  ) {
    return AccessibilityHelper.accessibleButton(
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              AppTheme.successColor,
              AppTheme.successColor.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          boxShadow: [
            BoxShadow(
              color: AppTheme.successColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 4),
            ),
          ],
        ),
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // Certificate Icon
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.verified,
                  color: Colors.white,
                  size: 28,
                ),
              ),

              const SizedBox(height: 12),

              // Certificate Title
              Text(
                certificate.title,
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),

              const SizedBox(height: 4),

              // Issue Date
              Text(
                'Issued ${_formatDate(certificate.issueDate)}',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white.withValues(alpha: 0.9),
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 8),

              // Credential ID
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 8,
                  vertical: 4,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  certificate.credentialId,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.white,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
      onPressed: () => onCertificateSelected(certificate),
      semanticLabel: 'View certificate: ${certificate.title}',
    )
        .animate(delay: (index * 150).ms)
        .fadeIn(duration: 600.ms)
        .scale(begin: const Offset(0.8, 0.8), duration: 600.ms);
  }

  String _formatDate(DateTime date) {
    final months = [
      'Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
      'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'
    ];
    return '${months[date.month - 1]} ${date.year}';
  }
}
