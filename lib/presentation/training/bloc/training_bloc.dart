import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/services/api_service_manager.dart';
import '../../../data/models/user_model.dart';


// Events
abstract class TrainingEvent extends Equatable {
  const TrainingEvent();

  @override
  List<Object?> get props => [];
}

class LoadTrainingData extends TrainingEvent {
  const LoadTrainingData();
}

class FilterCoursesByCategory extends TrainingEvent {
  final String category;

  const FilterCoursesByCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class EnrollInCourse extends TrainingEvent {
  final String courseId;

  const EnrollInCourse({required this.courseId});

  @override
  List<Object?> get props => [courseId];
}

class UpdateCourseProgress extends TrainingEvent {
  final String courseId;
  final double progress;

  const UpdateCourseProgress({
    required this.courseId,
    required this.progress,
  });

  @override
  List<Object?> get props => [courseId, progress];
}

class CompleteCourse extends TrainingEvent {
  final String courseId;

  const CompleteCourse({required this.courseId});

  @override
  List<Object?> get props => [courseId];
}

// States
abstract class TrainingState extends Equatable {
  const TrainingState();

  @override
  List<Object?> get props => [];
}

class TrainingInitial extends TrainingState {
  const TrainingInitial();
}

class TrainingLoading extends TrainingState {
  const TrainingLoading();
}

class TrainingLoaded extends TrainingState {
  final UserModel user;
  final List<CourseCategory> categories;
  final List<Course> featuredCourses;
  final List<Course> allCourses;
  final List<Course> myCourses;
  final List<Certificate> certificates;
  final List<Certification> availableCertifications;
  final double overallProgress;
  final int completedCourses;
  final int totalCourses;
  final int enrolledCourses;
  final double totalLearningHours;

  const TrainingLoaded({
    required this.user,
    required this.categories,
    required this.featuredCourses,
    required this.allCourses,
    required this.myCourses,
    required this.certificates,
    required this.availableCertifications,
    required this.overallProgress,
    required this.completedCourses,
    required this.totalCourses,
    required this.enrolledCourses,
    required this.totalLearningHours,
  });

  @override
  List<Object?> get props => [
        user,
        categories,
        featuredCourses,
        allCourses,
        myCourses,
        certificates,
        availableCertifications,
        overallProgress,
        completedCourses,
        totalCourses,
        enrolledCourses,
        totalLearningHours,
      ];

  TrainingLoaded copyWith({
    UserModel? user,
    List<CourseCategory>? categories,
    List<Course>? featuredCourses,
    List<Course>? allCourses,
    List<Course>? myCourses,
    List<Certificate>? certificates,
    List<Certification>? availableCertifications,
    double? overallProgress,
    int? completedCourses,
    int? totalCourses,
    int? enrolledCourses,
    double? totalLearningHours,
  }) {
    return TrainingLoaded(
      user: user ?? this.user,
      categories: categories ?? this.categories,
      featuredCourses: featuredCourses ?? this.featuredCourses,
      allCourses: allCourses ?? this.allCourses,
      myCourses: myCourses ?? this.myCourses,
      certificates: certificates ?? this.certificates,
      availableCertifications: availableCertifications ?? this.availableCertifications,
      overallProgress: overallProgress ?? this.overallProgress,
      completedCourses: completedCourses ?? this.completedCourses,
      totalCourses: totalCourses ?? this.totalCourses,
      enrolledCourses: enrolledCourses ?? this.enrolledCourses,
      totalLearningHours: totalLearningHours ?? this.totalLearningHours,
    );
  }
}

class TrainingError extends TrainingState {
  final String message;

  const TrainingError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class TrainingBloc extends Bloc<TrainingEvent, TrainingState> {
  final ApiServiceManager _apiService;

  TrainingBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const TrainingInitial()) {
    on<LoadTrainingData>(_onLoadTrainingData);
    on<FilterCoursesByCategory>(_onFilterCoursesByCategory);
    on<EnrollInCourse>(_onEnrollInCourse);
    on<UpdateCourseProgress>(_onUpdateCourseProgress);
    on<CompleteCourse>(_onCompleteCourse);
  }

  Future<void> _onLoadTrainingData(
    LoadTrainingData event,
    Emitter<TrainingState> emit,
  ) async {
    emit(const TrainingLoading());

    try {
      // For now, use mock data but structure it to be API-ready
      // TODO: Replace with real API calls when backend is ready
      final user = await _getMockUser();
      final categories = await _getMockCategories();
      final featuredCourses = await _getMockFeaturedCourses();
      final allCourses = await _getMockAllCourses();
      final myCourses = await _getMockMyCourses();
      final certificates = await _getMockCertificates();
      final availableCertifications = await _getMockAvailableCertifications();

      // Future API integration would look like:
      // try {
      //   final userResponse = await _apiService.user.getCurrentUser();
      //   if (userResponse.success) {
      //     user = UserModel.fromJson(userResponse.data!);
      //   }
      // } catch (e) {
      //   // Fallback to mock data
      // }

      // Calculate progress
      final completedCourses = myCourses.where((c) => c.isCompleted).length;
      final totalCourses = allCourses.length;
      final enrolledCourses = myCourses.length;
      final overallProgress = completedCourses / totalCourses;
      final totalLearningHours = myCourses.fold<double>(
        0.0,
        (sum, course) => sum + (course.completedHours ?? 0.0),
      );

      emit(TrainingLoaded(
        user: user,
        categories: categories,
        featuredCourses: featuredCourses,
        allCourses: allCourses,
        myCourses: myCourses,
        certificates: certificates,
        availableCertifications: availableCertifications,
        overallProgress: overallProgress,
        completedCourses: completedCourses,
        totalCourses: totalCourses,
        enrolledCourses: enrolledCourses,
        totalLearningHours: totalLearningHours,
      ));
    } catch (e) {
      emit(TrainingError(message: e.toString()));
    }
  }

  Future<void> _onFilterCoursesByCategory(
    FilterCoursesByCategory event,
    Emitter<TrainingState> emit,
  ) async {
    if (state is TrainingLoaded) {
      final currentState = state as TrainingLoaded;
      
      // Filter courses by category
      final filteredCourses = currentState.allCourses
          .where((course) => course.category == event.category)
          .toList();

      emit(currentState.copyWith(allCourses: filteredCourses));
    }
  }

  Future<void> _onEnrollInCourse(
    EnrollInCourse event,
    Emitter<TrainingState> emit,
  ) async {
    if (state is TrainingLoaded) {
      final currentState = state as TrainingLoaded;
      
      try {
        // Find the course and enroll
        final course = currentState.allCourses
            .firstWhere((c) => c.id == event.courseId);
        
        final enrolledCourse = course.copyWith(
          isEnrolled: true,
          enrollmentDate: DateTime.now(),
        );

        final updatedMyCourses = [...currentState.myCourses, enrolledCourse];
        
        emit(currentState.copyWith(
          myCourses: updatedMyCourses,
          enrolledCourses: updatedMyCourses.length,
        ));
      } catch (e) {
        emit(TrainingError(message: 'Failed to enroll in course'));
      }
    }
  }

  Future<void> _onUpdateCourseProgress(
    UpdateCourseProgress event,
    Emitter<TrainingState> emit,
  ) async {
    if (state is TrainingLoaded) {
      final currentState = state as TrainingLoaded;
      
      final updatedMyCourses = currentState.myCourses.map((course) {
        if (course.id == event.courseId) {
          return course.copyWith(progress: event.progress);
        }
        return course;
      }).toList();

      emit(currentState.copyWith(myCourses: updatedMyCourses));
    }
  }

  Future<void> _onCompleteCourse(
    CompleteCourse event,
    Emitter<TrainingState> emit,
  ) async {
    if (state is TrainingLoaded) {
      final currentState = state as TrainingLoaded;
      
      final updatedMyCourses = currentState.myCourses.map((course) {
        if (course.id == event.courseId) {
          return course.copyWith(
            isCompleted: true,
            progress: 1.0,
            completionDate: DateTime.now(),
          );
        }
        return course;
      }).toList();

      final completedCount = updatedMyCourses.where((c) => c.isCompleted).length;
      final newProgress = completedCount / currentState.totalCourses;

      emit(currentState.copyWith(
        myCourses: updatedMyCourses,
        completedCourses: completedCount,
        overallProgress: newProgress,
      ));
    }
  }

  // Mock data methods
  Future<UserModel> _getMockUser() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return UserModel(
      id: 'user_1',
      email: '<EMAIL>',
      firstName: 'Master',
      lastName: 'Tailor',
      phone: '+976 9999 9999',
      role: UserRole.tailor,
      isEmailVerified: true,
      isPhoneVerified: true,
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  Future<List<CourseCategory>> _getMockCategories() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      CourseCategory(id: '1', name: 'Basic Tailoring', icon: 'scissors'),
      CourseCategory(id: '2', name: 'Advanced Techniques', icon: 'star'),
      CourseCategory(id: '3', name: 'Pattern Making', icon: 'grid'),
      CourseCategory(id: '4', name: 'Business Skills', icon: 'business'),
      CourseCategory(id: '5', name: 'Fashion Design', icon: 'palette'),
    ];
  }

  Future<List<Course>> _getMockFeaturedCourses() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      Course(
        id: 'course_1',
        title: 'Master Suit Construction',
        description: 'Learn professional suit making techniques',
        category: 'Advanced Techniques',
        instructor: 'Master John Smith',
        duration: 8.5,
        rating: 4.8,
        studentCount: 1250,
        price: 299.99,
        imageUrl: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=300',
        isFeatured: true,
      ),
      Course(
        id: 'course_2',
        title: 'Pattern Making Fundamentals',
        description: 'Create professional patterns from scratch',
        category: 'Pattern Making',
        instructor: 'Sarah Johnson',
        duration: 6.0,
        rating: 4.9,
        studentCount: 890,
        price: 199.99,
        imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300',
        isFeatured: true,
      ),
    ];
  }

  Future<List<Course>> _getMockAllCourses() async {
    await Future.delayed(const Duration(milliseconds: 500));
    return [
      // Include featured courses plus more
      ...(await _getMockFeaturedCourses()),
      Course(
        id: 'course_3',
        title: 'Tailoring Business Basics',
        description: 'Start and grow your tailoring business',
        category: 'Business Skills',
        instructor: 'David Wilson',
        duration: 4.0,
        rating: 4.6,
        studentCount: 567,
        price: 149.99,
        imageUrl: 'https://images.unsplash.com/photo-1556742049-0cfed4f6a45d?w=300',
      ),
      Course(
        id: 'course_4',
        title: 'Hand Sewing Mastery',
        description: 'Perfect your hand sewing techniques',
        category: 'Basic Tailoring',
        instructor: 'Maria Garcia',
        duration: 3.5,
        rating: 4.7,
        studentCount: 432,
        price: 99.99,
        imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300',
      ),
    ];
  }

  Future<List<Course>> _getMockMyCourses() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return [
      Course(
        id: 'course_1',
        title: 'Master Suit Construction',
        description: 'Learn professional suit making techniques',
        category: 'Advanced Techniques',
        instructor: 'Master John Smith',
        duration: 8.5,
        rating: 4.8,
        studentCount: 1250,
        price: 299.99,
        imageUrl: 'https://images.unsplash.com/photo-1594938298603-c8148c4dae35?w=300',
        isEnrolled: true,
        progress: 0.65,
        completedHours: 5.5,
        enrollmentDate: DateTime.now().subtract(const Duration(days: 15)),
      ),
    ];
  }

  Future<List<Certificate>> _getMockCertificates() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      Certificate(
        id: 'cert_1',
        title: 'Basic Tailoring Certification',
        issueDate: DateTime.now().subtract(const Duration(days: 30)),
        expiryDate: DateTime.now().add(const Duration(days: 365)),
        credentialId: 'TL-BASIC-2024-001',
        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',
      ),
    ];
  }

  Future<List<Certification>> _getMockAvailableCertifications() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      Certification(
        id: 'cert_prog_1',
        title: 'Advanced Tailoring Master',
        description: 'Complete advanced tailoring certification program',
        requirements: ['Complete 5 advanced courses', 'Pass practical exam'],
        duration: '3 months',
        price: 599.99,
      ),
      Certification(
        id: 'cert_prog_2',
        title: 'Pattern Making Expert',
        description: 'Specialized certification in pattern making',
        requirements: ['Complete pattern making courses', 'Submit portfolio'],
        duration: '2 months',
        price: 399.99,
      ),
    ];
  }
}

// Models
class CourseCategory extends Equatable {
  final String id;
  final String name;
  final String icon;

  const CourseCategory({
    required this.id,
    required this.name,
    required this.icon,
  });

  @override
  List<Object?> get props => [id, name, icon];
}

class Course extends Equatable {
  final String id;
  final String title;
  final String description;
  final String category;
  final String instructor;
  final double duration;
  final double rating;
  final int studentCount;
  final double price;
  final String imageUrl;
  final bool isFeatured;
  final bool isEnrolled;
  final double progress;
  final double? completedHours;
  final DateTime? enrollmentDate;
  final DateTime? completionDate;
  final bool isCompleted;

  const Course({
    required this.id,
    required this.title,
    required this.description,
    required this.category,
    required this.instructor,
    required this.duration,
    required this.rating,
    required this.studentCount,
    required this.price,
    required this.imageUrl,
    this.isFeatured = false,
    this.isEnrolled = false,
    this.progress = 0.0,
    this.completedHours,
    this.enrollmentDate,
    this.completionDate,
    this.isCompleted = false,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        category,
        instructor,
        duration,
        rating,
        studentCount,
        price,
        imageUrl,
        isFeatured,
        isEnrolled,
        progress,
        completedHours,
        enrollmentDate,
        completionDate,
        isCompleted,
      ];

  Course copyWith({
    String? id,
    String? title,
    String? description,
    String? category,
    String? instructor,
    double? duration,
    double? rating,
    int? studentCount,
    double? price,
    String? imageUrl,
    bool? isFeatured,
    bool? isEnrolled,
    double? progress,
    double? completedHours,
    DateTime? enrollmentDate,
    DateTime? completionDate,
    bool? isCompleted,
  }) {
    return Course(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      category: category ?? this.category,
      instructor: instructor ?? this.instructor,
      duration: duration ?? this.duration,
      rating: rating ?? this.rating,
      studentCount: studentCount ?? this.studentCount,
      price: price ?? this.price,
      imageUrl: imageUrl ?? this.imageUrl,
      isFeatured: isFeatured ?? this.isFeatured,
      isEnrolled: isEnrolled ?? this.isEnrolled,
      progress: progress ?? this.progress,
      completedHours: completedHours ?? this.completedHours,
      enrollmentDate: enrollmentDate ?? this.enrollmentDate,
      completionDate: completionDate ?? this.completionDate,
      isCompleted: isCompleted ?? this.isCompleted,
    );
  }
}

class Certificate extends Equatable {
  final String id;
  final String title;
  final DateTime issueDate;
  final DateTime expiryDate;
  final String credentialId;
  final String imageUrl;

  const Certificate({
    required this.id,
    required this.title,
    required this.issueDate,
    required this.expiryDate,
    required this.credentialId,
    required this.imageUrl,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        issueDate,
        expiryDate,
        credentialId,
        imageUrl,
      ];
}

class Certification extends Equatable {
  final String id;
  final String title;
  final String description;
  final List<String> requirements;
  final String duration;
  final double price;

  const Certification({
    required this.id,
    required this.title,
    required this.description,
    required this.requirements,
    required this.duration,
    required this.price,
  });

  @override
  List<Object?> get props => [
        id,
        title,
        description,
        requirements,
        duration,
        price,
      ];
}
