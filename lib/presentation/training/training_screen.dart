import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';
import 'bloc/training_bloc.dart';
import 'widgets/training_header.dart';
import 'widgets/course_categories.dart';
import 'widgets/featured_courses.dart';
import 'widgets/my_courses.dart';
import 'widgets/progress_overview.dart';
import 'widgets/certification_badges.dart';

/// Training and skill development screen for tailors
class TrainingScreen extends StatefulWidget {
  const TrainingScreen({super.key});

  @override
  State<TrainingScreen> createState() => _TrainingScreenState();
}

class _TrainingScreenState extends State<TrainingScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TrainingBloc()..add(const LoadTrainingData()),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: SafeArea(
          child: BlocBuilder<TrainingBloc, TrainingState>(
            builder: (context, state) {
              if (state is TrainingLoading) {
                return _buildLoadingState();
              }

              if (state is TrainingError) {
                return _buildErrorState(state.message);
              }

              if (state is TrainingLoaded) {
                return _buildLoadedState(context, state);
              }

              return _buildInitialState();
            },
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Error Loading Training',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          ElevatedButton(
            onPressed: () {
              context.read<TrainingBloc>().add(const LoadTrainingData());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return const Center(
      child: Text('Welcome to Training Center'),
    );
  }

  Widget _buildLoadedState(BuildContext context, TrainingLoaded state) {
    return Column(
      children: [
        // Header with progress overview
        TrainingHeader(
          user: state.user,
          overallProgress: state.overallProgress,
          completedCourses: state.completedCourses,
          totalCourses: state.totalCourses,
        ),

        // Tab Bar
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _tabController,
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.secondaryTextColor,
            indicatorColor: AppTheme.primaryColor,
            tabs: const [
              Tab(text: 'Courses'),
              Tab(text: 'My Learning'),
              Tab(text: 'Certificates'),
              Tab(text: 'Community'),
            ],
          ),
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildCoursesTab(state),
              _buildMyLearningTab(state),
              _buildCertificatesTab(state),
              _buildCommunityTab(state),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildCoursesTab(TrainingLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Course Categories
          CourseCategories(
            categories: state.categories,
            onCategorySelected: (category) {
              context.read<TrainingBloc>().add(
                    FilterCoursesByCategory(category: category),
                  );
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Featured Courses
          FeaturedCourses(
            courses: state.featuredCourses,
            onCourseSelected: (course) {
              Navigator.pushNamed(
                context,
                '/course-details',
                arguments: course,
              );
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // All Courses
          Text(
            'All Courses',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: state.allCourses.length,
            itemBuilder: (context, index) {
              final course = state.allCourses[index];
              return _buildCourseCard(course, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyLearningTab(TrainingLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Progress Overview
          ProgressOverview(
            enrolledCourses: state.enrolledCourses,
            completedCourses: state.completedCourses,
            totalHours: state.totalLearningHours,
          ),

          const SizedBox(height: AppConfig.largePadding),

          // My Courses
          MyCourses(
            courses: state.myCourses,
            onCourseSelected: (course) {
              Navigator.pushNamed(
                context,
                '/course-player',
                arguments: course,
              );
            },
            onContinueLearning: (course) {
              Navigator.pushNamed(
                context,
                '/course-player',
                arguments: course,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCertificatesTab(TrainingLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'My Certificates',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          // Certification Badges
          CertificationBadges(
            certificates: state.certificates,
            onCertificateSelected: (certificate) {
              Navigator.pushNamed(
                context,
                '/certificate-details',
                arguments: certificate,
              );
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Available Certifications
          Text(
            'Available Certifications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: state.availableCertifications.length,
            itemBuilder: (context, index) {
              final certification = state.availableCertifications[index];
              return _buildCertificationCard(certification, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildCommunityTab(TrainingLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Learning Community',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          // Community features would go here
          _buildCommunityFeatures(),
        ],
      ),
    );
  }

  Widget _buildCourseCard(dynamic course, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Card(
        elevation: 2,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(
              Icons.play_arrow,
              color: Colors.white,
            ),
          ),
          title: Text(course.title ?? 'Course Title'),
          subtitle: Text(course.description ?? 'Course Description'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            Navigator.pushNamed(
              context,
              '/course-details',
              arguments: course,
            );
          },
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildCertificationCard(dynamic certification, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Card(
        elevation: 2,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: AppTheme.successColor,
            child: Icon(
              Icons.verified,
              color: Colors.white,
            ),
          ),
          title: Text(certification.title ?? 'Certification Title'),
          subtitle: Text(certification.description ?? 'Certification Description'),
          trailing: const Icon(Icons.arrow_forward_ios),
          onTap: () {
            Navigator.pushNamed(
              context,
              '/certification-details',
              arguments: certification,
            );
          },
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildCommunityFeatures() {
    return Column(
      children: [
        _buildCommunityFeature(
          icon: Icons.forum,
          title: 'Discussion Forums',
          description: 'Connect with fellow tailors and share experiences',
          onTap: () {
            Navigator.pushNamed(context, '/forums');
          },
        ),
        _buildCommunityFeature(
          icon: Icons.group,
          title: 'Study Groups',
          description: 'Join or create study groups for collaborative learning',
          onTap: () {
            Navigator.pushNamed(context, '/study-groups');
          },
        ),
        _buildCommunityFeature(
          icon: Icons.live_help,
          title: 'Ask Experts',
          description: 'Get answers from experienced master tailors',
          onTap: () {
            Navigator.pushNamed(context, '/ask-experts');
          },
        ),
      ],
    );
  }

  Widget _buildCommunityFeature({
    required IconData icon,
    required String title,
    required String description,
    required VoidCallback onTap,
  }) {
    return Card(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: AppTheme.primaryColor.withValues(alpha: 0.1),
          child: Icon(
            icon,
            color: AppTheme.primaryColor,
          ),
        ),
        title: Text(title),
        subtitle: Text(description),
        trailing: const Icon(Icons.arrow_forward_ios),
        onTap: onTap,
      ),
    );
  }
}
