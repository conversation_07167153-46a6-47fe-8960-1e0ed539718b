import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import 'widgets/home_app_bar.dart';
import 'widgets/search_bar_widget.dart';
import 'widgets/category_chips.dart';
import 'widgets/featured_tailors.dart';
import 'widgets/nearby_tailors.dart';
import 'widgets/quick_actions.dart';

/// Instagram-inspired home screen with tailor discovery
class HomeScreen extends StatefulWidget {
  const HomeScreen({super.key});

  @override
  State<HomeScreen> createState() => _HomeScreenState();
}

class _HomeScreenState extends State<HomeScreen> {
  final ScrollController _scrollController = ScrollController();
  bool _isSearchFocused = false;

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  void _onSearchFocusChanged(bool focused) {
    setState(() {
      _isSearchFocused = focused;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // Custom App Bar
          HomeAppBar(
            isSearchFocused: _isSearchFocused,
          ),

          // Search Bar
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: SearchBarWidget(
                onFocusChanged: _onSearchFocusChanged,
              ),
            ).animate().fadeIn(delay: 100.ms, duration: 600.ms),
          ),

          // Quick Actions
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding,
              ),
              child: const QuickActions(),
            ).animate().fadeIn(delay: 200.ms, duration: 600.ms),
          ),

          // Category Chips
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                vertical: AppConfig.defaultPadding,
              ),
              child: const CategoryChips(),
            ).animate().fadeIn(delay: 300.ms, duration: 600.ms),
          ),

          // Featured Tailors Section
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Featured Tailors',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/search',
                            arguments: {'category': 'featured'},
                          );
                        },
                        child: Text(
                          'See All',
                          style: TextStyle(
                            color: AppTheme.primaryColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConfig.defaultPadding),
                  const FeaturedTailors(),
                ],
              ),
            ).animate().fadeIn(delay: 400.ms, duration: 600.ms),
          ),

          const SliverToBoxAdapter(
            child: SizedBox(height: AppConfig.largePadding),
          ),

          // Nearby Tailors Section
          SliverToBoxAdapter(
            child: Padding(
              padding: const EdgeInsets.symmetric(
                horizontal: AppConfig.defaultPadding,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Nearby Tailors',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                      ),
                      TextButton(
                        onPressed: () {
                          Navigator.pushNamed(
                            context,
                            '/map',
                            arguments: {'type': 'nearby'},
                          );
                        },
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.map_outlined,
                              size: 16,
                              color: AppTheme.primaryColor,
                            ),
                            const SizedBox(width: 4),
                            Text(
                              'Map View',
                              style: TextStyle(
                                color: AppTheme.primaryColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: AppConfig.defaultPadding),
                ],
              ),
            ).animate().fadeIn(delay: 500.ms, duration: 600.ms),
          ),

          // Nearby Tailors List
          const NearbyTailors(),

          // Bottom Padding
          const SliverToBoxAdapter(
            child: SizedBox(height: AppConfig.largePadding * 2),
          ),
        ],
      ),
    );
  }
}
