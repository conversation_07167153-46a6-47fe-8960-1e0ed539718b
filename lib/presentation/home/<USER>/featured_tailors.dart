import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Featured tailors horizontal list
class FeaturedTailors extends StatelessWidget {
  const FeaturedTailors({super.key});

  @override
  Widget build(BuildContext context) {
    // Mock data - TODO: Replace with real data
    final tailors = [
      FeaturedTailor(
        id: '1',
        name: 'Master <PERSON>',
        specialty: 'Traditional Suits',
        rating: 4.9,
        reviewCount: 127,
        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=300&h=300&fit=crop&crop=face',
        isVerified: true,
        distance: '0.8 km',
      ),
      FeaturedTailor(
        id: '2',
        name: '<PERSON>',
        specialty: 'Wedding Dresses',
        rating: 4.8,
        reviewCount: 89,
        imageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=300&h=300&fit=crop&crop=face',
        isVerified: true,
        distance: '1.2 km',
      ),
      FeaturedTailor(
        id: '3',
        name: '<PERSON>',
        specialty: 'Casual Wear',
        rating: 4.7,
        reviewCount: 156,
        imageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=300&h=300&fit=crop&crop=face',
        isVerified: false,
        distance: '2.1 km',
      ),
    ];

    return SizedBox(
      height: 280,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: EdgeInsets.zero,
        itemCount: tailors.length,
        itemBuilder: (context, index) {
          final tailor = tailors[index];
          
          return Padding(
            padding: EdgeInsets.only(
              right: index < tailors.length - 1 ? AppConfig.defaultPadding : 0,
            ),
            child: FeaturedTailorCard(
              tailor: tailor,
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/tailor-profile',
                  arguments: tailor.id,
                );
              },
            ).animate(delay: (index * 100).ms).fadeIn(duration: 600.ms).slideX(
              begin: 0.3,
              end: 0,
              duration: 600.ms,
            ),
          );
        },
      ),
    );
  }
}

class FeaturedTailorCard extends StatelessWidget {
  final FeaturedTailor tailor;
  final VoidCallback onTap;

  const FeaturedTailorCard({
    super.key,
    required this.tailor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        child: Container(
          width: 200,
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withValues(alpha: 0.1),
                blurRadius: 10,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Image and Badge
              Stack(
                children: [
                  // Profile Image
                  Container(
                    height: 160,
                    width: double.infinity,
                    decoration: BoxDecoration(
                      borderRadius: const BorderRadius.vertical(
                        top: Radius.circular(AppConfig.borderRadius),
                      ),
                      image: DecorationImage(
                        image: NetworkImage(tailor.imageUrl),
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                  
                  // Verified Badge
                  if (tailor.isVerified)
                    Positioned(
                      top: 12,
                      right: 12,
                      child: Container(
                        padding: const EdgeInsets.all(4),
                        decoration: BoxDecoration(
                          color: AppTheme.successColor,
                          shape: BoxShape.circle,
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withValues(alpha: 0.2),
                              blurRadius: 4,
                              offset: const Offset(0, 2),
                            ),
                          ],
                        ),
                        child: const Icon(
                          Icons.verified,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  
                  // Distance Badge
                  Positioned(
                    bottom: 12,
                    left: 12,
                    child: Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: Colors.black.withValues(alpha: 0.7),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tailor.distance,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.w600,
                            ),
                      ),
                    ),
                  ),
                ],
              ),
              
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Name
                      Text(
                        tailor.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Specialty
                      Text(
                        tailor.specialty,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const Spacer(),
                      
                      // Rating and Reviews
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppTheme.warningColor,
                            size: 16,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            tailor.rating.toString(),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                          const SizedBox(width: 4),
                          Text(
                            '(${tailor.reviewCount})',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.secondaryTextColor,
                                ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class FeaturedTailor {
  final String id;
  final String name;
  final String specialty;
  final double rating;
  final int reviewCount;
  final String imageUrl;
  final bool isVerified;
  final String distance;

  const FeaturedTailor({
    required this.id,
    required this.name,
    required this.specialty,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.isVerified,
    required this.distance,
  });
}
