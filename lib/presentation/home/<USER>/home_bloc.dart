import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/tailor_model.dart';
import '../../../data/models/api_response.dart';
import '../../../data/services/api_service_manager.dart';

// Events
abstract class HomeEvent extends Equatable {
  const HomeEvent();

  @override
  List<Object?> get props => [];
}

class LoadHomeData extends HomeEvent {
  const LoadHomeData();
}

class RefreshHomeData extends HomeEvent {
  const RefreshHomeData();
}

class LoadMoreNearbyTailors extends HomeEvent {
  const LoadMoreNearbyTailors();
}

class SelectCategory extends HomeEvent {
  final String category;

  const SelectCategory(this.category);

  @override
  List<Object?> get props => [category];
}

class SearchTailors extends HomeEvent {
  final String query;

  const SearchTailors(this.query);

  @override
  List<Object?> get props => [query];
}

// States
abstract class HomeState extends Equatable {
  const HomeState();

  @override
  List<Object?> get props => [];
}

class HomeInitial extends HomeState {
  const HomeInitial();
}

class HomeLoading extends HomeState {
  const HomeLoading();
}

class HomeLoaded extends HomeState {
  final List<TailorModel> featuredTailors;
  final List<TailorModel> nearbyTailors;
  final List<String> categories;
  final String selectedCategory;
  final bool isLoadingMore;
  final bool hasMoreNearby;

  const HomeLoaded({
    required this.featuredTailors,
    required this.nearbyTailors,
    required this.categories,
    required this.selectedCategory,
    this.isLoadingMore = false,
    this.hasMoreNearby = true,
  });

  HomeLoaded copyWith({
    List<TailorModel>? featuredTailors,
    List<TailorModel>? nearbyTailors,
    List<String>? categories,
    String? selectedCategory,
    bool? isLoadingMore,
    bool? hasMoreNearby,
  }) {
    return HomeLoaded(
      featuredTailors: featuredTailors ?? this.featuredTailors,
      nearbyTailors: nearbyTailors ?? this.nearbyTailors,
      categories: categories ?? this.categories,
      selectedCategory: selectedCategory ?? this.selectedCategory,
      isLoadingMore: isLoadingMore ?? this.isLoadingMore,
      hasMoreNearby: hasMoreNearby ?? this.hasMoreNearby,
    );
  }

  @override
  List<Object?> get props => [
        featuredTailors,
        nearbyTailors,
        categories,
        selectedCategory,
        isLoadingMore,
        hasMoreNearby,
      ];
}

class HomeError extends HomeState {
  final String message;
  final bool canRetry;

  const HomeError({
    required this.message,
    this.canRetry = true,
  });

  @override
  List<Object?> get props => [message, canRetry];
}

// BLoC
class HomeBloc extends Bloc<HomeEvent, HomeState> {
  final ApiServiceManager _apiService;

  // Mock user location (Ulaanbaatar, Mongolia)
  static const double _userLatitude = 47.9184;
  static const double _userLongitude = 106.9177;

  HomeBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const HomeInitial()) {
    on<LoadHomeData>(_onLoadHomeData);
    on<RefreshHomeData>(_onRefreshHomeData);
    on<LoadMoreNearbyTailors>(_onLoadMoreNearbyTailors);
    on<SelectCategory>(_onSelectCategory);
    on<SearchTailors>(_onSearchTailors);
  }

  Future<void> _onLoadHomeData(
    LoadHomeData event,
    Emitter<HomeState> emit,
  ) async {
    emit(const HomeLoading());

    try {
      // Load all data concurrently
      final results = await Future.wait([
        _apiService.tailors.getFeaturedTailors(limit: 5),
        _apiService.tailors.getNearbyTailors(
          latitude: _userLatitude,
          longitude: _userLongitude,
          radius: 10,
        ),
        _apiService.tailors.getCategories(),
      ]);

      final featuredResponse = results[0] as ApiResponse<List<TailorModel>>;
      final nearbyResponse = results[1] as ApiResponse<List<TailorModel>>;
      final categoriesResponse = results[2] as ApiResponse<List<String>>;

      if (featuredResponse.success &&
          nearbyResponse.success &&
          categoriesResponse.success) {
        emit(HomeLoaded(
          featuredTailors: featuredResponse.data ?? [],
          nearbyTailors: nearbyResponse.data ?? [],
          categories: categoriesResponse.data ?? [],
          selectedCategory: 'All',
          hasMoreNearby: nearbyResponse.meta?.hasNextPage ?? false,
        ));
      } else {
        final error = featuredResponse.error ??
            nearbyResponse.error ??
            categoriesResponse.error;
        emit(HomeError(message: error?.message ?? 'Failed to load data'));
      }
    } catch (e) {
      emit(HomeError(message: 'An unexpected error occurred: ${e.toString()}'));
    }
  }

  Future<void> _onRefreshHomeData(
    RefreshHomeData event,
    Emitter<HomeState> emit,
  ) async {
    // Keep current state while refreshing
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;
      
      try {
        final results = await Future.wait([
          _apiService.tailors.getFeaturedTailors(limit: 5),
          _apiService.tailors.getNearbyTailors(
            latitude: _userLatitude,
            longitude: _userLongitude,
            radius: 10,
          ),
        ]);

        final featuredResponse = results[0] as ApiResponse<List<TailorModel>>;
        final nearbyResponse = results[1] as ApiResponse<List<TailorModel>>;

        if (featuredResponse.success && nearbyResponse.success) {
          emit(currentState.copyWith(
            featuredTailors: featuredResponse.data ?? [],
            nearbyTailors: nearbyResponse.data ?? [],
            hasMoreNearby: nearbyResponse.meta?.hasNextPage ?? false,
          ));
        }
      } catch (e) {
        // Keep current state if refresh fails
      }
    } else {
      add(const LoadHomeData());
    }
  }

  Future<void> _onLoadMoreNearbyTailors(
    LoadMoreNearbyTailors event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;
      
      if (currentState.isLoadingMore || !currentState.hasMoreNearby) return;

      emit(currentState.copyWith(isLoadingMore: true));

      try {
        final response = await _apiService.tailors.getNearbyTailors(
          latitude: _userLatitude,
          longitude: _userLongitude,
          radius: 10,
        );

        if (response.success) {
          final newTailors = response.data ?? [];
          final updatedNearbyTailors = [
            ...currentState.nearbyTailors,
            ...newTailors,
          ];

          emit(currentState.copyWith(
            nearbyTailors: updatedNearbyTailors,
            isLoadingMore: false,
            hasMoreNearby: response.meta?.hasNextPage ?? false,
          ));
        } else {
          emit(currentState.copyWith(isLoadingMore: false));
        }
      } catch (e) {
        emit(currentState.copyWith(isLoadingMore: false));
      }
    }
  }

  Future<void> _onSelectCategory(
    SelectCategory event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;
      emit(currentState.copyWith(selectedCategory: event.category));
      
      // TODO: Filter tailors by category
      // For now, we'll just update the selected category
    }
  }

  Future<void> _onSearchTailors(
    SearchTailors event,
    Emitter<HomeState> emit,
  ) async {
    if (state is HomeLoaded) {
      final currentState = state as HomeLoaded;

      try {
        final tailors = await _apiService.searchTailors(
          query: event.query,
          categories: currentState.selectedCategory != 'All'
              ? [currentState.selectedCategory]
              : null,
        );

        // For now, just update the nearby tailors with search results
        emit(currentState.copyWith(
          nearbyTailors: tailors,
        ));
      } catch (e) {
        // Emit error state or keep current state
        emit(HomeError(message: 'Search failed: ${e.toString()}'));
      }
    }
  }
}
