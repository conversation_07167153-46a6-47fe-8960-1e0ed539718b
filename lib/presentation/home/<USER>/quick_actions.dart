import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Quick action buttons for common tasks
class QuickActions extends StatelessWidget {
  const QuickActions({super.key});

  @override
  Widget build(BuildContext context) {
    final actions = [
      QuickAction(
        icon: Icons.account_balance_wallet,
        label: 'Wallet',
        color: const Color(0xFF667eea),
        onTap: () {
          Navigator.pushNamed(context, '/wallet');
        },
      ),
      QuickAction(
        icon: Icons.account_balance,
        label: 'Loans',
        color: const Color(0xFFf093fb),
        onTap: () {
          Navigator.pushNamed(context, '/loans');
        },
      ),
      QuickAction(
        icon: Icons.receipt_long,
        label: 'Orders',
        color: const Color(0xFF4facfe),
        onTap: () {
          Navigator.pushNamed(context, '/orders');
        },
      ),
      QuickAction(
        icon: Icons.chat_bubble,
        label: 'Messages',
        color: const Color(0xFF43e97b),
        onTap: () {
          Navigator.pushNamed(context, '/chat');
        },
      ),
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Actions',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: actions.asMap().entries.map((entry) {
            final index = entry.key;
            final action = entry.value;
            
            return Expanded(
              child: Padding(
                padding: EdgeInsets.only(
                  right: index < actions.length - 1 ? AppConfig.defaultPadding : 0,
                ),
                child: QuickActionCard(action: action),
              ),
            );
          }).toList(),
        ),
      ],
    );
  }
}

class QuickActionCard extends StatelessWidget {
  final QuickAction action;

  const QuickActionCard({
    super.key,
    required this.action,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: action.onTap,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            border: Border.all(
              color: AppTheme.dividerColor,
              width: 1,
            ),
          ),
          child: Column(
            children: [
              // Icon Container
              Container(
                width: 48,
                height: 48,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topLeft,
                    end: Alignment.bottomRight,
                    colors: [
                      action.color,
                      action.color.withValues(alpha: 0.7),
                    ],
                  ),
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: action.color.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 4),
                    ),
                  ],
                ),
                child: Icon(
                  action.icon,
                  color: Colors.white,
                  size: 24,
                ),
              ),
              
              const SizedBox(height: AppConfig.smallPadding),
              
              // Label
              Text(
                action.label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      fontWeight: FontWeight.w600,
                      color: AppTheme.primaryTextColor,
                    ),
                textAlign: TextAlign.center,
                maxLines: 2,
                overflow: TextOverflow.ellipsis,
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(duration: 600.ms).scale(
      begin: const Offset(0.8, 0.8),
      end: const Offset(1.0, 1.0),
      duration: 600.ms,
      curve: Curves.elasticOut,
    );
  }
}

class QuickAction {
  final IconData icon;
  final String label;
  final Color color;
  final VoidCallback onTap;

  const QuickAction({
    required this.icon,
    required this.label,
    required this.color,
    required this.onTap,
  });
}
