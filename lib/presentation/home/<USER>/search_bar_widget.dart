import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Instagram-inspired search bar
class SearchBarWidget extends StatefulWidget {
  final ValueChanged<bool>? onFocusChanged;

  const SearchBarWidget({
    super.key,
    this.onFocusChanged,
  });

  @override
  State<SearchBarWidget> createState() => _SearchBarWidgetState();
}

class _SearchBarWidgetState extends State<SearchBarWidget> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _isFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(_onFocusChanged);
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  void _onFocusChanged() {
    setState(() {
      _isFocused = _focusNode.hasFocus;
    });
    widget.onFocusChanged?.call(_isFocused);
  }

  void _onSearch(String query) {
    if (query.trim().isEmpty) return;
    
    // TODO: Implement search functionality
    print('Searching for: $query');
  }

  void _onFilterTap() {
    // TODO: Show filter bottom sheet
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => _buildFilterBottomSheet(),
    );
  }

  Widget _buildFilterBottomSheet() {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.vertical(
          top: Radius.circular(20),
        ),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.dividerColor,
              borderRadius: BorderRadius.circular(2),
            ),
          ),
          
          // Header
          Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
                TextButton(
                  onPressed: () {
                    Navigator.pop(context);
                  },
                  child: const Text('Clear All'),
                ),
              ],
            ),
          ),
          
          // Filter Content
          Expanded(
            child: Padding(
              padding: const EdgeInsets.all(AppConfig.defaultPadding),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Distance',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: AppConfig.defaultPadding),
                  // TODO: Add distance slider
                  
                  const SizedBox(height: AppConfig.largePadding),
                  
                  Text(
                    'Price Range',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: AppConfig.defaultPadding),
                  // TODO: Add price range slider
                  
                  const SizedBox(height: AppConfig.largePadding),
                  
                  Text(
                    'Rating',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.w600,
                        ),
                  ),
                  const SizedBox(height: AppConfig.defaultPadding),
                  // TODO: Add rating filter
                ],
              ),
            ),
          ),
          
          // Apply Button
          Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: SizedBox(
              width: double.infinity,
              child: ElevatedButton(
                onPressed: () {
                  Navigator.pop(context);
                  // TODO: Apply filters
                },
                child: const Text('Apply Filters'),
              ),
            ),
          ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: _isFocused ? AppTheme.primaryColor : AppTheme.dividerColor,
          width: _isFocused ? 2 : 1,
        ),
      ),
      child: Row(
        children: [
          // Search Icon
          Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConfig.defaultPadding,
            ),
            child: Icon(
              Icons.search,
              color: _isFocused 
                  ? AppTheme.primaryColor 
                  : AppTheme.secondaryTextColor,
              size: 20,
            ),
          ),

          // Search Input
          Expanded(
            child: TextField(
              controller: _controller,
              focusNode: _focusNode,
              decoration: InputDecoration(
                hintText: 'Search tailors, services...',
                hintStyle: TextStyle(
                  color: AppTheme.secondaryTextColor,
                  fontSize: 14,
                ),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.symmetric(
                  vertical: AppConfig.defaultPadding,
                ),
              ),
              textInputAction: TextInputAction.search,
              onSubmitted: _onSearch,
            ),
          ),

          // Filter Button
          Container(
            margin: const EdgeInsets.all(4),
            child: Material(
              color: _isFocused ? AppTheme.primaryColor : AppTheme.surfaceColor,
              borderRadius: BorderRadius.circular(AppConfig.borderRadius - 4),
              child: InkWell(
                onTap: _onFilterTap,
                borderRadius: BorderRadius.circular(AppConfig.borderRadius - 4),
                child: Padding(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  child: Icon(
                    Icons.tune,
                    color: _isFocused ? Colors.white : AppTheme.primaryTextColor,
                    size: 20,
                  ),
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate(target: _isFocused ? 1 : 0).scale(
      begin: const Offset(1.0, 1.0),
      end: const Offset(1.02, 1.02),
      duration: 200.ms,
    );
  }
}
