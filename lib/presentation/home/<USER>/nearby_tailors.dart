import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Nearby tailors vertical list
class NearbyTailors extends StatelessWidget {
  const NearbyTailors({super.key});

  @override
  Widget build(BuildContext context) {
    // Mock data - TODO: Replace with real data
    final tailors = [
      NearbyTailor(
        id: '1',
        name: 'Golden Needle Tailoring',
        specialty: 'Custom Suits & Formal Wear',
        rating: 4.8,
        reviewCount: 234,
        imageUrl: 'https://images.unsplash.com/photo-1441986300917-64674bd600d8?w=400&h=300&fit=crop',
        isVerified: true,
        distance: '0.5 km',
        priceRange: '\$\$',
        isOpen: true,
        services: ['Custom Tailoring', 'Alterations', 'Dry Cleaning'],
      ),
      NearbyTailor(
        id: '2',
        name: 'Silk Road Atelier',
        specialty: 'Traditional & Modern Designs',
        rating: 4.9,
        reviewCount: 189,
        imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=300&fit=crop',
        isVerified: true,
        distance: '1.1 km',
        priceRange: '\$\$\$',
        isOpen: false,
        services: ['Wedding Dresses', 'Traditional Wear', 'Embroidery'],
      ),
      NearbyTailor(
        id: '3',
        name: 'Quick Fix Tailors',
        specialty: 'Fast Alterations & Repairs',
        rating: 4.6,
        reviewCount: 156,
        imageUrl: 'https://images.unsplash.com/photo-1556905055-8f358a7a47b2?w=400&h=300&fit=crop',
        isVerified: false,
        distance: '1.8 km',
        priceRange: '\$',
        isOpen: true,
        services: ['Alterations', 'Hemming', 'Repairs'],
      ),
      NearbyTailor(
        id: '4',
        name: 'Elite Fashion House',
        specialty: 'Luxury Custom Clothing',
        rating: 4.7,
        reviewCount: 98,
        imageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=300&fit=crop',
        isVerified: true,
        distance: '2.3 km',
        priceRange: '\$\$\$\$',
        isOpen: true,
        services: ['Luxury Suits', 'Designer Wear', 'Personal Styling'],
      ),
    ];

    return SliverList(
      delegate: SliverChildBuilderDelegate(
        (context, index) {
          final tailor = tailors[index];
          
          return Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConfig.defaultPadding,
              vertical: AppConfig.smallPadding,
            ),
            child: NearbyTailorCard(
              tailor: tailor,
              onTap: () {
                Navigator.pushNamed(
                  context,
                  '/tailor-profile',
                  arguments: tailor.id,
                );
              },
            ).animate(delay: (index * 100).ms).fadeIn(duration: 600.ms).slideY(
              begin: 0.3,
              end: 0,
              duration: 600.ms,
            ),
          );
        },
        childCount: tailors.length,
      ),
    );
  }
}

class NearbyTailorCard extends StatelessWidget {
  final NearbyTailor tailor;
  final VoidCallback onTap;

  const NearbyTailorCard({
    super.key,
    required this.tailor,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        child: Container(
          decoration: BoxDecoration(
            color: AppTheme.surfaceColor,
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            border: Border.all(
              color: AppTheme.dividerColor,
              width: 1,
            ),
          ),
          child: Row(
            children: [
              // Image
              Container(
                width: 100,
                height: 120,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.horizontal(
                    left: Radius.circular(AppConfig.borderRadius),
                  ),
                  image: DecorationImage(
                    image: NetworkImage(tailor.imageUrl),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
              
              // Content
              Expanded(
                child: Padding(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Header Row
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              tailor.name,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                          
                          // Verified Badge
                          if (tailor.isVerified) ...[
                            const SizedBox(width: 4),
                            Icon(
                              Icons.verified,
                              color: AppTheme.successColor,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                      
                      const SizedBox(height: 4),
                      
                      // Specialty
                      Text(
                        tailor.specialty,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Rating and Distance Row
                      Row(
                        children: [
                          Icon(
                            Icons.star,
                            color: AppTheme.warningColor,
                            size: 14,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            tailor.rating.toString(),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  fontWeight: FontWeight.w600,
                            ),
                          ),
                          const SizedBox(width: 2),
                          Text(
                            '(${tailor.reviewCount})',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.secondaryTextColor,
                            ),
                          ),
                          
                          const SizedBox(width: 12),
                          
                          Icon(
                            Icons.location_on_outlined,
                            color: AppTheme.secondaryTextColor,
                            size: 14,
                          ),
                          const SizedBox(width: 2),
                          Text(
                            tailor.distance,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.secondaryTextColor,
                            ),
                          ),
                          
                          const SizedBox(width: 12),
                          
                          Text(
                            tailor.priceRange,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.primaryColor,
                                  fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                      
                      const SizedBox(height: 8),
                      
                      // Status and Services Row
                      Row(
                        children: [
                          // Open/Closed Status
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 6,
                              vertical: 2,
                            ),
                            decoration: BoxDecoration(
                              color: tailor.isOpen 
                                  ? AppTheme.successColor.withValues(alpha: 0.1)
                                  : AppTheme.errorColor.withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(4),
                            ),
                            child: Text(
                              tailor.isOpen ? 'Open' : 'Closed',
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: tailor.isOpen 
                                        ? AppTheme.successColor
                                        : AppTheme.errorColor,
                                    fontWeight: FontWeight.w600,
                                    fontSize: 10,
                              ),
                            ),
                          ),
                          
                          const SizedBox(width: 8),
                          
                          // Services
                          Expanded(
                            child: Text(
                              tailor.services.take(2).join(' • '),
                              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                    color: AppTheme.secondaryTextColor,
                                    fontSize: 11,
                              ),
                              maxLines: 1,
                              overflow: TextOverflow.ellipsis,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              ),
              
              // Action Button
              Padding(
                padding: const EdgeInsets.all(AppConfig.defaultPadding),
                child: Icon(
                  Icons.arrow_forward_ios,
                  color: AppTheme.secondaryTextColor,
                  size: 16,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

class NearbyTailor {
  final String id;
  final String name;
  final String specialty;
  final double rating;
  final int reviewCount;
  final String imageUrl;
  final bool isVerified;
  final String distance;
  final String priceRange;
  final bool isOpen;
  final List<String> services;

  const NearbyTailor({
    required this.id,
    required this.name,
    required this.specialty,
    required this.rating,
    required this.reviewCount,
    required this.imageUrl,
    required this.isVerified,
    required this.distance,
    required this.priceRange,
    required this.isOpen,
    required this.services,
  });
}
