import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';

/// Instagram-style category chips for filtering
class CategoryChips extends StatefulWidget {
  const CategoryChips({super.key});

  @override
  State<CategoryChips> createState() => _CategoryChipsState();
}

class _CategoryChipsState extends State<CategoryChips> {
  int _selectedIndex = 0;

  final List<String> _categories = [
    'All',
    'Shirts',
    'Suits',
    'Dresses',
    'Pants',
    'Jackets',
    'Traditional',
    'Formal',
    'Casual',
  ];

  void _onCategorySelected(int index) {
    setState(() {
      _selectedIndex = index;
    });
    
    // TODO: Filter tailors by category
    print('Selected category: ${_categories[index]}');
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 40,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        padding: const EdgeInsets.symmetric(
          horizontal: AppConfig.defaultPadding,
        ),
        itemCount: _categories.length,
        itemBuilder: (context, index) {
          final isSelected = index == _selectedIndex;
          final category = _categories[index];
          
          return Padding(
            padding: EdgeInsets.only(
              right: index < _categories.length - 1 ? AppConfig.smallPadding : 0,
            ),
            child: CategoryChip(
              label: category,
              isSelected: isSelected,
              onTap: () => _onCategorySelected(index),
            ).animate(delay: (index * 50).ms).fadeIn(duration: 400.ms).slideX(
              begin: 0.3,
              end: 0,
              duration: 400.ms,
            ),
          );
        },
      ),
    );
  }
}

class CategoryChip extends StatelessWidget {
  final String label;
  final bool isSelected;
  final VoidCallback onTap;

  const CategoryChip({
    super.key,
    required this.label,
    required this.isSelected,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(20),
        child: AnimatedContainer(
          duration: AppConfig.shortAnimation,
          curve: Curves.easeInOut,
          padding: const EdgeInsets.symmetric(
            horizontal: AppConfig.defaultPadding,
            vertical: AppConfig.smallPadding,
          ),
          decoration: BoxDecoration(
            gradient: isSelected
                ? AppTheme.instagramGradient
                : null,
            color: isSelected
                ? null
                : AppTheme.backgroundColor,
            borderRadius: BorderRadius.circular(20),
            border: Border.all(
              color: isSelected
                  ? Colors.transparent
                  : AppTheme.dividerColor,
              width: 1,
            ),
            boxShadow: isSelected
                ? [
                    BoxShadow(
                      color: AppTheme.primaryColor.withValues(alpha: 0.3),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ]
                : null,
          ),
          child: Center(
            child: Text(
              label,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: isSelected
                        ? Colors.white
                        : AppTheme.primaryTextColor,
                    fontWeight: isSelected
                        ? FontWeight.w600
                        : FontWeight.w500,
                  ),
            ),
          ),
        ),
      ),
    ).animate(target: isSelected ? 1 : 0).scale(
      begin: const Offset(1.0, 1.0),
      end: const Offset(1.05, 1.05),
      duration: 200.ms,
    );
  }
}
