import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/services/api_service_manager.dart';
import '../../../data/models/user_model.dart';

// Events
abstract class SupplyChainEvent extends Equatable {
  const SupplyChainEvent();

  @override
  List<Object?> get props => [];
}

class LoadSupplyChainData extends SupplyChainEvent {
  const LoadSupplyChainData();
}

class FilterSuppliersByCategory extends SupplyChainEvent {
  final String category;

  const FilterSuppliersByCategory({required this.category});

  @override
  List<Object?> get props => [category];
}

class CreateSupplyOrder extends SupplyChainEvent {
  final String supplierId;
  final List<OrderItem> items;
  final String deliveryAddress;

  const CreateSupplyOrder({
    required this.supplierId,
    required this.items,
    required this.deliveryAddress,
  });

  @override
  List<Object?> get props => [supplierId, items, deliveryAddress];
}

class UpdateInventory extends SupplyChainEvent {
  final String itemId;
  final int quantity;

  const UpdateInventory({
    required this.itemId,
    required this.quantity,
  });

  @override
  List<Object?> get props => [itemId, quantity];
}

// States
abstract class SupplyChainState extends Equatable {
  const SupplyChainState();

  @override
  List<Object?> get props => [];
}

class SupplyChainInitial extends SupplyChainState {
  const SupplyChainInitial();
}

class SupplyChainLoading extends SupplyChainState {
  const SupplyChainLoading();
}

class SupplyChainLoaded extends SupplyChainState {
  final UserModel user;
  final List<SupplierCategory> categories;
  final List<Supplier> suppliers;
  final List<Supplier> featuredSuppliers;
  final List<SupplyOrder> myOrders;
  final List<InventoryItem> inventory;
  final List<InventoryItem> lowStockItems;
  final Map<String, dynamic> priceComparison;
  final int activeOrders;
  final int deliveredOrders;
  final double totalSpent;

  const SupplyChainLoaded({
    required this.user,
    required this.categories,
    required this.suppliers,
    required this.featuredSuppliers,
    required this.myOrders,
    required this.inventory,
    required this.lowStockItems,
    required this.priceComparison,
    required this.activeOrders,
    required this.deliveredOrders,
    required this.totalSpent,
  });

  @override
  List<Object?> get props => [
        user,
        categories,
        suppliers,
        featuredSuppliers,
        myOrders,
        inventory,
        lowStockItems,
        priceComparison,
        activeOrders,
        deliveredOrders,
        totalSpent,
      ];

  SupplyChainLoaded copyWith({
    UserModel? user,
    List<SupplierCategory>? categories,
    List<Supplier>? suppliers,
    List<Supplier>? featuredSuppliers,
    List<SupplyOrder>? myOrders,
    List<InventoryItem>? inventory,
    List<InventoryItem>? lowStockItems,
    Map<String, dynamic>? priceComparison,
    int? activeOrders,
    int? deliveredOrders,
    double? totalSpent,
  }) {
    return SupplyChainLoaded(
      user: user ?? this.user,
      categories: categories ?? this.categories,
      suppliers: suppliers ?? this.suppliers,
      featuredSuppliers: featuredSuppliers ?? this.featuredSuppliers,
      myOrders: myOrders ?? this.myOrders,
      inventory: inventory ?? this.inventory,
      lowStockItems: lowStockItems ?? this.lowStockItems,
      priceComparison: priceComparison ?? this.priceComparison,
      activeOrders: activeOrders ?? this.activeOrders,
      deliveredOrders: deliveredOrders ?? this.deliveredOrders,
      totalSpent: totalSpent ?? this.totalSpent,
    );
  }
}

class SupplyChainError extends SupplyChainState {
  final String message;

  const SupplyChainError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class SupplyChainBloc extends Bloc<SupplyChainEvent, SupplyChainState> {
  final ApiServiceManager _apiService;

  SupplyChainBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const SupplyChainInitial()) {
    on<LoadSupplyChainData>(_onLoadSupplyChainData);
    on<FilterSuppliersByCategory>(_onFilterSuppliersByCategory);
    on<CreateSupplyOrder>(_onCreateSupplyOrder);
    on<UpdateInventory>(_onUpdateInventory);
  }

  Future<void> _onLoadSupplyChainData(
    LoadSupplyChainData event,
    Emitter<SupplyChainState> emit,
  ) async {
    emit(const SupplyChainLoading());

    try {
      // Load all supply chain data
      final user = await _getMockUser();
      final categories = await _getMockCategories();
      final suppliers = await _getMockSuppliers();
      final featuredSuppliers = await _getMockFeaturedSuppliers();
      final myOrders = await _getMockMyOrders();
      final inventory = await _getMockInventory();
      final lowStockItems = inventory.where((item) => item.quantity <= item.minStock).toList();
      final priceComparison = await _getMockPriceComparison();

      // Calculate statistics
      final activeOrders = myOrders.where((order) => order.status == 'active').length;
      final deliveredOrders = myOrders.where((order) => order.status == 'delivered').length;
      final totalSpent = myOrders.fold<double>(0.0, (sum, order) => sum + order.totalAmount);

      emit(SupplyChainLoaded(
        user: user,
        categories: categories,
        suppliers: suppliers,
        featuredSuppliers: featuredSuppliers,
        myOrders: myOrders,
        inventory: inventory,
        lowStockItems: lowStockItems,
        priceComparison: priceComparison,
        activeOrders: activeOrders,
        deliveredOrders: deliveredOrders,
        totalSpent: totalSpent,
      ));
    } catch (e) {
      emit(SupplyChainError(message: e.toString()));
    }
  }

  Future<void> _onFilterSuppliersByCategory(
    FilterSuppliersByCategory event,
    Emitter<SupplyChainState> emit,
  ) async {
    if (state is SupplyChainLoaded) {
      final currentState = state as SupplyChainLoaded;
      
      // Filter suppliers by category
      final filteredSuppliers = currentState.suppliers
          .where((supplier) => supplier.category == event.category)
          .toList();

      emit(currentState.copyWith(suppliers: filteredSuppliers));
    }
  }

  Future<void> _onCreateSupplyOrder(
    CreateSupplyOrder event,
    Emitter<SupplyChainState> emit,
  ) async {
    if (state is SupplyChainLoaded) {
      final currentState = state as SupplyChainLoaded;
      
      try {
        // Create new order
        final newOrder = SupplyOrder(
          id: 'order_${DateTime.now().millisecondsSinceEpoch}',
          supplierId: event.supplierId,
          items: event.items,
          totalAmount: event.items.fold(0.0, (sum, item) => sum + (item.price * item.quantity)),
          status: 'active',
          orderDate: DateTime.now(),
          deliveryAddress: event.deliveryAddress,
        );

        final updatedOrders = [...currentState.myOrders, newOrder];
        
        emit(currentState.copyWith(
          myOrders: updatedOrders,
          activeOrders: currentState.activeOrders + 1,
        ));
      } catch (e) {
        emit(SupplyChainError(message: 'Failed to create order'));
      }
    }
  }

  Future<void> _onUpdateInventory(
    UpdateInventory event,
    Emitter<SupplyChainState> emit,
  ) async {
    if (state is SupplyChainLoaded) {
      final currentState = state as SupplyChainLoaded;
      
      final updatedInventory = currentState.inventory.map((item) {
        if (item.id == event.itemId) {
          return item.copyWith(quantity: event.quantity);
        }
        return item;
      }).toList();

      final lowStockItems = updatedInventory.where((item) => item.quantity <= item.minStock).toList();

      emit(currentState.copyWith(
        inventory: updatedInventory,
        lowStockItems: lowStockItems,
      ));
    }
  }

  // Mock data methods
  Future<UserModel> _getMockUser() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return UserModel(
      id: 'user_1',
      email: '<EMAIL>',
      firstName: 'Master',
      lastName: 'Tailor',
      phone: '+976 9999 9999',
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
  }

  Future<List<SupplierCategory>> _getMockCategories() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return [
      SupplierCategory(id: '1', name: 'Fabrics', icon: 'fabric'),
      SupplierCategory(id: '2', name: 'Threads', icon: 'thread'),
      SupplierCategory(id: '3', name: 'Buttons', icon: 'button'),
      SupplierCategory(id: '4', name: 'Zippers', icon: 'zipper'),
      SupplierCategory(id: '5', name: 'Tools', icon: 'tools'),
    ];
  }

  Future<List<Supplier>> _getMockSuppliers() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      Supplier(
        id: 'supplier_1',
        name: 'Premium Fabrics Co.',
        category: 'Fabrics',
        rating: 4.8,
        location: 'Ulaanbaatar',
        description: 'High-quality fabrics for professional tailoring',
        imageUrl: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=300',
        isVerified: true,
        deliveryTime: '2-3 days',
        minOrder: 50.0,
      ),
      Supplier(
        id: 'supplier_2',
        name: 'Thread Masters',
        category: 'Threads',
        rating: 4.6,
        location: 'Darkhan',
        description: 'Professional grade threads and yarns',
        imageUrl: 'https://images.unsplash.com/photo-1558618047-3c8c76ca7d13?w=300',
        isVerified: true,
        deliveryTime: '1-2 days',
        minOrder: 25.0,
      ),
    ];
  }

  Future<List<Supplier>> _getMockFeaturedSuppliers() async {
    final allSuppliers = await _getMockSuppliers();
    return allSuppliers.where((s) => s.rating >= 4.7).toList();
  }

  Future<List<SupplyOrder>> _getMockMyOrders() async {
    await Future.delayed(const Duration(milliseconds: 300));
    return [
      SupplyOrder(
        id: 'order_1',
        supplierId: 'supplier_1',
        items: [
          OrderItem(
            id: 'item_1',
            name: 'Premium Wool Fabric',
            quantity: 5,
            price: 45.0,
            unit: 'meters',
          ),
        ],
        totalAmount: 225.0,
        status: 'active',
        orderDate: DateTime.now().subtract(const Duration(days: 2)),
        deliveryAddress: 'Tailor Shop, Ulaanbaatar',
        estimatedDelivery: DateTime.now().add(const Duration(days: 1)),
      ),
    ];
  }

  Future<List<InventoryItem>> _getMockInventory() async {
    await Future.delayed(const Duration(milliseconds: 400));
    return [
      InventoryItem(
        id: 'inv_1',
        name: 'Cotton Fabric',
        category: 'Fabrics',
        quantity: 25,
        minStock: 10,
        unit: 'meters',
        costPerUnit: 15.0,
        supplier: 'Premium Fabrics Co.',
        lastRestocked: DateTime.now().subtract(const Duration(days: 7)),
      ),
      InventoryItem(
        id: 'inv_2',
        name: 'Polyester Thread',
        category: 'Threads',
        quantity: 5, // Low stock
        minStock: 20,
        unit: 'spools',
        costPerUnit: 3.50,
        supplier: 'Thread Masters',
        lastRestocked: DateTime.now().subtract(const Duration(days: 14)),
      ),
    ];
  }

  Future<Map<String, dynamic>> _getMockPriceComparison() async {
    await Future.delayed(const Duration(milliseconds: 200));
    return {
      'trending_items': [
        {'name': 'Cotton Fabric', 'price_change': '+5%'},
        {'name': 'Silk Thread', 'price_change': '-2%'},
      ],
      'best_deals': [
        {'supplier': 'Premium Fabrics Co.', 'discount': '15%'},
        {'supplier': 'Thread Masters', 'discount': '10%'},
      ],
    };
  }
}

// Models
class SupplierCategory extends Equatable {
  final String id;
  final String name;
  final String icon;

  const SupplierCategory({
    required this.id,
    required this.name,
    required this.icon,
  });

  @override
  List<Object?> get props => [id, name, icon];
}

class Supplier extends Equatable {
  final String id;
  final String name;
  final String category;
  final double rating;
  final String location;
  final String description;
  final String imageUrl;
  final bool isVerified;
  final String deliveryTime;
  final double minOrder;

  const Supplier({
    required this.id,
    required this.name,
    required this.category,
    required this.rating,
    required this.location,
    required this.description,
    required this.imageUrl,
    required this.isVerified,
    required this.deliveryTime,
    required this.minOrder,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        category,
        rating,
        location,
        description,
        imageUrl,
        isVerified,
        deliveryTime,
        minOrder,
      ];
}

class SupplyOrder extends Equatable {
  final String id;
  final String supplierId;
  final List<OrderItem> items;
  final double totalAmount;
  final String status;
  final DateTime orderDate;
  final String deliveryAddress;
  final DateTime? estimatedDelivery;

  const SupplyOrder({
    required this.id,
    required this.supplierId,
    required this.items,
    required this.totalAmount,
    required this.status,
    required this.orderDate,
    required this.deliveryAddress,
    this.estimatedDelivery,
  });

  @override
  List<Object?> get props => [
        id,
        supplierId,
        items,
        totalAmount,
        status,
        orderDate,
        deliveryAddress,
        estimatedDelivery,
      ];
}

class OrderItem extends Equatable {
  final String id;
  final String name;
  final int quantity;
  final double price;
  final String unit;

  const OrderItem({
    required this.id,
    required this.name,
    required this.quantity,
    required this.price,
    required this.unit,
  });

  @override
  List<Object?> get props => [id, name, quantity, price, unit];
}

class InventoryItem extends Equatable {
  final String id;
  final String name;
  final String category;
  final int quantity;
  final int minStock;
  final String unit;
  final double costPerUnit;
  final String supplier;
  final DateTime lastRestocked;

  const InventoryItem({
    required this.id,
    required this.name,
    required this.category,
    required this.quantity,
    required this.minStock,
    required this.unit,
    required this.costPerUnit,
    required this.supplier,
    required this.lastRestocked,
  });

  @override
  List<Object?> get props => [
        id,
        name,
        category,
        quantity,
        minStock,
        unit,
        costPerUnit,
        supplier,
        lastRestocked,
      ];

  InventoryItem copyWith({
    String? id,
    String? name,
    String? category,
    int? quantity,
    int? minStock,
    String? unit,
    double? costPerUnit,
    String? supplier,
    DateTime? lastRestocked,
  }) {
    return InventoryItem(
      id: id ?? this.id,
      name: name ?? this.name,
      category: category ?? this.category,
      quantity: quantity ?? this.quantity,
      minStock: minStock ?? this.minStock,
      unit: unit ?? this.unit,
      costPerUnit: costPerUnit ?? this.costPerUnit,
      supplier: supplier ?? this.supplier,
      lastRestocked: lastRestocked ?? this.lastRestocked,
    );
  }
}
