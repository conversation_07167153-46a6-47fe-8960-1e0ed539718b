import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';

import 'bloc/supply_chain_bloc.dart';
import 'widgets/supply_chain_header.dart';
import 'widgets/supplier_categories.dart';
import 'widgets/featured_suppliers.dart';
import 'widgets/my_orders.dart';
import 'widgets/inventory_overview.dart';
import 'widgets/price_comparison.dart';

/// Supply chain management screen for tailors
class SupplyChainScreen extends StatefulWidget {
  const SupplyChainScreen({super.key});

  @override
  State<SupplyChainScreen> createState() => _SupplyChainScreenState();
}

class _SupplyChainScreenState extends State<SupplyChainScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SupplyChainBloc()..add(const LoadSupplyChainData()),
      child: Scaffold(
        backgroundColor: AppTheme.backgroundColor,
        body: SafeArea(
          child: BlocBuilder<SupplyChainBloc, SupplyChainState>(
            builder: (context, state) {
              if (state is SupplyChainLoading) {
                return _buildLoadingState();
              }

              if (state is SupplyChainError) {
                return _buildErrorState(state.message);
              }

              if (state is SupplyChainLoaded) {
                return _buildLoadedState(context, state);
              }

              return _buildInitialState();
            },
          ),
        ),
        floatingActionButton: FloatingActionButton.extended(
          onPressed: () {
            Navigator.pushNamed(context, '/create-order');
          },
          backgroundColor: AppTheme.primaryColor,
          icon: const Icon(Icons.add_shopping_cart, color: Colors.white),
          label: const Text(
            'New Order',
            style: TextStyle(color: Colors.white),
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: CircularProgressIndicator(),
    );
  }

  Widget _buildErrorState(String message) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Error Loading Supply Chain',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            message,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          ElevatedButton(
            onPressed: () {
              context.read<SupplyChainBloc>().add(const LoadSupplyChainData());
            },
            child: const Text('Retry'),
          ),
        ],
      ),
    );
  }

  Widget _buildInitialState() {
    return const Center(
      child: Text('Welcome to Supply Chain Management'),
    );
  }

  Widget _buildLoadedState(BuildContext context, SupplyChainLoaded state) {
    return Column(
      children: [
        // Header with inventory overview
        SupplyChainHeader(
          user: state.user,
          totalSuppliers: state.suppliers.length,
          activeOrders: state.activeOrders,
          totalSpent: state.totalSpent,
        ),

        // Tab Bar
        Container(
          color: Colors.white,
          child: TabBar(
            controller: _tabController,
            labelColor: AppTheme.primaryColor,
            unselectedLabelColor: AppTheme.secondaryTextColor,
            indicatorColor: AppTheme.primaryColor,
            tabs: const [
              Tab(text: 'Suppliers'),
              Tab(text: 'My Orders'),
              Tab(text: 'Inventory'),
              Tab(text: 'Analytics'),
            ],
          ),
        ),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildSuppliersTab(state),
              _buildMyOrdersTab(state),
              _buildInventoryTab(state),
              _buildAnalyticsTab(state),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildSuppliersTab(SupplyChainLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Supplier Categories
          SupplierCategories(
            categories: state.categories,
            onCategorySelected: (category) {
              context.read<SupplyChainBloc>().add(
                    FilterSuppliersByCategory(category: category),
                  );
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Featured Suppliers
          FeaturedSuppliers(
            suppliers: state.featuredSuppliers,
            onSupplierSelected: (supplier) {
              Navigator.pushNamed(
                context,
                '/supplier-details',
                arguments: supplier,
              );
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // All Suppliers
          Text(
            'All Suppliers',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          
          ListView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            itemCount: state.suppliers.length,
            itemBuilder: (context, index) {
              final supplier = state.suppliers[index];
              return _buildSupplierCard(supplier, index);
            },
          ),
        ],
      ),
    );
  }

  Widget _buildMyOrdersTab(SupplyChainLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Order Statistics
          _buildOrderStats(state),

          const SizedBox(height: AppConfig.largePadding),

          // My Orders
          MyOrders(
            orders: state.myOrders,
            onOrderSelected: (order) {
              Navigator.pushNamed(
                context,
                '/order-tracking',
                arguments: order,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildInventoryTab(SupplyChainLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Inventory Overview
          InventoryOverview(
            inventory: state.inventory,
            lowStockItems: state.lowStockItems,
            onItemSelected: (item) {
              Navigator.pushNamed(
                context,
                '/inventory-item',
                arguments: item,
              );
            },
          ),
        ],
      ),
    );
  }

  Widget _buildAnalyticsTab(SupplyChainLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Supply Chain Analytics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          // Price Comparison
          PriceComparison(
            priceData: state.priceComparison,
            onComparePressed: () {
              Navigator.pushNamed(context, '/price-comparison');
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Spending Analytics
          _buildSpendingAnalytics(state),
        ],
      ),
    );
  }

  Widget _buildSupplierCard(dynamic supplier, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Card(
        elevation: 2,
        child: ListTile(
          leading: CircleAvatar(
            backgroundColor: AppTheme.primaryColor,
            child: Icon(
              Icons.business,
              color: Colors.white,
            ),
          ),
          title: Text(supplier.name ?? 'Supplier Name'),
          subtitle: Text(supplier.category ?? 'Category'),
          trailing: Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.star,
                color: AppTheme.warningColor,
                size: 16,
              ),
              const SizedBox(width: 4),
              Text(supplier.rating?.toString() ?? '4.5'),
              const SizedBox(width: 8),
              const Icon(Icons.arrow_forward_ios),
            ],
          ),
          onTap: () {
            Navigator.pushNamed(
              context,
              '/supplier-details',
              arguments: supplier,
            );
          },
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildOrderStats(SupplyChainLoaded state) {
    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            'Active Orders',
            state.activeOrders.toString(),
            Icons.shopping_cart,
            AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: AppConfig.defaultPadding),
        Expanded(
          child: _buildStatCard(
            'Delivered',
            state.deliveredOrders.toString(),
            Icons.check_circle,
            AppTheme.successColor,
          ),
        ),
        const SizedBox(width: AppConfig.defaultPadding),
        Expanded(
          child: _buildStatCard(
            'Total Spent',
            '\$${state.totalSpent.toStringAsFixed(0)}',
            Icons.attach_money,
            AppTheme.warningColor,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildSpendingAnalytics(SupplyChainLoaded state) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Monthly Spending Trend',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            SizedBox(
              height: 200,
              child: const Center(
                child: Text('Chart would go here'),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
