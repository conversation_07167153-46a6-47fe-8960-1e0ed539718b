import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/supply_chain_bloc.dart';

/// Inventory overview with low stock alerts
class InventoryOverview extends StatelessWidget {
  final List<InventoryItem> inventory;
  final List<InventoryItem> lowStockItems;
  final Function(InventoryItem) onItemSelected;

  const InventoryOverview({
    super.key,
    required this.inventory,
    required this.lowStockItems,
    required this.onItemSelected,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Inventory Stats
        _buildInventoryStats(context),

        const SizedBox(height: AppConfig.largePadding),

        // Low Stock Alert
        if (lowStockItems.isNotEmpty) ...[
          _buildLowStockAlert(context),
          const SizedBox(height: AppConfig.largePadding),
        ],

        // Inventory Items
        Text(
          'Inventory Items',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        
        ListView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          itemCount: inventory.length,
          itemBuilder: (context, index) {
            final item = inventory[index];
            return _buildInventoryItemCard(context, item, index);
          },
        ),
      ],
    );
  }

  Widget _buildInventoryStats(BuildContext context) {
    final totalItems = inventory.length;
    final lowStockCount = lowStockItems.length;
    final totalValue = inventory.fold<double>(
      0.0,
      (sum, item) => sum + (item.quantity * item.costPerUnit),
    );

    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor.withValues(alpha: 0.1),
            AppTheme.primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: AppTheme.primaryColor.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Inventory Overview',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          
          Row(
            children: [
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Items',
                  totalItems.toString(),
                  Icons.inventory,
                  AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Low Stock',
                  lowStockCount.toString(),
                  Icons.warning,
                  AppTheme.errorColor,
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: _buildStatCard(
                  context,
                  'Total Value',
                  '\$${totalValue.toStringAsFixed(0)}',
                  Icons.attach_money,
                  AppTheme.successColor,
                ),
              ),
            ],
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms);
  }

  Widget _buildStatCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 24,
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLowStockAlert(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.errorColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(
          color: AppTheme.errorColor.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.warning,
                color: AppTheme.errorColor,
                size: 20,
              ),
              const SizedBox(width: 8),
              Text(
                'Low Stock Alert',
                style: Theme.of(context).textTheme.titleSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.errorColor,
                ),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            '${lowStockItems.length} items are running low on stock',
            style: Theme.of(context).textTheme.bodyMedium,
          ),
          const SizedBox(height: 12),
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: lowStockItems.map((item) => Chip(
              label: Text(
                item.name,
                style: const TextStyle(fontSize: 12),
              ),
              backgroundColor: AppTheme.errorColor.withValues(alpha: 0.1),
              side: BorderSide(
                color: AppTheme.errorColor.withValues(alpha: 0.3),
              ),
            )).toList(),
          ),
          const SizedBox(height: 12),
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: () {
                Navigator.pushNamed(context, '/restock-items');
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.errorColor,
              ),
              child: const Text(
                'Restock Items',
                style: TextStyle(color: Colors.white),
              ),
            ),
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms);
  }

  Widget _buildInventoryItemCard(
    BuildContext context,
    InventoryItem item,
    int index,
  ) {
    final isLowStock = item.quantity <= item.minStock;

    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleButton(
        child: Card(
          elevation: 2,
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            side: isLowStock
                ? BorderSide(
                    color: AppTheme.errorColor.withValues(alpha: 0.3),
                    width: 1,
                  )
                : BorderSide.none,
          ),
          child: Padding(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            child: Row(
              children: [
                // Item Icon
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    color: isLowStock
                        ? AppTheme.errorColor.withValues(alpha: 0.1)
                        : AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(
                    _getCategoryIcon(item.category),
                    color: isLowStock ? AppTheme.errorColor : AppTheme.primaryColor,
                    size: 24,
                  ),
                ),

                const SizedBox(width: AppConfig.defaultPadding),

                // Item Details
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              item.name,
                              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          if (isLowStock)
                            Container(
                              padding: const EdgeInsets.symmetric(
                                horizontal: 6,
                                vertical: 2,
                              ),
                              decoration: BoxDecoration(
                                color: AppTheme.errorColor,
                                borderRadius: BorderRadius.circular(8),
                              ),
                              child: Text(
                                'LOW',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.bold,
                                  fontSize: 10,
                                ),
                              ),
                            ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        item.category,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      const SizedBox(height: 8),
                      Row(
                        children: [
                          Text(
                            'Stock: ${item.quantity} ${item.unit}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              fontWeight: FontWeight.w600,
                              color: isLowStock ? AppTheme.errorColor : null,
                            ),
                          ),
                          const SizedBox(width: 16),
                          Text(
                            'Min: ${item.minStock}',
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'Cost: \$${item.costPerUnit.toStringAsFixed(2)}/${item.unit}',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),

                // Action Button
                IconButton(
                  onPressed: () => onItemSelected(item),
                  icon: const Icon(Icons.arrow_forward_ios),
                  iconSize: 16,
                ),
              ],
            ),
          ),
        ),
        onPressed: () => onItemSelected(item),
        semanticLabel: 'View inventory item: ${item.name}',
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  IconData _getCategoryIcon(String category) {
    switch (category.toLowerCase()) {
      case 'fabrics':
        return Icons.texture;
      case 'threads':
        return Icons.linear_scale;
      case 'buttons':
        return Icons.radio_button_unchecked;
      case 'zippers':
        return Icons.vertical_align_center;
      case 'tools':
        return Icons.build;
      default:
        return Icons.inventory;
    }
  }
}
