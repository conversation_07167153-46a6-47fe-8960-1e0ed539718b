import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Price comparison and market trends widget
class PriceComparison extends StatelessWidget {
  final Map<String, dynamic> priceData;
  final VoidCallback onComparePressed;

  const PriceComparison({
    super.key,
    required this.priceData,
    required this.onComparePressed,
  });

  @override
  Widget build(BuildContext context) {
    final trendingItems = priceData['trending_items'] as List<dynamic>? ?? [];
    final bestDeals = priceData['best_deals'] as List<dynamic>? ?? [];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Market Insights',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            AccessibilityHelper.accessibleButton(
              child: Text(
                'Compare Prices',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onPressed: onComparePressed,
              semanticLabel: 'Compare prices across suppliers',
            ),
          ],
        ),
        const SizedBox(height: AppConfig.defaultPadding),

        // Trending Items
        if (trendingItems.isNotEmpty) ...[
          _buildTrendingItems(context, trendingItems),
          const SizedBox(height: AppConfig.defaultPadding),
        ],

        // Best Deals
        if (bestDeals.isNotEmpty) ...[
          _buildBestDeals(context, bestDeals),
        ],
      ],
    );
  }

  Widget _buildTrendingItems(BuildContext context, List<dynamic> items) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.trending_up,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Price Trends',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            ...items.asMap().entries.map((entry) {
              final index = entry.key;
              final item = entry.value as Map<String, dynamic>;
              final name = item['name'] as String? ?? '';
              final priceChange = item['price_change'] as String? ?? '';
              final isPositive = priceChange.startsWith('+');
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Expanded(
                      child: Text(
                        name,
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                    ),
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: isPositive
                            ? AppTheme.errorColor.withValues(alpha: 0.1)
                            : AppTheme.successColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Row(
                        mainAxisSize: MainAxisSize.min,
                        children: [
                          Icon(
                            isPositive ? Icons.arrow_upward : Icons.arrow_downward,
                            color: isPositive ? AppTheme.errorColor : AppTheme.successColor,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            priceChange,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: isPositive ? AppTheme.errorColor : AppTheme.successColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                  ],
                ),
              )
                  .animate(delay: (index * 100).ms)
                  .fadeIn(duration: 400.ms)
                  .slideX(begin: 0.3, end: 0, duration: 400.ms);
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildBestDeals(BuildContext context, List<dynamic> deals) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.local_offer,
                  color: AppTheme.successColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Best Deals',
                  style: Theme.of(context).textTheme.titleSmall?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ],
            ),
            const SizedBox(height: 12),
            
            ...deals.asMap().entries.map((entry) {
              final index = entry.key;
              final deal = entry.value as Map<String, dynamic>;
              final supplier = deal['supplier'] as String? ?? '';
              final discount = deal['discount'] as String? ?? '';
              
              return Container(
                margin: const EdgeInsets.only(bottom: 8),
                child: AccessibilityHelper.accessibleButton(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.successColor.withValues(alpha: 0.1),
                          AppTheme.successColor.withValues(alpha: 0.05),
                        ],
                        begin: Alignment.centerLeft,
                        end: Alignment.centerRight,
                      ),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(
                        color: AppTheme.successColor.withValues(alpha: 0.2),
                        width: 1,
                      ),
                    ),
                    child: Row(
                      children: [
                        Container(
                          width: 40,
                          height: 40,
                          decoration: BoxDecoration(
                            color: AppTheme.successColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Icon(
                            Icons.business,
                            color: AppTheme.successColor,
                            size: 20,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Expanded(
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Text(
                                supplier,
                                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                              Text(
                                'Special discount available',
                                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: AppTheme.secondaryTextColor,
                                ),
                              ),
                            ],
                          ),
                        ),
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 6,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.successColor,
                            borderRadius: BorderRadius.circular(12),
                          ),
                          child: Text(
                            discount,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Colors.white,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                        ),
                        const SizedBox(width: 8),
                        Icon(
                          Icons.arrow_forward_ios,
                          color: AppTheme.secondaryTextColor,
                          size: 16,
                        ),
                      ],
                    ),
                  ),
                  onPressed: () {
                    Navigator.pushNamed(
                      context,
                      '/supplier-details',
                      arguments: {'name': supplier},
                    );
                  },
                  semanticLabel: 'View deal from $supplier',
                ),
              )
                  .animate(delay: (index * 150).ms)
                  .fadeIn(duration: 500.ms)
                  .slideX(begin: 0.3, end: 0, duration: 500.ms);
            }),
          ],
        ),
      ),
    );
  }
}
