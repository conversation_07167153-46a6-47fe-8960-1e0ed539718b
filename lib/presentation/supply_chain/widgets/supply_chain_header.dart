import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/user_model.dart';

/// Supply chain header with user info and key metrics
class SupplyChainHeader extends StatelessWidget {
  final UserModel user;
  final int totalSuppliers;
  final int activeOrders;
  final double totalSpent;

  const SupplyChainHeader({
    super.key,
    required this.user,
    required this.totalSuppliers,
    required this.activeOrders,
    required this.totalSpent,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            AppTheme.primaryColor,
            AppTheme.primaryColor.withValues(alpha: 0.8),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(AppConfig.borderRadius),
          bottomRight: Radius.circular(AppConfig.borderRadius),
        ),
      ),
      child: Column(
        children: [
          // User Info Row
          Row(
            children: [
              // Profile Image
              CircleAvatar(
                radius: 30,
                backgroundImage: user.profileImageUrl != null
                    ? CachedNetworkImageProvider(user.profileImageUrl!)
                    : null,
                backgroundColor: Colors.white.withValues(alpha: 0.2),
                child: user.profileImageUrl == null
                    ? Text(
                        '${user.firstName[0]}${user.lastName[0]}',
                        style: const TextStyle(
                          color: Colors.white,
                          fontSize: 20,
                          fontWeight: FontWeight.bold,
                        ),
                      )
                    : null,
              ),

              const SizedBox(width: AppConfig.defaultPadding),

              // User Details
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Supply Chain',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: Colors.white.withValues(alpha: 0.9),
                      ),
                    ),
                    Text(
                      '${user.firstName} ${user.lastName}',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Manage your materials and suppliers',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Colors.white.withValues(alpha: 0.8),
                      ),
                    ),
                  ],
                ),
              ),

              // Quick Actions
              Column(
                children: [
                  AccessibilityHelper.accessibleButton(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.notifications_outlined,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    onPressed: () {
                      Navigator.pushNamed(context, '/notifications');
                    },
                    semanticLabel: 'View notifications',
                  ),
                  const SizedBox(height: 8),
                  AccessibilityHelper.accessibleButton(
                    child: Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.white.withValues(alpha: 0.2),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: const Icon(
                        Icons.qr_code_scanner,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    onPressed: () {
                      Navigator.pushNamed(context, '/barcode-scanner');
                    },
                    semanticLabel: 'Scan barcode',
                  ),
                ],
              ),
            ],
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Metrics Overview
          Container(
            padding: const EdgeInsets.all(AppConfig.defaultPadding),
            decoration: BoxDecoration(
              color: Colors.white.withValues(alpha: 0.15),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              border: Border.all(
                color: Colors.white.withValues(alpha: 0.3),
                width: 1,
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Suppliers',
                    totalSuppliers.toString(),
                    Icons.business,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.white.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Active Orders',
                    activeOrders.toString(),
                    Icons.shopping_cart,
                  ),
                ),
                Container(
                  width: 1,
                  height: 40,
                  color: Colors.white.withValues(alpha: 0.3),
                ),
                Expanded(
                  child: _buildMetricCard(
                    context,
                    'Total Spent',
                    '\$${totalSpent.toStringAsFixed(0)}',
                    Icons.attach_money,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: AppConfig.defaultPadding),

          // Quick Action Buttons
          Row(
            children: [
              Expanded(
                child: _buildQuickActionButton(
                  context,
                  'New Order',
                  Icons.add_shopping_cart,
                  () {
                    Navigator.pushNamed(context, '/create-supply-order');
                  },
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: _buildQuickActionButton(
                  context,
                  'Track Orders',
                  Icons.local_shipping,
                  () {
                    Navigator.pushNamed(context, '/track-orders');
                  },
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: _buildQuickActionButton(
                  context,
                  'Inventory',
                  Icons.inventory,
                  () {
                    Navigator.pushNamed(context, '/inventory');
                  },
                ),
              ),
            ],
          ),
        ],
      ),
    )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideY(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildMetricCard(
    BuildContext context,
    String label,
    String value,
    IconData icon,
  ) {
    return Column(
      children: [
        Icon(
          icon,
          color: Colors.white,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildQuickActionButton(
    BuildContext context,
    String label,
    IconData icon,
    VoidCallback onPressed,
  ) {
    return AccessibilityHelper.accessibleButton(
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: Colors.white.withValues(alpha: 0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: Colors.white.withValues(alpha: 0.3),
            width: 1,
          ),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: Colors.white,
              size: 16,
            ),
            const SizedBox(width: 4),
            Flexible(
              child: Text(
                label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.w600,
                ),
                overflow: TextOverflow.ellipsis,
              ),
            ),
          ],
        ),
      ),
      onPressed: onPressed,
      semanticLabel: label,
    );
  }
}
