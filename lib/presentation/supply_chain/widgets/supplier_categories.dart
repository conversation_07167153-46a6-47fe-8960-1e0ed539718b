import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/supply_chain_bloc.dart';

/// Supplier categories horizontal list
class SupplierCategories extends StatefulWidget {
  final List<SupplierCategory> categories;
  final Function(String) onCategorySelected;

  const SupplierCategories({
    super.key,
    required this.categories,
    required this.onCategorySelected,
  });

  @override
  State<SupplierCategories> createState() => _SupplierCategoriesState();
}

class _SupplierCategoriesState extends State<SupplierCategories> {
  String? _selectedCategory;

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Material Categories',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        
        SizedBox(
          height: 100,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: widget.categories.length,
            itemBuilder: (context, index) {
              final category = widget.categories[index];
              final isSelected = _selectedCategory == category.id;
              
              return _buildCategoryCard(category, isSelected, index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildCategoryCard(
    SupplierCategory category,
    bool isSelected,
    int index,
  ) {
    return Container(
      width: 80,
      margin: const EdgeInsets.only(right: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleButton(
        child: Column(
          children: [
            // Category Icon
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                gradient: isSelected ? AppTheme.instagramGradient : null,
                color: isSelected ? null : AppTheme.backgroundColor,
                borderRadius: BorderRadius.circular(16),
                border: Border.all(
                  color: isSelected 
                      ? Colors.transparent 
                      : AppTheme.dividerColor,
                  width: 1,
                ),
                boxShadow: isSelected
                    ? [
                        BoxShadow(
                          color: AppTheme.primaryColor.withValues(alpha: 0.3),
                          blurRadius: 8,
                          offset: const Offset(0, 4),
                        ),
                      ]
                    : null,
              ),
              child: Icon(
                _getCategoryIcon(category.icon),
                color: isSelected ? Colors.white : AppTheme.primaryColor,
                size: 24,
              ),
            ),

            const SizedBox(height: 8),

            // Category Name
            Text(
              category.name,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                fontWeight: isSelected ? FontWeight.bold : FontWeight.normal,
                color: isSelected 
                    ? AppTheme.primaryColor 
                    : AppTheme.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
          ],
        ),
        onPressed: () {
          setState(() {
            _selectedCategory = isSelected ? null : category.id;
          });
          
          if (!isSelected) {
            widget.onCategorySelected(category.name);
          }
        },
        semanticLabel: 'Select ${category.name} category',
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: 0.3, end: 0, duration: 400.ms);
  }

  IconData _getCategoryIcon(String iconName) {
    switch (iconName) {
      case 'fabric':
        return Icons.texture;
      case 'thread':
        return Icons.linear_scale;
      case 'button':
        return Icons.radio_button_unchecked;
      case 'zipper':
        return Icons.vertical_align_center;
      case 'tools':
        return Icons.build;
      default:
        return Icons.category;
    }
  }
}
