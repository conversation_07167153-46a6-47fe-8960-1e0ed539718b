import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';

import '../../data/models/tailor_model.dart';
import 'bloc/order_creation_bloc.dart';
import 'widgets/order_step_indicator.dart';
import 'widgets/service_selection_step.dart';
import 'widgets/measurements_step.dart';
import 'widgets/fabric_selection_step.dart';
import 'widgets/delivery_options_step.dart';
import 'widgets/order_summary_step.dart';

/// Instagram-inspired multi-step order creation screen
class OrderCreationScreen extends StatefulWidget {
  final TailorModel tailor;
  final String? preSelectedService;

  const OrderCreationScreen({
    super.key,
    required this.tailor,
    this.preSelectedService,
  });

  @override
  State<OrderCreationScreen> createState() => _OrderCreationScreenState();
}

class _OrderCreationScreenState extends State<OrderCreationScreen>
    with TickerProviderStateMixin {
  late PageController _pageController;
  late AnimationController _progressController;
  int _currentStep = 0;

  final List<OrderStep> _steps = [
    OrderStep(
      title: 'Service',
      subtitle: 'Choose service',
      icon: Icons.design_services,
    ),
    OrderStep(
      title: 'Measurements',
      subtitle: 'Add measurements',
      icon: Icons.straighten,
    ),
    OrderStep(
      title: 'Fabric',
      subtitle: 'Select fabric',
      icon: Icons.texture,
    ),
    OrderStep(
      title: 'Delivery',
      subtitle: 'Delivery options',
      icon: Icons.local_shipping,
    ),
    OrderStep(
      title: 'Summary',
      subtitle: 'Review order',
      icon: Icons.receipt_long,
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
    _progressController = AnimationController(
      duration: AppConfig.mediumAnimation,
      vsync: this,
    );
  }

  @override
  void dispose() {
    _pageController.dispose();
    _progressController.dispose();
    super.dispose();
  }

  void _nextStep() {
    if (_currentStep < _steps.length - 1) {
      setState(() {
        _currentStep++;
      });
      _pageController.nextPage(
        duration: AppConfig.mediumAnimation,
        curve: Curves.easeInOut,
      );
      _updateProgress();
    }
  }

  void _previousStep() {
    if (_currentStep > 0) {
      setState(() {
        _currentStep--;
      });
      _pageController.previousPage(
        duration: AppConfig.mediumAnimation,
        curve: Curves.easeInOut,
      );
      _updateProgress();
    }
  }

  void _updateProgress() {
    final progress = (_currentStep + 1) / _steps.length;
    _progressController.animateTo(progress);
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => OrderCreationBloc(

        tailor: widget.tailor,
      )..add(InitializeOrder(preSelectedService: widget.preSelectedService)),
      child: Scaffold(
        backgroundColor: Colors.white,
        body: SafeArea(
          child: Column(
            children: [
              // Header with Progress
              _buildHeader(context),

              // Step Content
              Expanded(
                child: PageView.builder(
                  controller: _pageController,
                  onPageChanged: (index) {
                    setState(() {
                      _currentStep = index;
                    });
                    _updateProgress();
                  },
                  itemCount: _steps.length,
                  itemBuilder: (context, index) {
                    return _buildStepContent(index);
                  },
                ),
              ),

              // Bottom Navigation
              _buildBottomNavigation(context),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top Row
          Row(
            children: [
              AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.arrow_back_ios,
                    size: 16,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                onPressed: () {
                  if (_currentStep > 0) {
                    _previousStep();
                  } else {
                    Navigator.of(context).pop();
                  }
                },
                semanticLabel: _currentStep > 0 ? 'Go to previous step' : 'Cancel order',
              ),
              Expanded(
                child: Column(
                  children: [
                    Text(
                      'Create Order',
                      style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    Text(
                      'with ${widget.tailor.name}',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
              AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.backgroundColor,
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: const Icon(
                    Icons.close,
                    size: 16,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                onPressed: () => Navigator.of(context).pop(),
                semanticLabel: 'Cancel order creation',
              ),
            ],
          ),

          const SizedBox(height: AppConfig.defaultPadding),

          // Progress Indicator
          OrderStepIndicator(
            steps: _steps,
            currentStep: _currentStep,
            progressController: _progressController,
          ),
        ],
      ),
    );
  }

  Widget _buildStepContent(int stepIndex) {
    switch (stepIndex) {
      case 0:
        return ServiceSelectionStep(
          tailor: widget.tailor,
          preSelectedService: widget.preSelectedService,
        );
      case 1:
        return const MeasurementsStep();
      case 2:
        return const FabricSelectionStep();
      case 3:
        return const DeliveryOptionsStep();
      case 4:
        return OrderSummaryStep(tailor: widget.tailor);
      default:
        return const SizedBox.shrink();
    }
  }

  Widget _buildBottomNavigation(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: BlocBuilder<OrderCreationBloc, OrderCreationState>(
          builder: (context, state) {
            final canProceed = _canProceedToNextStep(state);
            final isLastStep = _currentStep == _steps.length - 1;

            return Row(
              children: [
                // Previous Button
                if (_currentStep > 0)
                  Expanded(
                    child: AccessibilityHelper.accessibleButton(
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 16),
                        decoration: BoxDecoration(
                          color: AppTheme.backgroundColor,
                          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                          border: Border.all(color: AppTheme.dividerColor),
                        ),
                        child: Text(
                          'Previous',
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            color: AppTheme.primaryTextColor,
                            fontWeight: FontWeight.w600,
                          ),
                          textAlign: TextAlign.center,
                        ),
                      ),
                      onPressed: _previousStep,
                      semanticLabel: 'Go to previous step',
                    ),
                  ),

                if (_currentStep > 0) const SizedBox(width: 12),

                // Next/Complete Button
                Expanded(
                  flex: _currentStep > 0 ? 1 : 1,
                  child: AccessibilityHelper.accessibleButton(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: BoxDecoration(
                        gradient: canProceed ? AppTheme.instagramGradient : null,
                        color: canProceed ? null : AppTheme.dividerColor,
                        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          if (state is OrderCreationLoading)
                            const SizedBox(
                              width: 20,
                              height: 20,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                              ),
                            )
                          else
                            Text(
                              isLastStep ? 'Place Order' : 'Next',
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                color: canProceed ? Colors.white : AppTheme.secondaryTextColor,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          if (!isLastStep && canProceed) ...[
                            const SizedBox(width: 8),
                            const Icon(
                              Icons.arrow_forward_ios,
                              color: Colors.white,
                              size: 16,
                            ),
                          ],
                        ],
                      ),
                    ),
                    onPressed: canProceed
                        ? () {
                            if (isLastStep) {
                              context.read<OrderCreationBloc>().add(const PlaceOrder());
                            } else {
                              _nextStep();
                            }
                          }
                        : null,
                    semanticLabel: isLastStep ? 'Place order' : 'Go to next step',
                  ),
                ),
              ],
            );
          },
        ),
      ),
    );
  }

  bool _canProceedToNextStep(OrderCreationState state) {
    if (state is OrderCreationLoading) return false;
    
    if (state is OrderCreationLoaded) {
      switch (_currentStep) {
        case 0: // Service Selection
          return state.selectedService != null;
        case 1: // Measurements
          return state.measurements.isNotEmpty;
        case 2: // Fabric Selection
          return state.selectedFabric != null;
        case 3: // Delivery Options
          return state.deliveryOption != null;
        case 4: // Summary
          return true;
        default:
          return false;
      }
    }
    
    return false;
  }
}

class OrderStep {
  final String title;
  final String subtitle;
  final IconData icon;

  const OrderStep({
    required this.title,
    required this.subtitle,
    required this.icon,
  });
}
