import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';
import '../bloc/order_creation_bloc.dart';

/// Service selection step in order creation
class ServiceSelectionStep extends StatelessWidget {
  final TailorModel tailor;
  final String? preSelectedService;

  const ServiceSelectionStep({
    super.key,
    required this.tailor,
    this.preSelectedService,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderCreationBloc, OrderCreationState>(
      builder: (context, state) {
        if (state is OrderCreationLoading) {
          return AccessibilityHelper.accessibleLoadingIndicator(
            semanticLabel: 'Loading services',
          );
        }

        if (state is OrderCreationLoaded) {
          return _buildServiceSelection(context, state);
        }

        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildServiceSelection(BuildContext context, OrderCreationLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Choose Service',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select the service you need from ${tailor.name}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Services List
          ...state.availableServices.asMap().entries.map((entry) {
            final index = entry.key;
            final service = entry.value;
            final isSelected = state.selectedService?.id == service.id;

            return _buildServiceCard(context, service, isSelected, index);
          }),

          const SizedBox(height: AppConfig.largePadding),

          // Custom Service Option
          _buildCustomServiceCard(context),
        ],
      ),
    );
  }

  Widget _buildServiceCard(
    BuildContext context,
    ServiceModel service,
    bool isSelected,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleCard(
        semanticLabel: 'Service: ${service.name}, Price: ₮${service.basePrice}',
        onTap: () {
          context.read<OrderCreationBloc>().add(SelectService(service: service));
        },
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            color: isSelected
                ? AppTheme.primaryColor.withValues(alpha: 0.05)
                : Colors.white,
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Service Header
              Row(
                children: [
                  // Service Icon
                  Container(
                    width: 50,
                    height: 50,
                    decoration: BoxDecoration(
                      gradient: isSelected
                          ? AppTheme.instagramGradient
                          : LinearGradient(
                              colors: [
                                AppTheme.backgroundColor,
                                AppTheme.backgroundColor,
                              ],
                            ),
                      borderRadius: BorderRadius.circular(25),
                    ),
                    child: Icon(
                      _getServiceIcon(service.category),
                      color: isSelected ? Colors.white : AppTheme.primaryTextColor,
                      size: 24,
                    ),
                  ),

                  const SizedBox(width: AppConfig.defaultPadding),

                  // Service Info
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          service.name,
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(height: 4),
                        Text(
                          service.category,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),

                  // Price
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        '₮${service.basePrice.toStringAsFixed(0)}',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryColor,
                        ),
                      ),
                      Text(
                        'Starting from',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),

                  // Selection Indicator
                  const SizedBox(width: 12),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                ],
              ),

              const SizedBox(height: AppConfig.defaultPadding),

              // Service Description
              Text(
                service.description,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryTextColor,
                  height: 1.5,
                ),
              ),

              const SizedBox(height: AppConfig.defaultPadding),

              // Service Details
              Row(
                children: [
                  _buildServiceDetail(
                    context,
                    Icons.schedule,
                    '${service.estimatedDays} days',
                  ),
                  const SizedBox(width: AppConfig.largePadding),
                  _buildServiceDetail(
                    context,
                    Icons.star,
                    '4.8 rating',
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms);
  }

  Widget _buildServiceDetail(BuildContext context, IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(
          icon,
          size: 16,
          color: AppTheme.secondaryTextColor,
        ),
        const SizedBox(width: 4),
        Text(
          text,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.secondaryTextColor,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomServiceCard(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Request custom service',
      onTap: () {
        // TODO: Show custom service dialog
      },
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.dividerColor),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          color: AppTheme.backgroundColor,
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                Icons.add_circle_outline,
                color: AppTheme.primaryColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Custom Service',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Don\'t see what you need? Request a custom service',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  IconData _getServiceIcon(String category) {
    switch (category.toLowerCase()) {
      case 'formal wear':
        return Icons.business_center;
      case 'casual wear':
        return Icons.checkroom;
      case 'traditional':
        return Icons.festival;
      case 'wedding':
        return Icons.favorite;
      case 'alterations':
        return Icons.content_cut;
      default:
        return Icons.design_services;
    }
  }
}
