import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';
import '../bloc/order_creation_bloc.dart';

/// Order summary step in order creation
class OrderSummaryStep extends StatelessWidget {
  final TailorModel tailor;

  const OrderSummaryStep({
    super.key,
    required this.tailor,
  });

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderCreationBloc, OrderCreationState>(
      builder: (context, state) {
        if (state is OrderCreationLoaded) {
          return _buildOrderSummary(context, state);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildOrderSummary(BuildContext context, OrderCreationLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Order Summary',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Review your order details before placing',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Tailor Info
          _buildTailorInfo(context),

          const SizedBox(height: AppConfig.largePadding),

          // Service Details
          _buildServiceDetails(context, state),

          const SizedBox(height: AppConfig.largePadding),

          // Measurements Summary
          if (state.measurements.isNotEmpty)
            _buildMeasurementsSummary(context, state),

          const SizedBox(height: AppConfig.largePadding),

          // Fabric Details
          if (state.selectedFabric != null)
            _buildFabricDetails(context, state),

          const SizedBox(height: AppConfig.largePadding),

          // Delivery Details
          if (state.deliveryOption != null)
            _buildDeliveryDetails(context, state),

          const SizedBox(height: AppConfig.largePadding),

          // Price Breakdown
          _buildPriceBreakdown(context, state),

          const SizedBox(height: AppConfig.largePadding),

          // Terms and Conditions
          _buildTermsAndConditions(context),
        ],
      ),
    );
  }

  Widget _buildTailorInfo(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Tailor information: ${tailor.name}',
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Row(
          children: [
            // Tailor Avatar
            CircleAvatar(
              radius: 30,
              backgroundImage: CachedNetworkImageProvider(
                tailor.imageUrl ?? 'https://via.placeholder.com/60x60',
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            // Tailor Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Text(
                        tailor.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      if (tailor.isVerified) ...[
                        const SizedBox(width: 4),
                        const Icon(
                          Icons.verified,
                          color: AppTheme.primaryColor,
                          size: 16,
                        ),
                      ],
                    ],
                  ),
                  const SizedBox(height: 4),
                  Text(
                    tailor.categories.take(2).join(' • '),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.star,
                        color: Colors.amber,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        tailor.rating.toStringAsFixed(1),
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          fontWeight: FontWeight.w600,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Icon(
                        Icons.location_on,
                        color: AppTheme.secondaryTextColor,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        tailor.location?.distance != null
                            ? '${tailor.location!.distance!.toStringAsFixed(1)} km'
                            : 'Unknown',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildServiceDetails(BuildContext context, OrderCreationLoaded state) {
    if (state.selectedService == null) return const SizedBox.shrink();

    return _buildSummarySection(
      context,
      title: 'Service',
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              gradient: AppTheme.instagramGradient,
              borderRadius: BorderRadius.circular(25),
            ),
            child: const Icon(
              Icons.design_services,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConfig.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.selectedService!.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  state.selectedService!.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            '₮${state.selectedService!.basePrice.toStringAsFixed(0)}',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMeasurementsSummary(BuildContext context, OrderCreationLoaded state) {
    return _buildSummarySection(
      context,
      title: 'Measurements',
      child: Column(
        children: state.measurements.entries.map((entry) {
          return Padding(
            padding: const EdgeInsets.symmetric(vertical: 4),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  entry.key.toUpperCase(),
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                Text(
                  '${entry.value} cm',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ],
            ),
          );
        }).toList(),
      ),
    );
  }

  Widget _buildFabricDetails(BuildContext context, OrderCreationLoaded state) {
    return _buildSummarySection(
      context,
      title: 'Fabric',
      child: Row(
        children: [
          ClipRRect(
            borderRadius: BorderRadius.circular(8),
            child: AccessibilityHelper.accessibleImage(
              image: CachedNetworkImageProvider(
                state.selectedFabric!.imageUrl ?? 'https://via.placeholder.com/50x50',
              ),
              semanticLabel: '${state.selectedFabric!.name} fabric',
              width: 50,
              height: 50,
              fit: BoxFit.cover,
            ),
          ),
          const SizedBox(width: AppConfig.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.selectedFabric!.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  state.selectedFabric!.description,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            state.selectedFabric!.additionalCost > 0
                ? '+₮${state.selectedFabric!.additionalCost.toStringAsFixed(0)}'
                : 'Included',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: state.selectedFabric!.additionalCost > 0
                  ? AppTheme.primaryColor
                  : AppTheme.successColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildDeliveryDetails(BuildContext context, OrderCreationLoaded state) {
    return _buildSummarySection(
      context,
      title: 'Delivery',
      child: Row(
        children: [
          Container(
            width: 50,
            height: 50,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(25),
            ),
            child: Icon(
              Icons.local_shipping,
              color: AppTheme.primaryColor,
              size: 24,
            ),
          ),
          const SizedBox(width: AppConfig.defaultPadding),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  state.deliveryOption!.name,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  '${state.deliveryOption!.estimatedDays} days',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ),
          Text(
            state.deliveryOption!.cost > 0
                ? '₮${state.deliveryOption!.cost.toStringAsFixed(0)}'
                : 'Free',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: state.deliveryOption!.cost > 0
                  ? AppTheme.primaryColor
                  : AppTheme.successColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceBreakdown(BuildContext context, OrderCreationLoaded state) {
    return _buildSummarySection(
      context,
      title: 'Price Breakdown',
      child: Column(
        children: [
          _buildPriceRow(
            context,
            'Service',
            '₮${state.selectedService?.basePrice.toStringAsFixed(0) ?? '0'}',
          ),
          if (state.selectedFabric != null && state.selectedFabric!.additionalCost > 0)
            _buildPriceRow(
              context,
              'Fabric',
              '₮${state.selectedFabric!.additionalCost.toStringAsFixed(0)}',
            ),
          if (state.deliveryOption != null && state.deliveryOption!.cost > 0)
            _buildPriceRow(
              context,
              'Delivery',
              '₮${state.deliveryOption!.cost.toStringAsFixed(0)}',
            ),
          const Divider(),
          _buildPriceRow(
            context,
            'Total',
            '₮${state.totalPrice.toStringAsFixed(0)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    BuildContext context,
    String label,
    String price, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: AppTheme.primaryTextColor,
            ),
          ),
          Text(
            price,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isTotal ? AppTheme.primaryColor : AppTheme.primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTermsAndConditions(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Terms and conditions',
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Terms & Conditions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              '• Payment is required before work begins\n'
              '• Delivery times are estimates and may vary\n'
              '• Cancellations must be made 24 hours in advance\n'
              '• Final measurements will be taken before starting',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: AppTheme.secondaryTextColor,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSummarySection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: '$title section',
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            child,
          ],
        ),
      ),
    );
  }
}
