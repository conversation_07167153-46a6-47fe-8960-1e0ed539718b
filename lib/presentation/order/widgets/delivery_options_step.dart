import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/order_creation_bloc.dart';

/// Delivery options step in order creation
class DeliveryOptionsStep extends StatefulWidget {
  const DeliveryOptionsStep({super.key});

  @override
  State<DeliveryOptionsStep> createState() => _DeliveryOptionsStepState();
}

class _DeliveryOptionsStepState extends State<DeliveryOptionsStep> {
  final TextEditingController _addressController = TextEditingController();
  final TextEditingController _instructionsController = TextEditingController();
  DateTime? _preferredDate;
  TimeOfDay? _preferredTime;

  @override
  void dispose() {
    _addressController.dispose();
    _instructionsController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderCreationBloc, OrderCreationState>(
      builder: (context, state) {
        if (state is OrderCreationLoaded) {
          return _buildDeliveryOptions(context, state);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildDeliveryOptions(BuildContext context, OrderCreationLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Delivery Options',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Choose how and when you\'d like to receive your order',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Delivery Methods
          Text(
            'Delivery Method',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          ...state.deliveryOptions.asMap().entries.map((entry) {
            final index = entry.key;
            final option = entry.value;
            final isSelected = state.deliveryOption?.id == option.id;
            return _buildDeliveryOptionCard(context, option, isSelected, index);
          }),

          const SizedBox(height: AppConfig.largePadding),

          // Delivery Address
          _buildDeliveryAddress(context),

          const SizedBox(height: AppConfig.largePadding),

          // Preferred Delivery Time
          _buildPreferredTime(context),

          const SizedBox(height: AppConfig.largePadding),

          // Special Instructions
          _buildSpecialInstructions(context),
        ],
      ),
    );
  }

  Widget _buildDeliveryOptionCard(
    BuildContext context,
    DeliveryOption option,
    bool isSelected,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleCard(
        semanticLabel: 'Delivery option: ${option.name}, ${option.estimatedDays} days, ₮${option.cost}',
        onTap: () {
          context.read<OrderCreationBloc>().add(
            SelectDeliveryOption(deliveryOption: option),
          );
        },
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            border: Border.all(
              color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
              width: isSelected ? 2 : 1,
            ),
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            color: isSelected
                ? AppTheme.primaryColor.withValues(alpha: 0.05)
                : Colors.white,
          ),
          child: Row(
            children: [
              // Delivery Icon
              Container(
                width: 50,
                height: 50,
                decoration: BoxDecoration(
                  gradient: isSelected
                      ? AppTheme.instagramGradient
                      : LinearGradient(
                          colors: [
                            AppTheme.backgroundColor,
                            AppTheme.backgroundColor,
                          ],
                        ),
                  borderRadius: BorderRadius.circular(25),
                ),
                child: Icon(
                  _getDeliveryIcon(option.name),
                  color: isSelected ? Colors.white : AppTheme.primaryTextColor,
                  size: 24,
                ),
              ),

              const SizedBox(width: AppConfig.defaultPadding),

              // Delivery Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      option.name,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      option.description,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                    const SizedBox(height: 8),
                    Row(
                      children: [
                        Icon(
                          Icons.schedule,
                          size: 16,
                          color: AppTheme.secondaryTextColor,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          '${option.estimatedDays} days',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ],
                ),
              ),

              // Price and Selection
              Column(
                crossAxisAlignment: CrossAxisAlignment.end,
                children: [
                  Text(
                    option.cost > 0 ? '₮${option.cost.toStringAsFixed(0)}' : 'Free',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: option.cost > 0 ? AppTheme.primaryColor : AppTheme.successColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Container(
                    width: 24,
                    height: 24,
                    decoration: BoxDecoration(
                      color: isSelected ? AppTheme.primaryColor : Colors.transparent,
                      border: Border.all(
                        color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
                        width: 2,
                      ),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: isSelected
                        ? const Icon(
                            Icons.check,
                            color: Colors.white,
                            size: 16,
                          )
                        : null,
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildDeliveryAddress(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Delivery Address',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        AccessibilityHelper.accessibleTextField(
          controller: _addressController,
          label: 'Address',
          hint: 'Enter your delivery address',
          semanticLabel: 'Delivery address',
          keyboardType: TextInputType.streetAddress,
          prefixIcon: const Icon(Icons.location_on),
        ),
        const SizedBox(height: 12),
        AccessibilityHelper.accessibleButton(
          child: Container(
            padding: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              border: Border.all(color: AppTheme.primaryColor),
              borderRadius: BorderRadius.circular(AppConfig.borderRadius),
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(
                  Icons.my_location,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Use Current Location',
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
          onPressed: () {
            // TODO: Get current location
          },
          semanticLabel: 'Use current location for delivery',
        ),
      ],
    );
  }

  Widget _buildPreferredTime(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Preferred Delivery Time',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        Row(
          children: [
            Expanded(
              child: AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.dividerColor),
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.calendar_today,
                        color: AppTheme.secondaryTextColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _preferredDate != null
                            ? '${_preferredDate!.day}/${_preferredDate!.month}/${_preferredDate!.year}'
                            : 'Select Date',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _preferredDate != null
                              ? AppTheme.primaryTextColor
                              : AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                onPressed: () => _selectDate(context),
                semanticLabel: 'Select preferred delivery date',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.dividerColor),
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.access_time,
                        color: AppTheme.secondaryTextColor,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        _preferredTime != null
                            ? _preferredTime!.format(context)
                            : 'Select Time',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: _preferredTime != null
                              ? AppTheme.primaryTextColor
                              : AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                onPressed: () => _selectTime(context),
                semanticLabel: 'Select preferred delivery time',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSpecialInstructions(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Special Instructions',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        AccessibilityHelper.accessibleTextField(
          controller: _instructionsController,
          label: 'Instructions',
          hint: 'Any special delivery instructions...',
          semanticLabel: 'Special delivery instructions',
          keyboardType: TextInputType.multiline,
        ),
      ],
    );
  }

  IconData _getDeliveryIcon(String deliveryName) {
    switch (deliveryName.toLowerCase()) {
      case 'standard delivery':
        return Icons.local_shipping;
      case 'express delivery':
        return Icons.flash_on;
      case 'pickup':
        return Icons.store;
      default:
        return Icons.local_shipping;
    }
  }

  Future<void> _selectDate(BuildContext context) async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    
    if (date != null) {
      setState(() {
        _preferredDate = date;
      });
    }
  }

  Future<void> _selectTime(BuildContext context) async {
    final time = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 9, minute: 0),
    );
    
    if (time != null) {
      setState(() {
        _preferredTime = time;
      });
    }
  }
}
