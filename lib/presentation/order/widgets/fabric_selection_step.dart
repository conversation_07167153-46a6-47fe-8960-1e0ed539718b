import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/order_creation_bloc.dart';

/// Fabric selection step in order creation
class FabricSelectionStep extends StatelessWidget {
  const FabricSelectionStep({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderCreationBloc, OrderCreationState>(
      builder: (context, state) {
        if (state is OrderCreationLoaded) {
          return _buildFabricSelection(context, state);
        }
        return const SizedBox.shrink();
      },
    );
  }

  Widget _buildFabricSelection(BuildContext context, OrderCreationLoaded state) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Choose Fabric',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Select the perfect fabric for your ${state.selectedService?.name.toLowerCase()}',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Fabric Categories
          _buildFabricCategories(context),

          const SizedBox(height: AppConfig.largePadding),

          // Available Fabrics
          Text(
            'Available Fabrics',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          // Fabric Grid
          GridView.builder(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
              crossAxisCount: 2,
              crossAxisSpacing: AppConfig.defaultPadding,
              mainAxisSpacing: AppConfig.defaultPadding,
              childAspectRatio: 0.8,
            ),
            itemCount: state.availableFabrics.length,
            itemBuilder: (context, index) {
              final fabric = state.availableFabrics[index];
              final isSelected = state.selectedFabric?.id == fabric.id;
              return _buildFabricCard(context, fabric, isSelected, index);
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Custom Fabric Option
          _buildCustomFabricCard(context),
        ],
      ),
    );
  }

  Widget _buildFabricCategories(BuildContext context) {
    final categories = ['Premium', 'Standard', 'Eco-Friendly', 'Luxury'];
    
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Fabric Categories',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        SizedBox(
          height: 40,
          child: ListView.builder(
            scrollDirection: Axis.horizontal,
            itemCount: categories.length,
            itemBuilder: (context, index) {
              final category = categories[index];
              final isSelected = index == 0; // Mock selection
              
              return Container(
                margin: const EdgeInsets.only(right: 12),
                child: AccessibilityHelper.accessibleButton(
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 20,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      gradient: isSelected ? AppTheme.instagramGradient : null,
                      color: isSelected ? null : AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: isSelected ? Colors.transparent : AppTheme.dividerColor,
                      ),
                    ),
                    child: Text(
                      category,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isSelected ? Colors.white : AppTheme.primaryTextColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                  onPressed: () {
                    // TODO: Filter fabrics by category
                  },
                  semanticLabel: 'Filter by $category fabrics',
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildFabricCard(
    BuildContext context,
    FabricModel fabric,
    bool isSelected,
    int index,
  ) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Fabric: ${fabric.name}, Additional cost: ₮${fabric.additionalCost}',
      onTap: () {
        context.read<OrderCreationBloc>().add(SelectFabric(fabric: fabric));
      },
      child: Container(
        decoration: BoxDecoration(
          border: Border.all(
            color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
            width: isSelected ? 2 : 1,
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          color: isSelected
              ? AppTheme.primaryColor.withValues(alpha: 0.05)
              : Colors.white,
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Fabric Image
            Expanded(
              flex: 3,
              child: Stack(
                children: [
                  ClipRRect(
                    borderRadius: const BorderRadius.only(
                      topLeft: Radius.circular(AppConfig.borderRadius),
                      topRight: Radius.circular(AppConfig.borderRadius),
                    ),
                    child: AccessibilityHelper.accessibleImage(
                      image: CachedNetworkImageProvider(
                        fabric.imageUrl ?? 'https://via.placeholder.com/200x150',
                      ),
                      semanticLabel: '${fabric.name} fabric sample',
                      width: double.infinity,
                      fit: BoxFit.cover,
                    ),
                  ),
                  
                  // Selection Indicator
                  if (isSelected)
                    Positioned(
                      top: 8,
                      right: 8,
                      child: Container(
                        width: 24,
                        height: 24,
                        decoration: BoxDecoration(
                          color: AppTheme.primaryColor,
                          borderRadius: BorderRadius.circular(12),
                        ),
                        child: const Icon(
                          Icons.check,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                ],
              ),
            ),

            // Fabric Info
            Expanded(
              flex: 2,
              child: Padding(
                padding: const EdgeInsets.all(12),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Fabric Name
                    Text(
                      fabric.name,
                      style: Theme.of(context).textTheme.titleSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppTheme.primaryTextColor,
                      ),
                      maxLines: 1,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const SizedBox(height: 4),
                    
                    // Fabric Description
                    Text(
                      fabric.description,
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                      maxLines: 2,
                      overflow: TextOverflow.ellipsis,
                    ),
                    
                    const Spacer(),
                    
                    // Additional Cost
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        Text(
                          fabric.additionalCost > 0 ? '+₮${fabric.additionalCost.toStringAsFixed(0)}' : 'Included',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: fabric.additionalCost > 0 ? AppTheme.primaryColor : AppTheme.successColor,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        if (isSelected)
                          Icon(
                            Icons.check_circle,
                            color: AppTheme.primaryColor,
                            size: 16,
                          ),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .scale(begin: const Offset(0.8, 0.8), duration: 600.ms);
  }

  Widget _buildCustomFabricCard(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Bring your own fabric',
      onTap: () {
        // TODO: Show custom fabric dialog
      },
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.dividerColor),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          color: AppTheme.backgroundColor,
        ),
        child: Row(
          children: [
            Container(
              width: 60,
              height: 60,
              decoration: BoxDecoration(
                color: AppTheme.primaryColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
              ),
              child: Icon(
                Icons.upload_file,
                color: AppTheme.primaryColor,
                size: 30,
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Bring Your Own Fabric',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Have a specific fabric in mind? Bring it to the tailor',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'No additional cost',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.successColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }
}
