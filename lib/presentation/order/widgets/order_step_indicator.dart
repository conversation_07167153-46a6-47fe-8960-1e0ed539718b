import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../order_creation_screen.dart';

/// Instagram-inspired step indicator for order creation
class OrderStepIndicator extends StatelessWidget {
  final List<OrderStep> steps;
  final int currentStep;
  final AnimationController progressController;

  const OrderStepIndicator({
    super.key,
    required this.steps,
    required this.currentStep,
    required this.progressController,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Progress Bar
        Container(
          height: 4,
          decoration: BoxDecoration(
            color: AppTheme.dividerColor,
            borderRadius: BorderRadius.circular(2),
          ),
          child: AnimatedBuilder(
            animation: progressController,
            builder: (context, child) {
              return FractionallySizedBox(
                alignment: Alignment.centerLeft,
                widthFactor: progressController.value,
                child: Container(
                  decoration: BoxDecoration(
                    gradient: AppTheme.instagramGradient,
                    borderRadius: BorderRadius.circular(2),
                  ),
                ),
              );
            },
          ),
        ),

        const SizedBox(height: AppConfig.defaultPadding),

        // Step Indicators
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceEvenly,
          children: steps.asMap().entries.map((entry) {
            final index = entry.key;
            final step = entry.value;
            return _buildStepItem(context, step, index);
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildStepItem(BuildContext context, OrderStep step, int index) {
    final isActive = index == currentStep;
    final isCompleted = index < currentStep;
    final isUpcoming = index > currentStep;

    return Expanded(
      child: Column(
        children: [
          // Step Circle
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              gradient: isActive || isCompleted ? AppTheme.instagramGradient : null,
              color: isUpcoming ? AppTheme.dividerColor : null,
              borderRadius: BorderRadius.circular(20),
              border: isUpcoming
                  ? Border.all(color: AppTheme.dividerColor, width: 2)
                  : null,
            ),
            child: Icon(
              isCompleted ? Icons.check : step.icon,
              color: isUpcoming ? AppTheme.secondaryTextColor : Colors.white,
              size: 20,
            ),
          )
              .animate(target: isActive ? 1 : 0)
              .scale(
                begin: const Offset(1.0, 1.0),
                end: const Offset(1.1, 1.1),
                duration: 300.ms,
              ),

          const SizedBox(height: 8),

          // Step Title
          Text(
            step.title,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: isActive ? FontWeight.bold : FontWeight.w500,
              color: isActive
                  ? AppTheme.primaryTextColor
                  : isUpcoming
                      ? AppTheme.secondaryTextColor
                      : AppTheme.primaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),

          // Step Subtitle
          Text(
            step.subtitle,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontSize: 10,
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }
}
