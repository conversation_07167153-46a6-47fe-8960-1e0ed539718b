import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/order_creation_bloc.dart';

/// Measurements input step in order creation
class MeasurementsStep extends StatefulWidget {
  const MeasurementsStep({super.key});

  @override
  State<MeasurementsStep> createState() => _MeasurementsStepState();
}

class _MeasurementsStepState extends State<MeasurementsStep> {
  final Map<String, TextEditingController> _controllers = {};
  final Map<String, double> _measurements = {};

  final List<MeasurementField> _measurementFields = [
    MeasurementField(
      key: 'chest',
      label: 'Chest',
      hint: 'Around fullest part',
      unit: 'cm',
      icon: Icons.straighten,
    ),
    MeasurementField(
      key: 'waist',
      label: 'Waist',
      hint: 'Around natural waistline',
      unit: 'cm',
      icon: Icons.straighten,
    ),
    MeasurementField(
      key: 'hips',
      label: 'Hips',
      hint: 'Around fullest part',
      unit: 'cm',
      icon: Icons.straighten,
    ),
    MeasurementField(
      key: 'shoulder',
      label: 'Shoulder Width',
      hint: 'From shoulder to shoulder',
      unit: 'cm',
      icon: Icons.straighten,
    ),
    MeasurementField(
      key: 'sleeve',
      label: 'Sleeve Length',
      hint: 'From shoulder to wrist',
      unit: 'cm',
      icon: Icons.straighten,
    ),
    MeasurementField(
      key: 'length',
      label: 'Length',
      hint: 'Total garment length',
      unit: 'cm',
      icon: Icons.straighten,
    ),
  ];

  @override
  void initState() {
    super.initState();
    for (final field in _measurementFields) {
      _controllers[field.key] = TextEditingController();
    }
  }

  @override
  void dispose() {
    for (final controller in _controllers.values) {
      controller.dispose();
    }
    super.dispose();
  }

  void _updateMeasurement(String key, String value) {
    final doubleValue = double.tryParse(value);
    if (doubleValue != null) {
      _measurements[key] = doubleValue;
    } else {
      _measurements.remove(key);
    }
    
    context.read<OrderCreationBloc>().add(
      UpdateMeasurements(measurements: Map.from(_measurements)),
    );
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Add Measurements',
            style: Theme.of(context).textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Provide your measurements for the perfect fit',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Measurement Guide Card
          _buildMeasurementGuide(context),

          const SizedBox(height: AppConfig.largePadding),

          // Measurement Fields
          ...(_measurementFields.asMap().entries.map((entry) {
            final index = entry.key;
            final field = entry.value;
            return _buildMeasurementField(context, field, index);
          })),

          const SizedBox(height: AppConfig.largePadding),

          // Professional Measurement Option
          _buildProfessionalMeasurementCard(context),

          const SizedBox(height: AppConfig.largePadding),

          // Size Chart Reference
          _buildSizeChartCard(context),
        ],
      ),
    );
  }

  Widget _buildMeasurementGuide(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Measurement guide',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              AppTheme.primaryColor.withValues(alpha: 0.1),
              AppTheme.primaryColor.withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.primaryColor.withValues(alpha: 0.2)),
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                gradient: AppTheme.instagramGradient,
                borderRadius: BorderRadius.circular(25),
              ),
              child: const Icon(
                Icons.info_outline,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Measurement Tips',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Use a flexible measuring tape and measure over light clothing',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            AccessibilityHelper.accessibleButton(
              child: Icon(
                Icons.help_outline,
                color: AppTheme.primaryColor,
                size: 20,
              ),
              onPressed: () => _showMeasurementGuide(context),
              semanticLabel: 'Show measurement guide',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurementField(
    BuildContext context,
    MeasurementField field,
    int index,
  ) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleTextField(
        controller: _controllers[field.key]!,
        label: field.label,
        hint: '${field.hint} (${field.unit})',
        semanticLabel: '${field.label} measurement in ${field.unit}',
        keyboardType: const TextInputType.numberWithOptions(decimal: true),
        prefixIcon: Icon(
          field.icon,
          color: AppTheme.secondaryTextColor,
        ),
        suffixIcon: Container(
          padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          child: Text(
            field.unit,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildProfessionalMeasurementCard(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Book professional measurement',
      onTap: () {
        // TODO: Navigate to professional measurement booking
      },
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Row(
          children: [
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                color: AppTheme.successColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(25),
              ),
              child: Icon(
                Icons.person_pin_circle,
                color: AppTheme.successColor,
                size: 24,
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Professional Measurement',
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    'Book an appointment for accurate measurements',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Icon(
              Icons.arrow_forward_ios,
              color: AppTheme.secondaryTextColor,
              size: 16,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSizeChartCard(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'View size chart',
      onTap: () => _showSizeChart(context),
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          border: Border.all(color: AppTheme.primaryColor),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.table_chart,
              color: AppTheme.primaryColor,
              size: 20,
            ),
            const SizedBox(width: 8),
            Text(
              'View Size Chart',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showMeasurementGuide(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'How to Measure',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            // Measurement guide content would go here
            const Expanded(
              child: Center(
                child: Text('Measurement guide content'),
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showSizeChart(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Size Chart'),
        content: const Text('Size chart content would go here'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}

class MeasurementField {
  final String key;
  final String label;
  final String hint;
  final String unit;
  final IconData icon;

  const MeasurementField({
    required this.key,
    required this.label,
    required this.hint,
    required this.unit,
    required this.icon,
  });
}
