import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/order_model.dart';

/// Order information details card
class OrderInfoCard extends StatelessWidget {
  final OrderModel order;

  const OrderInfoCard({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Service Details
          _buildServiceDetails(context),

          const SizedBox(height: AppConfig.largePadding),

          // Measurements
          _buildMeasurements(context),

          const SizedBox(height: AppConfig.largePadding),

          // Fabric Details
          if (order.fabric != null) _buildFabricDetails(context),

          const SizedBox(height: AppConfig.largePadding),

          // Delivery Information
          _buildDeliveryInfo(context),

          const SizedBox(height: AppConfig.largePadding),

          // Special Instructions
          if (order.specialInstructions != null) _buildSpecialInstructions(context),

          const SizedBox(height: AppConfig.largePadding),

          // Price Breakdown
          _buildPriceBreakdown(context),

          const SizedBox(height: AppConfig.largePadding),

          // Order Actions
          _buildOrderActions(context),
        ],
      ),
    );
  }

  Widget _buildServiceDetails(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Service Details',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  width: 50,
                  height: 50,
                  decoration: BoxDecoration(
                    gradient: AppTheme.instagramGradient,
                    borderRadius: BorderRadius.circular(25),
                  ),
                  child: const Icon(
                    Icons.design_services,
                    color: Colors.white,
                    size: 24,
                  ),
                ),
                const SizedBox(width: AppConfig.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.service.name,
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        order.service.category,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
                Text(
                  '₮${order.service.basePrice.toStringAsFixed(0)}',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            Text(
              order.service.description,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.primaryTextColor,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMeasurements(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Measurements',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Column(
          children: order.measurements.entries.map((entry) {
            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _formatMeasurementName(entry.key),
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      '${entry.value} cm',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildFabricDetails(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Fabric',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Row(
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AccessibilityHelper.accessibleImage(
                image: CachedNetworkImageProvider(
                  order.fabric!.imageUrl ?? 'https://via.placeholder.com/60x60',
                ),
                semanticLabel: '${order.fabric!.name} fabric',
                width: 60,
                height: 60,
                fit: BoxFit.cover,
              ),
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    order.fabric!.name,
                    style: Theme.of(context).textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Text(
                    order.fabric!.description,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.secondaryTextColor,
                    ),
                  ),
                ],
              ),
            ),
            Text(
              order.fabric!.additionalCost > 0
                  ? '+₮${order.fabric!.additionalCost.toStringAsFixed(0)}'
                  : 'Included',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: order.fabric!.additionalCost > 0
                    ? AppTheme.primaryColor
                    : AppTheme.successColor,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDeliveryInfo(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Delivery Information',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Column(
          children: [
            _buildInfoRow(
              context,
              'Method',
              order.deliveryOption.name,
            ),
            _buildInfoRow(
              context,
              'Estimated Days',
              '${order.deliveryOption.estimatedDays} days',
            ),
            _buildInfoRow(
              context,
              'Delivery Cost',
              order.deliveryOption.cost > 0
                  ? '₮${order.deliveryOption.cost.toStringAsFixed(0)}'
                  : 'Free',
            ),
            _buildInfoRow(
              context,
              'Estimated Delivery',
              _formatDate(order.estimatedDelivery),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildSpecialInstructions(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Special Instructions',
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Text(
          order.specialInstructions!,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: AppTheme.primaryTextColor,
            height: 1.5,
          ),
        ),
      ),
    );
  }

  Widget _buildPriceBreakdown(BuildContext context) {
    return _buildInfoSection(
      context,
      title: 'Price Breakdown',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Column(
          children: [
            _buildPriceRow(
              context,
              'Service',
              '₮${order.service.basePrice.toStringAsFixed(0)}',
            ),
            if (order.fabric != null && order.fabric!.additionalCost > 0)
              _buildPriceRow(
                context,
                'Fabric',
                '₮${order.fabric!.additionalCost.toStringAsFixed(0)}',
              ),
            if (order.deliveryOption.cost > 0)
              _buildPriceRow(
                context,
                'Delivery',
                '₮${order.deliveryOption.cost.toStringAsFixed(0)}',
              ),
            const Divider(),
            _buildPriceRow(
              context,
              'Total',
              '₮${order.totalPrice.toStringAsFixed(0)}',
              isTotal: true,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildOrderActions(BuildContext context) {
    return Column(
      children: [
        if (order.status == OrderStatus.readyForFitting)
          SizedBox(
            width: double.infinity,
            child: AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  gradient: AppTheme.instagramGradient,
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: Text(
                  'Schedule Fitting',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              onPressed: () {
                // TODO: Navigate to fitting scheduler
              },
              semanticLabel: 'Schedule fitting appointment',
            ),
          ),
        
        const SizedBox(height: 12),
        
        Row(
          children: [
            Expanded(
              child: AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.primaryColor),
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Text(
                    'Download Invoice',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                onPressed: () {
                  // TODO: Download invoice
                },
                semanticLabel: 'Download order invoice',
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  decoration: BoxDecoration(
                    border: Border.all(color: AppTheme.primaryColor),
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Text(
                    'Contact Support',
                    style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
                onPressed: () {
                  // TODO: Contact support
                },
                semanticLabel: 'Contact customer support',
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildInfoSection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        child,
      ],
    );
  }

  Widget _buildInfoRow(BuildContext context, String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          Text(
            value,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPriceRow(
    BuildContext context,
    String label,
    String price, {
    bool isTotal = false,
  }) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            label,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: isTotal ? FontWeight.bold : FontWeight.normal,
              color: AppTheme.primaryTextColor,
            ),
          ),
          Text(
            price,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: isTotal ? AppTheme.primaryColor : AppTheme.primaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  String _formatMeasurementName(String key) {
    return key.split('_').map((word) => 
      word[0].toUpperCase() + word.substring(1)
    ).join(' ');
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
