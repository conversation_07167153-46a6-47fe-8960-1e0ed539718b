import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/order_model.dart';
import '../bloc/order_details_bloc.dart';

/// Order communication section with messaging
class OrderCommunicationSection extends StatefulWidget {
  final OrderModel order;

  const OrderCommunicationSection({
    super.key,
    required this.order,
  });

  @override
  State<OrderCommunicationSection> createState() => _OrderCommunicationSectionState();
}

class _OrderCommunicationSectionState extends State<OrderCommunicationSection> {
  final TextEditingController _messageController = TextEditingController();
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _messageController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _sendMessage() {
    if (_messageController.text.trim().isNotEmpty) {
      context.read<OrderDetailsBloc>().add(
        SendMessage(
          orderId: widget.order.id,
          message: _messageController.text.trim(),
        ),
      );
      _messageController.clear();
      
      // Scroll to bottom after sending message
      WidgetsBinding.instance.addPostFrameCallback((_) {
        if (_scrollController.hasClients) {
          _scrollController.animateTo(
            _scrollController.position.maxScrollExtent,
            duration: const Duration(milliseconds: 300),
            curve: Curves.easeOut,
          );
        }
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocBuilder<OrderDetailsBloc, OrderDetailsState>(
      builder: (context, state) {
        if (state is OrderDetailsLoaded) {
          return Column(
            children: [
              // Messages List
              Expanded(
                child: _buildMessagesList(context, state.messages),
              ),
              
              // Message Input
              _buildMessageInput(context),
            ],
          );
        }
        
        return const Center(
          child: Text('No messages available'),
        );
      },
    );
  }

  Widget _buildMessagesList(BuildContext context, List<ChatMessage> messages) {
    if (messages.isEmpty) {
      return _buildEmptyMessages(context);
    }

    return ListView.builder(
      controller: _scrollController,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      itemCount: messages.length,
      itemBuilder: (context, index) {
        final message = messages[index];
        return _buildMessageBubble(context, message, index);
      },
    );
  }

  Widget _buildEmptyMessages(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.chat_bubble_outline,
            size: 64,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No messages yet',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Start a conversation with your tailor',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildMessageBubble(BuildContext context, ChatMessage message, int index) {
    final isFromCustomer = message.isFromCustomer;
    
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: Row(
        mainAxisAlignment: isFromCustomer 
            ? MainAxisAlignment.end 
            : MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Avatar (for tailor messages)
          if (!isFromCustomer) ...[
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor,
              child: Text(
                message.senderName[0].toUpperCase(),
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(width: 8),
          ],
          
          // Message Content
          Flexible(
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              child: Column(
                crossAxisAlignment: isFromCustomer 
                    ? CrossAxisAlignment.end 
                    : CrossAxisAlignment.start,
                children: [
                  // Message Bubble
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                    decoration: BoxDecoration(
                      gradient: isFromCustomer 
                          ? AppTheme.instagramGradient 
                          : null,
                      color: isFromCustomer 
                          ? null 
                          : AppTheme.backgroundColor,
                      borderRadius: BorderRadius.only(
                        topLeft: const Radius.circular(16),
                        topRight: const Radius.circular(16),
                        bottomLeft: Radius.circular(isFromCustomer ? 16 : 4),
                        bottomRight: Radius.circular(isFromCustomer ? 4 : 16),
                      ),
                      border: isFromCustomer 
                          ? null 
                          : Border.all(color: AppTheme.dividerColor),
                    ),
                    child: Text(
                      message.message,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isFromCustomer 
                            ? Colors.white 
                            : AppTheme.primaryTextColor,
                        height: 1.4,
                      ),
                    ),
                  ),
                  
                  // Attachments
                  if (message.attachments.isNotEmpty) ...[
                    const SizedBox(height: 8),
                    _buildAttachments(context, message.attachments),
                  ],
                  
                  // Timestamp
                  const SizedBox(height: 4),
                  Text(
                    _formatMessageTime(message.timestamp),
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: AppTheme.secondaryTextColor,
                      fontSize: 11,
                    ),
                  ),
                ],
              ),
            ),
          ),
          
          // Avatar (for customer messages)
          if (isFromCustomer) ...[
            const SizedBox(width: 8),
            CircleAvatar(
              radius: 16,
              backgroundColor: AppTheme.primaryColor,
              child: const Icon(
                Icons.person,
                color: Colors.white,
                size: 16,
              ),
            ),
          ],
        ],
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 400.ms)
        .slideY(begin: 0.3, end: 0, duration: 400.ms);
  }

  Widget _buildAttachments(BuildContext context, List<String> attachments) {
    return SizedBox(
      height: 80,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: attachments.length,
        itemBuilder: (context, index) {
          return Container(
            margin: const EdgeInsets.only(right: 8),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: AccessibilityHelper.accessibleImage(
                image: CachedNetworkImageProvider(attachments[index]),
                semanticLabel: 'Attachment ${index + 1}',
                width: 80,
                height: 80,
                fit: BoxFit.cover,
              ),
            ),
          );
        },
      ),
    );
  }

  Widget _buildMessageInput(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        border: Border(
          top: BorderSide(color: AppTheme.dividerColor),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            // Attachment Button
            AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Icon(
                  Icons.attach_file,
                  color: AppTheme.primaryColor,
                  size: 20,
                ),
              ),
              onPressed: () {
                // TODO: Implement attachment functionality
              },
              semanticLabel: 'Attach file',
            ),
            
            const SizedBox(width: 12),
            
            // Message Input Field
            Expanded(
              child: Container(
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppTheme.dividerColor),
                ),
                child: TextField(
                  controller: _messageController,
                  decoration: const InputDecoration(
                    hintText: 'Type a message...',
                    border: InputBorder.none,
                    contentPadding: EdgeInsets.symmetric(
                      horizontal: 16,
                      vertical: 12,
                    ),
                  ),
                  maxLines: null,
                  textCapitalization: TextCapitalization.sentences,
                  onSubmitted: (_) => _sendMessage(),
                ),
              ),
            ),
            
            const SizedBox(width: 12),
            
            // Send Button
            AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  gradient: AppTheme.instagramGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.send,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: _sendMessage,
              semanticLabel: 'Send message',
            ),
          ],
        ),
      ),
    );
  }

  String _formatMessageTime(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);
    
    if (difference.inDays > 0) {
      return '${timestamp.day}/${timestamp.month}';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}
