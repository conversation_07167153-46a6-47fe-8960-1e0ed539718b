import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/order_model.dart';

/// Instagram-inspired order status timeline
class OrderStatusTimeline extends StatelessWidget {
  final OrderModel order;

  const OrderStatusTimeline({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Current Status Card
          _buildCurrentStatusCard(context),

          const SizedBox(height: AppConfig.largePadding),

          // Timeline Header
          Text(
            'Order Timeline',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),

          // Timeline Items
          ...order.updates.asMap().entries.map((entry) {
            final index = entry.key;
            final update = entry.value;
            final isLast = index == order.updates.length - 1;
            return _buildTimelineItem(context, update, isLast, index);
          }),

          // Next Steps (if order is active)
          if (order.isActive) ...[
            const SizedBox(height: AppConfig.largePadding),
            _buildNextSteps(context),
          ],
        ],
      ),
    );
  }

  Widget _buildCurrentStatusCard(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Current order status: ${order.statusDisplayText}',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.largePadding),
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
            colors: [
              _getStatusColor(order.status).withValues(alpha: 0.1),
              _getStatusColor(order.status).withValues(alpha: 0.05),
            ],
          ),
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(
            color: _getStatusColor(order.status).withValues(alpha: 0.3),
          ),
        ),
        child: Column(
          children: [
            // Status Icon and Text
            Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: _getStatusColor(order.status),
                    borderRadius: BorderRadius.circular(30),
                  ),
                  child: Icon(
                    _getStatusIcon(order.status),
                    color: Colors.white,
                    size: 30,
                  ),
                ),
                const SizedBox(width: AppConfig.defaultPadding),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        order.statusDisplayText,
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        _getStatusDescription(order.status),
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Progress Bar
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      'Progress',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryTextColor,
                      ),
                    ),
                    Text(
                      '${(order.progressPercentage * 100).toInt()}%',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        fontWeight: FontWeight.w600,
                        color: _getStatusColor(order.status),
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 8),
                LinearProgressIndicator(
                  value: order.progressPercentage,
                  backgroundColor: AppTheme.dividerColor,
                  valueColor: AlwaysStoppedAnimation<Color>(_getStatusColor(order.status)),
                ),
              ],
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Estimated Delivery
            Row(
              children: [
                Icon(
                  Icons.schedule,
                  color: AppTheme.secondaryTextColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Estimated delivery: ${_formatDate(order.estimatedDelivery)}',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    )
        .animate()
        .fadeIn(duration: 600.ms)
        .slideY(begin: 0.3, end: 0, duration: 600.ms);
  }

  Widget _buildTimelineItem(
    BuildContext context,
    OrderUpdate update,
    bool isLast,
    int index,
  ) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Timeline update: ${update.title}',
      child: Container(
        margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
        child: IntrinsicHeight(
          child: Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Timeline Line
              Column(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: AppTheme.primaryColor,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  if (!isLast)
                    Expanded(
                      child: Container(
                        width: 2,
                        color: AppTheme.dividerColor,
                        margin: const EdgeInsets.symmetric(vertical: 4),
                      ),
                    ),
                ],
              ),

              const SizedBox(width: AppConfig.defaultPadding),

              // Update Content
              Expanded(
                child: Container(
                  padding: const EdgeInsets.all(AppConfig.defaultPadding),
                  decoration: BoxDecoration(
                    color: Colors.white,
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                    border: Border.all(color: AppTheme.dividerColor),
                  ),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Update Header
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Expanded(
                            child: Text(
                              update.title,
                              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                                fontWeight: FontWeight.bold,
                                color: AppTheme.primaryTextColor,
                              ),
                            ),
                          ),
                          Text(
                            _formatDateTime(update.timestamp),
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: AppTheme.secondaryTextColor,
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8),

                      // Update Description
                      Text(
                        update.description,
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.primaryTextColor,
                          height: 1.5,
                        ),
                      ),

                      // Update Image (if available)
                      if (update.imageUrl != null) ...[
                        const SizedBox(height: AppConfig.defaultPadding),
                        ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: AccessibilityHelper.accessibleImage(
                            image: NetworkImage(update.imageUrl!),
                            semanticLabel: 'Update image for ${update.title}',
                            width: double.infinity,
                            height: 150,
                            fit: BoxFit.cover,
                          ),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildNextSteps(BuildContext context) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Next steps for your order',
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Icon(
                  Icons.info_outline,
                  color: AppTheme.primaryColor,
                  size: 24,
                ),
                const SizedBox(width: 8),
                Text(
                  'What\'s Next?',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ],
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            Text(
              _getNextStepsText(order.status),
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.primaryTextColor,
                height: 1.5,
              ),
            ),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return const Color(0xFFF57C00);
      case OrderStatus.confirmed:
        return const Color(0xFF1976D2);
      case OrderStatus.inProgress:
        return const Color(0xFF7B1FA2);
      case OrderStatus.readyForFitting:
        return const Color(0xFF00ACC1);
      case OrderStatus.completed:
        return const Color(0xFF388E3C);
      case OrderStatus.cancelled:
        return const Color(0xFFD32F2F);
      case OrderStatus.refunded:
        return const Color(0xFF455A64);
    }
  }

  IconData _getStatusIcon(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return Icons.hourglass_empty;
      case OrderStatus.confirmed:
        return Icons.check_circle;
      case OrderStatus.inProgress:
        return Icons.construction;
      case OrderStatus.readyForFitting:
        return Icons.checkroom;
      case OrderStatus.completed:
        return Icons.done_all;
      case OrderStatus.cancelled:
        return Icons.cancel;
      case OrderStatus.refunded:
        return Icons.money_off;
    }
  }

  String _getStatusDescription(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Waiting for tailor confirmation';
      case OrderStatus.confirmed:
        return 'Order confirmed, work will begin soon';
      case OrderStatus.inProgress:
        return 'Your garment is being crafted';
      case OrderStatus.readyForFitting:
        return 'Ready for fitting appointment';
      case OrderStatus.completed:
        return 'Order completed successfully';
      case OrderStatus.cancelled:
        return 'Order has been cancelled';
      case OrderStatus.refunded:
        return 'Order refunded';
    }
  }

  String _getNextStepsText(OrderStatus status) {
    switch (status) {
      case OrderStatus.pending:
        return 'Your tailor will review and confirm your order within 24 hours. You\'ll receive a notification once confirmed.';
      case OrderStatus.confirmed:
        return 'Work on your garment will begin soon. You can track progress updates here and communicate with your tailor.';
      case OrderStatus.inProgress:
        return 'Your tailor is working on your garment. You\'ll receive updates as work progresses. Feel free to message if you have questions.';
      case OrderStatus.readyForFitting:
        return 'Your garment is ready for fitting! Please schedule an appointment with your tailor to ensure the perfect fit.';
      case OrderStatus.completed:
        return 'Congratulations! Your order is complete. Don\'t forget to leave a review for your tailor.';
      case OrderStatus.cancelled:
        return 'This order has been cancelled. If you have questions, please contact support.';
      case OrderStatus.refunded:
        return 'This order has been refunded. The refund should appear in your account within 3-5 business days.';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }
}
