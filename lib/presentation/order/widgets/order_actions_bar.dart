import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/order_model.dart';
import '../bloc/order_details_bloc.dart';

/// Order actions bar with context-sensitive actions
class OrderActionsBar extends StatelessWidget {
  final OrderModel order;

  const OrderActionsBar({
    super.key,
    required this.order,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary Action
            _buildPrimaryAction(context),
            
            const SizedBox(height: 12),
            
            // Secondary Actions
            _buildSecondaryActions(context),
          ],
        ),
      ),
    );
  }

  Widget _buildPrimaryAction(BuildContext context) {
    switch (order.status) {
      case OrderStatus.pending:
        return _buildActionButton(
          context,
          label: 'Contact Tailor',
          icon: Icons.chat,
          onPressed: () => _navigateToChat(context),
          isPrimary: true,
        );
      
      case OrderStatus.confirmed:
      case OrderStatus.inProgress:
        return _buildActionButton(
          context,
          label: 'Message Tailor',
          icon: Icons.chat_bubble,
          onPressed: () => _navigateToChat(context),
          isPrimary: true,
        );
      
      case OrderStatus.readyForFitting:
        return _buildActionButton(
          context,
          label: 'Schedule Fitting',
          icon: Icons.calendar_today,
          onPressed: () => _scheduleFitting(context),
          isPrimary: true,
        );
      
      case OrderStatus.completed:
        return _buildActionButton(
          context,
          label: 'Leave Review',
          icon: Icons.star,
          onPressed: () => _leaveReview(context),
          isPrimary: true,
        );
      
      case OrderStatus.cancelled:
      case OrderStatus.refunded:
        return _buildActionButton(
          context,
          label: 'Reorder',
          icon: Icons.refresh,
          onPressed: () => _reorder(context),
          isPrimary: true,
        );
    }
  }

  Widget _buildSecondaryActions(BuildContext context) {
    return Row(
      children: [
        // Call Action
        Expanded(
          child: _buildActionButton(
            context,
            label: 'Call',
            icon: Icons.phone,
            onPressed: () => _makeCall(context),
            isPrimary: false,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // Share Action
        Expanded(
          child: _buildActionButton(
            context,
            label: 'Share',
            icon: Icons.share,
            onPressed: () => _shareOrder(context),
            isPrimary: false,
          ),
        ),
        
        const SizedBox(width: 12),
        
        // More Actions
        _buildMoreActionsButton(context),
      ],
    );
  }

  Widget _buildActionButton(
    BuildContext context, {
    required String label,
    required IconData icon,
    required VoidCallback onPressed,
    required bool isPrimary,
  }) {
    return AccessibilityHelper.accessibleButton(
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          gradient: isPrimary ? AppTheme.instagramGradient : null,
          color: isPrimary ? null : AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: isPrimary ? null : Border.all(color: AppTheme.dividerColor),
        ),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              icon,
              color: isPrimary ? Colors.white : AppTheme.primaryColor,
              size: 18,
            ),
            const SizedBox(width: 8),
            Text(
              label,
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                color: isPrimary ? Colors.white : AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
      onPressed: onPressed,
      semanticLabel: label,
    );
  }

  Widget _buildMoreActionsButton(BuildContext context) {
    return AccessibilityHelper.accessibleButton(
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.backgroundColor,
          borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          border: Border.all(color: AppTheme.dividerColor),
        ),
        child: Icon(
          Icons.more_horiz,
          color: AppTheme.primaryColor,
          size: 20,
        ),
      ),
      onPressed: () => _showMoreActions(context),
      semanticLabel: 'More actions',
    );
  }

  void _navigateToChat(BuildContext context) {
    Navigator.of(context).pushNamed(
      '/chat',
      arguments: {
        'orderId': order.id,
        'tailorId': order.tailorId,
      },
    );
  }

  void _scheduleFitting(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => _FittingScheduler(order: order),
    );
  }

  void _leaveReview(BuildContext context) {
    Navigator.of(context).pushNamed(
      '/review',
      arguments: {
        'orderId': order.id,
        'tailorId': order.tailorId,
      },
    );
  }

  void _reorder(BuildContext context) {
    Navigator.of(context).pushNamed(
      '/order-creation',
      arguments: {
        'tailorId': order.tailorId,
        'preSelectedService': order.service.name,
      },
    );
  }

  void _makeCall(BuildContext context) {
    // TODO: Implement phone call functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Calling tailor...')),
    );
  }

  void _shareOrder(BuildContext context) {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Sharing order...')),
    );
  }

  void _showMoreActions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'More Actions',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            
            if (order.canBeCancelled)
              ListTile(
                leading: const Icon(Icons.cancel, color: AppTheme.errorColor),
                title: const Text('Cancel Order'),
                onTap: () {
                  Navigator.pop(context);
                  _showCancelDialog(context);
                },
              ),
            
            ListTile(
              leading: const Icon(Icons.receipt),
              title: const Text('Download Invoice'),
              onTap: () {
                Navigator.pop(context);
                _downloadInvoice(context);
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.support_agent),
              title: const Text('Contact Support'),
              onTap: () {
                Navigator.pop(context);
                _contactSupport(context);
              },
            ),
            
            ListTile(
              leading: const Icon(Icons.report),
              title: const Text('Report Issue'),
              onTap: () {
                Navigator.pop(context);
                _reportIssue(context);
              },
            ),
          ],
        ),
      ),
    );
  }

  void _showCancelDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text(
          'Are you sure you want to cancel this order? This action cannot be undone.',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Keep Order'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              context.read<OrderDetailsBloc>().add(CancelOrder(orderId: order.id));
            },
            child: const Text(
              'Cancel Order',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  void _downloadInvoice(BuildContext context) {
    // TODO: Implement invoice download
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Downloading invoice...')),
    );
  }

  void _contactSupport(BuildContext context) {
    // TODO: Implement support contact
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Contacting support...')),
    );
  }

  void _reportIssue(BuildContext context) {
    // TODO: Implement issue reporting
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Reporting issue...')),
    );
  }
}

class _FittingScheduler extends StatefulWidget {
  final OrderModel order;

  const _FittingScheduler({required this.order});

  @override
  State<_FittingScheduler> createState() => _FittingSchedulerState();
}

class _FittingSchedulerState extends State<_FittingScheduler> {
  DateTime? _selectedDate;
  TimeOfDay? _selectedTime;
  final TextEditingController _notesController = TextEditingController();

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.7,
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Schedule Fitting',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Date Selection
          ListTile(
            leading: const Icon(Icons.calendar_today),
            title: Text(_selectedDate != null 
                ? '${_selectedDate!.day}/${_selectedDate!.month}/${_selectedDate!.year}'
                : 'Select Date'),
            onTap: _selectDate,
          ),
          
          // Time Selection
          ListTile(
            leading: const Icon(Icons.access_time),
            title: Text(_selectedTime != null 
                ? _selectedTime!.format(context)
                : 'Select Time'),
            onTap: _selectTime,
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Notes
          TextField(
            controller: _notesController,
            decoration: const InputDecoration(
              labelText: 'Additional Notes',
              hintText: 'Any special requirements...',
              border: OutlineInputBorder(),
            ),
            maxLines: 3,
          ),
          
          const Spacer(),
          
          // Schedule Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _selectedDate != null && _selectedTime != null
                  ? _scheduleFitting
                  : null,
              child: const Text('Schedule Fitting'),
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _selectDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 30)),
    );
    
    if (date != null) {
      setState(() {
        _selectedDate = date;
      });
    }
  }

  Future<void> _selectTime() async {
    final time = await showTimePicker(
      context: context,
      initialTime: const TimeOfDay(hour: 9, minute: 0),
    );
    
    if (time != null) {
      setState(() {
        _selectedTime = time;
      });
    }
  }

  void _scheduleFitting() {
    if (_selectedDate != null && _selectedTime != null) {
      final fittingDateTime = DateTime(
        _selectedDate!.year,
        _selectedDate!.month,
        _selectedDate!.day,
        _selectedTime!.hour,
        _selectedTime!.minute,
      );
      
      context.read<OrderDetailsBloc>().add(
        RequestFitting(
          orderId: widget.order.id,
          preferredDate: fittingDateTime,
          notes: _notesController.text.isNotEmpty ? _notesController.text : null,
        ),
      );
      
      Navigator.pop(context);
    }
  }
}
