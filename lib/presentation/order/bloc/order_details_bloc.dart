import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/order_model.dart';
import '../../../data/services/api_service_manager.dart';

// Events
abstract class OrderDetailsEvent extends Equatable {
  const OrderDetailsEvent();

  @override
  List<Object?> get props => [];
}

class LoadOrderDetails extends OrderDetailsEvent {
  final String orderId;

  const LoadOrderDetails({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class RefreshOrderDetails extends OrderDetailsEvent {
  final String orderId;

  const RefreshOrderDetails({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class CancelOrder extends OrderDetailsEvent {
  final String orderId;

  const CancelOrder({required this.orderId});

  @override
  List<Object?> get props => [orderId];
}

class AddOrderUpdate extends OrderDetailsEvent {
  final String orderId;
  final OrderUpdate update;

  const AddOrderUpdate({
    required this.orderId,
    required this.update,
  });

  @override
  List<Object?> get props => [orderId, update];
}

class SendMessage extends OrderDetailsEvent {
  final String orderId;
  final String message;
  final List<String>? attachments;

  const SendMessage({
    required this.orderId,
    required this.message,
    this.attachments,
  });

  @override
  List<Object?> get props => [orderId, message, attachments];
}

class RequestFitting extends OrderDetailsEvent {
  final String orderId;
  final DateTime preferredDate;
  final String? notes;

  const RequestFitting({
    required this.orderId,
    required this.preferredDate,
    this.notes,
  });

  @override
  List<Object?> get props => [orderId, preferredDate, notes];
}

// States
abstract class OrderDetailsState extends Equatable {
  const OrderDetailsState();

  @override
  List<Object?> get props => [];
}

class OrderDetailsInitial extends OrderDetailsState {
  const OrderDetailsInitial();
}

class OrderDetailsLoading extends OrderDetailsState {
  const OrderDetailsLoading();
}

class OrderDetailsLoaded extends OrderDetailsState {
  final OrderModel order;
  final List<ChatMessage> messages;
  final bool isRefreshing;

  const OrderDetailsLoaded({
    required this.order,
    this.messages = const [],
    this.isRefreshing = false,
  });

  @override
  List<Object?> get props => [order, messages, isRefreshing];

  OrderDetailsLoaded copyWith({
    OrderModel? order,
    List<ChatMessage>? messages,
    bool? isRefreshing,
  }) {
    return OrderDetailsLoaded(
      order: order ?? this.order,
      messages: messages ?? this.messages,
      isRefreshing: isRefreshing ?? this.isRefreshing,
    );
  }
}

class OrderDetailsError extends OrderDetailsState {
  final String message;

  const OrderDetailsError({required this.message});

  @override
  List<Object?> get props => [message];
}

class OrderActionSuccess extends OrderDetailsState {
  final String message;
  final OrderModel order;

  const OrderActionSuccess({
    required this.message,
    required this.order,
  });

  @override
  List<Object?> get props => [message, order];
}

// BLoC
class OrderDetailsBloc extends Bloc<OrderDetailsEvent, OrderDetailsState> {
  final ApiServiceManager _apiService;

  OrderDetailsBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const OrderDetailsInitial()) {
    on<LoadOrderDetails>(_onLoadOrderDetails);
    on<RefreshOrderDetails>(_onRefreshOrderDetails);
    on<CancelOrder>(_onCancelOrder);
    on<AddOrderUpdate>(_onAddOrderUpdate);
    on<SendMessage>(_onSendMessage);
    on<RequestFitting>(_onRequestFitting);
  }

  Future<void> _onLoadOrderDetails(
    LoadOrderDetails event,
    Emitter<OrderDetailsState> emit,
  ) async {
    emit(const OrderDetailsLoading());

    try {
      // Simulate API call to get order details
      final order = await _getMockOrder(event.orderId);
      final messages = await _getMockMessages(event.orderId);

      emit(OrderDetailsLoaded(
        order: order,
        messages: messages,
      ));
    } catch (e) {
      emit(OrderDetailsError(
        message: 'Failed to load order details: ${e.toString()}',
      ));
    }
  }

  Future<void> _onRefreshOrderDetails(
    RefreshOrderDetails event,
    Emitter<OrderDetailsState> emit,
  ) async {
    if (state is OrderDetailsLoaded) {
      final currentState = state as OrderDetailsLoaded;
      emit(currentState.copyWith(isRefreshing: true));

      try {
        final order = await _getMockOrder(event.orderId);
        final messages = await _getMockMessages(event.orderId);

        emit(OrderDetailsLoaded(
          order: order,
          messages: messages,
          isRefreshing: false,
        ));
      } catch (e) {
        emit(currentState.copyWith(isRefreshing: false));
      }
    }
  }

  Future<void> _onCancelOrder(
    CancelOrder event,
    Emitter<OrderDetailsState> emit,
  ) async {
    if (state is OrderDetailsLoaded) {
      final currentState = state as OrderDetailsLoaded;

      try {
        // Simulate API call to cancel order
        await Future.delayed(const Duration(seconds: 1));

        final cancelledOrder = currentState.order.copyWith(
          status: OrderStatus.cancelled,
          updatedAt: DateTime.now(),
        );

        emit(OrderActionSuccess(
          message: 'Order cancelled successfully',
          order: cancelledOrder,
        ));

        // Return to loaded state with updated order
        emit(currentState.copyWith(order: cancelledOrder));
      } catch (e) {
        emit(OrderDetailsError(
          message: 'Failed to cancel order: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onAddOrderUpdate(
    AddOrderUpdate event,
    Emitter<OrderDetailsState> emit,
  ) async {
    if (state is OrderDetailsLoaded) {
      final currentState = state as OrderDetailsLoaded;

      try {
        final updatedOrder = currentState.order.copyWith(
          updates: [...currentState.order.updates, event.update],
          updatedAt: DateTime.now(),
        );

        emit(currentState.copyWith(order: updatedOrder));
      } catch (e) {
        emit(OrderDetailsError(
          message: 'Failed to add update: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onSendMessage(
    SendMessage event,
    Emitter<OrderDetailsState> emit,
  ) async {
    if (state is OrderDetailsLoaded) {
      final currentState = state as OrderDetailsLoaded;

      try {
        final newMessage = ChatMessage(
          id: 'msg_${DateTime.now().millisecondsSinceEpoch}',
          senderId: 'current_user_id', // TODO: Get from auth
          senderName: 'You',
          message: event.message,
          timestamp: DateTime.now(),
          isFromCustomer: true,
          attachments: event.attachments ?? [],
        );

        final updatedMessages = [...currentState.messages, newMessage];

        emit(currentState.copyWith(messages: updatedMessages));
      } catch (e) {
        emit(OrderDetailsError(
          message: 'Failed to send message: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onRequestFitting(
    RequestFitting event,
    Emitter<OrderDetailsState> emit,
  ) async {
    if (state is OrderDetailsLoaded) {
      final currentState = state as OrderDetailsLoaded;

      try {
        // Simulate API call to request fitting
        await Future.delayed(const Duration(seconds: 1));

        final fittingUpdate = OrderUpdate(
          id: 'update_${DateTime.now().millisecondsSinceEpoch}',
          title: 'Fitting Requested',
          description: 'Customer requested fitting for ${event.preferredDate.day}/${event.preferredDate.month}/${event.preferredDate.year}',
          timestamp: DateTime.now(),
        );

        final updatedOrder = currentState.order.copyWith(
          updates: [...currentState.order.updates, fittingUpdate],
          updatedAt: DateTime.now(),
        );

        emit(OrderActionSuccess(
          message: 'Fitting request sent successfully',
          order: updatedOrder,
        ));

        emit(currentState.copyWith(order: updatedOrder));
      } catch (e) {
        emit(OrderDetailsError(
          message: 'Failed to request fitting: ${e.toString()}',
        ));
      }
    }
  }

  Future<OrderModel> _getMockOrder(String orderId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 500));

    // Return mock order data
    return OrderModel(
      id: orderId,
      tailorId: 'tailor_1',
      customerId: 'customer_1',
      service: const ServiceModel(
        id: 'service_1',
        name: 'Custom Business Suit',
        description: 'Premium tailored business suit with modern fit',
        basePrice: 250000,
        estimatedDays: 14,
        category: 'Formal Wear',
      ),
      measurements: {
        'chest': 102.0,
        'waist': 86.0,
        'hips': 98.0,
        'shoulder': 45.0,
        'sleeve': 65.0,
        'length': 75.0,
      },
      fabric: const FabricModel(
        id: 'fabric_1',
        name: 'Premium Wool',
        description: 'High-quality Italian wool blend',
        additionalCost: 50000,
        imageUrl: 'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=200',
      ),
      deliveryOption: const DeliveryOption(
        id: 'delivery_1',
        name: 'Standard Delivery',
        description: 'Regular delivery timeline',
        estimatedDays: 14,
        cost: 0,
      ),
      specialInstructions: 'Please ensure extra attention to sleeve length',
      totalPrice: 300000,
      status: OrderStatus.inProgress,
      createdAt: DateTime.now().subtract(const Duration(days: 5)),
      estimatedDelivery: DateTime.now().add(const Duration(days: 9)),
      updates: [
        OrderUpdate(
          id: 'update_1',
          title: 'Order Confirmed',
          description: 'Your order has been confirmed and work will begin soon',
          timestamp: DateTime.now().subtract(const Duration(days: 5)),
        ),
        OrderUpdate(
          id: 'update_2',
          title: 'Measurements Verified',
          description: 'All measurements have been verified and approved',
          timestamp: DateTime.now().subtract(const Duration(days: 4)),
        ),
        OrderUpdate(
          id: 'update_3',
          title: 'Cutting Started',
          description: 'Fabric cutting has begun based on your measurements',
          timestamp: DateTime.now().subtract(const Duration(days: 2)),
        ),
      ],
    );
  }

  Future<List<ChatMessage>> _getMockMessages(String orderId) async {
    // Simulate API delay
    await Future.delayed(const Duration(milliseconds: 300));

    return [
      ChatMessage(
        id: 'msg_1',
        senderId: 'tailor_1',
        senderName: 'Master Tailor',
        message: 'Hello! I\'ve received your order and will start working on it soon.',
        timestamp: DateTime.now().subtract(const Duration(days: 5)),
        isFromCustomer: false,
      ),
      ChatMessage(
        id: 'msg_2',
        senderId: 'customer_1',
        senderName: 'You',
        message: 'Thank you! Looking forward to seeing the progress.',
        timestamp: DateTime.now().subtract(const Duration(days: 4)),
        isFromCustomer: true,
      ),
      ChatMessage(
        id: 'msg_3',
        senderId: 'tailor_1',
        senderName: 'Master Tailor',
        message: 'I\'ve started cutting the fabric. Everything looks perfect!',
        timestamp: DateTime.now().subtract(const Duration(days: 2)),
        isFromCustomer: false,
        attachments: ['https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=300'],
      ),
    ];
  }
}

// Chat Message Model
class ChatMessage extends Equatable {
  final String id;
  final String senderId;
  final String senderName;
  final String message;
  final DateTime timestamp;
  final bool isFromCustomer;
  final List<String> attachments;

  const ChatMessage({
    required this.id,
    required this.senderId,
    required this.senderName,
    required this.message,
    required this.timestamp,
    required this.isFromCustomer,
    this.attachments = const [],
  });

  @override
  List<Object?> get props => [
        id,
        senderId,
        senderName,
        message,
        timestamp,
        isFromCustomer,
        attachments,
      ];
}
