import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/tailor_model.dart';
import '../../../data/models/order_model.dart';
import '../../../data/services/mock_api_service.dart';

// Service Model
class ServiceModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final double basePrice;
  final int estimatedDays;
  final String category;

  const ServiceModel({
    required this.id,
    required this.name,
    required this.description,
    required this.basePrice,
    required this.estimatedDays,
    required this.category,
  });

  @override
  List<Object?> get props => [id, name, description, basePrice, estimatedDays, category];
}

// Fabric Model
class FabricModel extends Equatable {
  final String id;
  final String name;
  final String description;
  final double additionalCost;
  final String? imageUrl;

  const FabricModel({
    required this.id,
    required this.name,
    required this.description,
    required this.additionalCost,
    this.imageUrl,
  });

  @override
  List<Object?> get props => [id, name, description, additionalCost, imageUrl];
}

// Delivery Option Model
class DeliveryOption extends Equatable {
  final String id;
  final String name;
  final String description;
  final int estimatedDays;
  final double cost;

  const DeliveryOption({
    required this.id,
    required this.name,
    required this.description,
    required this.estimatedDays,
    required this.cost,
  });

  @override
  List<Object?> get props => [id, name, description, estimatedDays, cost];
}

// Events
abstract class OrderCreationEvent extends Equatable {
  const OrderCreationEvent();

  @override
  List<Object?> get props => [];
}

class InitializeOrder extends OrderCreationEvent {
  final String? preSelectedService;

  const InitializeOrder({this.preSelectedService});

  @override
  List<Object?> get props => [preSelectedService];
}

class SelectService extends OrderCreationEvent {
  final ServiceModel service;

  const SelectService({required this.service});

  @override
  List<Object?> get props => [service];
}

class UpdateMeasurements extends OrderCreationEvent {
  final Map<String, double> measurements;

  const UpdateMeasurements({required this.measurements});

  @override
  List<Object?> get props => [measurements];
}

class SelectFabric extends OrderCreationEvent {
  final FabricModel fabric;

  const SelectFabric({required this.fabric});

  @override
  List<Object?> get props => [fabric];
}

class SelectDeliveryOption extends OrderCreationEvent {
  final DeliveryOption deliveryOption;

  const SelectDeliveryOption({required this.deliveryOption});

  @override
  List<Object?> get props => [deliveryOption];
}

class AddSpecialInstructions extends OrderCreationEvent {
  final String instructions;

  const AddSpecialInstructions({required this.instructions});

  @override
  List<Object?> get props => [instructions];
}

class PlaceOrder extends OrderCreationEvent {
  const PlaceOrder();
}

// States
abstract class OrderCreationState extends Equatable {
  const OrderCreationState();

  @override
  List<Object?> get props => [];
}

class OrderCreationInitial extends OrderCreationState {
  const OrderCreationInitial();
}

class OrderCreationLoading extends OrderCreationState {
  const OrderCreationLoading();
}

class OrderCreationLoaded extends OrderCreationState {
  final TailorModel tailor;
  final List<ServiceModel> availableServices;
  final ServiceModel? selectedService;
  final Map<String, double> measurements;
  final List<FabricModel> availableFabrics;
  final FabricModel? selectedFabric;
  final List<DeliveryOption> deliveryOptions;
  final DeliveryOption? deliveryOption;
  final String? specialInstructions;
  final double totalPrice;

  const OrderCreationLoaded({
    required this.tailor,
    required this.availableServices,
    this.selectedService,
    this.measurements = const {},
    required this.availableFabrics,
    this.selectedFabric,
    required this.deliveryOptions,
    this.deliveryOption,
    this.specialInstructions,
    this.totalPrice = 0.0,
  });

  @override
  List<Object?> get props => [
        tailor,
        availableServices,
        selectedService,
        measurements,
        availableFabrics,
        selectedFabric,
        deliveryOptions,
        deliveryOption,
        specialInstructions,
        totalPrice,
      ];

  OrderCreationLoaded copyWith({
    TailorModel? tailor,
    List<ServiceModel>? availableServices,
    ServiceModel? selectedService,
    Map<String, double>? measurements,
    List<FabricModel>? availableFabrics,
    FabricModel? selectedFabric,
    List<DeliveryOption>? deliveryOptions,
    DeliveryOption? deliveryOption,
    String? specialInstructions,
    double? totalPrice,
  }) {
    return OrderCreationLoaded(
      tailor: tailor ?? this.tailor,
      availableServices: availableServices ?? this.availableServices,
      selectedService: selectedService ?? this.selectedService,
      measurements: measurements ?? this.measurements,
      availableFabrics: availableFabrics ?? this.availableFabrics,
      selectedFabric: selectedFabric ?? this.selectedFabric,
      deliveryOptions: deliveryOptions ?? this.deliveryOptions,
      deliveryOption: deliveryOption ?? this.deliveryOption,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      totalPrice: totalPrice ?? this.totalPrice,
    );
  }
}

class OrderCreationSuccess extends OrderCreationState {
  final OrderModel order;

  const OrderCreationSuccess({required this.order});

  @override
  List<Object?> get props => [order];
}

class OrderCreationError extends OrderCreationState {
  final String message;

  const OrderCreationError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class OrderCreationBloc extends Bloc<OrderCreationEvent, OrderCreationState> {
  final MockApiService _apiService;
  final TailorModel _tailor;

  OrderCreationBloc({
    required MockApiService apiService,
    required TailorModel tailor,
  })  : _apiService = apiService,
        _tailor = tailor,
        super(const OrderCreationInitial()) {
    on<InitializeOrder>(_onInitializeOrder);
    on<SelectService>(_onSelectService);
    on<UpdateMeasurements>(_onUpdateMeasurements);
    on<SelectFabric>(_onSelectFabric);
    on<SelectDeliveryOption>(_onSelectDeliveryOption);
    on<AddSpecialInstructions>(_onAddSpecialInstructions);
    on<PlaceOrder>(_onPlaceOrder);
  }

  Future<void> _onInitializeOrder(
    InitializeOrder event,
    Emitter<OrderCreationState> emit,
  ) async {
    emit(const OrderCreationLoading());

    try {
      // Load available services, fabrics, and delivery options
      final services = _getMockServices();
      final fabrics = _getMockFabrics();
      final deliveryOptions = _getMockDeliveryOptions();

      ServiceModel? preSelectedService;
      if (event.preSelectedService != null) {
        preSelectedService = services.firstWhere(
          (service) => service.name == event.preSelectedService,
          orElse: () => services.first,
        );
      }

      emit(OrderCreationLoaded(
        tailor: _tailor,
        availableServices: services,
        selectedService: preSelectedService,
        availableFabrics: fabrics,
        deliveryOptions: deliveryOptions,
        totalPrice: preSelectedService?.basePrice ?? 0.0,
      ));
    } catch (e) {
      emit(OrderCreationError(message: 'Failed to initialize order: ${e.toString()}'));
    }
  }

  Future<void> _onSelectService(
    SelectService event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      final newTotalPrice = _calculateTotalPrice(
        service: event.service,
        fabric: currentState.selectedFabric,
        deliveryOption: currentState.deliveryOption,
      );

      emit(currentState.copyWith(
        selectedService: event.service,
        totalPrice: newTotalPrice,
      ));
    }
  }

  Future<void> _onUpdateMeasurements(
    UpdateMeasurements event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      emit(currentState.copyWith(measurements: event.measurements));
    }
  }

  Future<void> _onSelectFabric(
    SelectFabric event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      final newTotalPrice = _calculateTotalPrice(
        service: currentState.selectedService,
        fabric: event.fabric,
        deliveryOption: currentState.deliveryOption,
      );

      emit(currentState.copyWith(
        selectedFabric: event.fabric,
        totalPrice: newTotalPrice,
      ));
    }
  }

  Future<void> _onSelectDeliveryOption(
    SelectDeliveryOption event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      final newTotalPrice = _calculateTotalPrice(
        service: currentState.selectedService,
        fabric: currentState.selectedFabric,
        deliveryOption: event.deliveryOption,
      );

      emit(currentState.copyWith(
        deliveryOption: event.deliveryOption,
        totalPrice: newTotalPrice,
      ));
    }
  }

  Future<void> _onAddSpecialInstructions(
    AddSpecialInstructions event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      emit(currentState.copyWith(specialInstructions: event.instructions));
    }
  }

  Future<void> _onPlaceOrder(
    PlaceOrder event,
    Emitter<OrderCreationState> emit,
  ) async {
    if (state is OrderCreationLoaded) {
      final currentState = state as OrderCreationLoaded;
      emit(const OrderCreationLoading());

      try {
        // Create order object
        final order = OrderModel(
          id: 'order_${DateTime.now().millisecondsSinceEpoch}',
          tailorId: _tailor.id,
          customerId: 'current_user_id', // TODO: Get from auth
          service: currentState.selectedService!,
          measurements: currentState.measurements,
          fabric: currentState.selectedFabric,
          deliveryOption: currentState.deliveryOption!,
          specialInstructions: currentState.specialInstructions,
          totalPrice: currentState.totalPrice,
          status: OrderStatus.pending,
          createdAt: DateTime.now(),
          estimatedDelivery: DateTime.now().add(
            Duration(days: currentState.deliveryOption!.estimatedDays),
          ),
        );

        // Simulate API call
        await Future.delayed(const Duration(seconds: 2));

        emit(OrderCreationSuccess(order: order));
      } catch (e) {
        emit(OrderCreationError(message: 'Failed to place order: ${e.toString()}'));
      }
    }
  }

  double _calculateTotalPrice({
    ServiceModel? service,
    FabricModel? fabric,
    DeliveryOption? deliveryOption,
  }) {
    double total = 0.0;
    
    if (service != null) {
      total += service.basePrice;
    }
    
    if (fabric != null) {
      total += fabric.additionalCost;
    }
    
    if (deliveryOption != null) {
      total += deliveryOption.cost;
    }
    
    return total;
  }

  List<ServiceModel> _getMockServices() {
    return [
      ServiceModel(
        id: 'service_1',
        name: 'Custom Suit',
        description: 'Tailored business suit with premium finishing',
        basePrice: 250000,
        estimatedDays: 14,
        category: 'Formal Wear',
      ),
      ServiceModel(
        id: 'service_2',
        name: 'Shirt Tailoring',
        description: 'Custom fitted dress shirt',
        basePrice: 45000,
        estimatedDays: 7,
        category: 'Formal Wear',
      ),
      ServiceModel(
        id: 'service_3',
        name: 'Dress Making',
        description: 'Custom dress for special occasions',
        basePrice: 80000,
        estimatedDays: 10,
        category: 'Formal Wear',
      ),
    ];
  }

  List<FabricModel> _getMockFabrics() {
    return [
      FabricModel(
        id: 'fabric_1',
        name: 'Premium Wool',
        description: 'High-quality wool blend',
        additionalCost: 50000,
        imageUrl: 'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=200',
      ),
      FabricModel(
        id: 'fabric_2',
        name: 'Cotton Blend',
        description: 'Comfortable cotton fabric',
        additionalCost: 20000,
        imageUrl: 'https://images.unsplash.com/photo-1586790170083-2f9ceadc732d?w=200',
      ),
    ];
  }

  List<DeliveryOption> _getMockDeliveryOptions() {
    return [
      DeliveryOption(
        id: 'delivery_1',
        name: 'Standard Delivery',
        description: 'Regular delivery timeline',
        estimatedDays: 14,
        cost: 0,
      ),
      DeliveryOption(
        id: 'delivery_2',
        name: 'Express Delivery',
        description: 'Faster delivery with rush charges',
        estimatedDays: 7,
        cost: 25000,
      ),
    ];
  }
}
