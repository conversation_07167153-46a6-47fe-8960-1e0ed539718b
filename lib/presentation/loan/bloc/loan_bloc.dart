import 'dart:math' as math;
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/services/api_service_manager.dart';
import '../../../data/models/loan_model.dart';
import '../../../data/models/user_model.dart';

// Events
abstract class LoanEvent extends Equatable {
  const LoanEvent();

  @override
  List<Object?> get props => [];
}

class LoadLoans extends LoanEvent {
  const LoadLoans();
}

class LoadLoanOffers extends LoanEvent {
  const LoadLoanOffers();
}

class ApplyForLoan extends LoanEvent {
  final LoanApplicationModel application;

  const ApplyForLoan({required this.application});

  @override
  List<Object?> get props => [application];
}

class CheckLoanEligibility extends LoanEvent {
  final double amount;
  final LoanType type;
  final int termMonths;

  const CheckLoanEligibility({
    required this.amount,
    required this.type,
    required this.termMonths,
  });

  @override
  List<Object?> get props => [amount, type, termMonths];
}

class CalculateLoan extends LoanEvent {
  final double amount;
  final double interestRate;
  final int termMonths;

  const CalculateLoan({
    required this.amount,
    required this.interestRate,
    required this.termMonths,
  });

  @override
  List<Object?> get props => [amount, interestRate, termMonths];
}

class MakeLoanPayment extends LoanEvent {
  final String loanId;
  final double amount;
  final String? paymentMethod;

  const MakeLoanPayment({
    required this.loanId,
    required this.amount,
    this.paymentMethod,
  });

  @override
  List<Object?> get props => [loanId, amount, paymentMethod];
}

class LoadLoanDetails extends LoanEvent {
  final String loanId;

  const LoadLoanDetails({required this.loanId});

  @override
  List<Object?> get props => [loanId];
}

// States
abstract class LoanState extends Equatable {
  const LoanState();

  @override
  List<Object?> get props => [];
}

class LoanInitial extends LoanState {
  const LoanInitial();
}

class LoanLoading extends LoanState {
  const LoanLoading();
}

class LoanLoaded extends LoanState {
  final UserModel user;
  final List<LoanModel> loans;
  final List<LoanApplicationModel> applications;
  final List<Map<String, dynamic>> offers;
  final Map<String, dynamic>? eligibility;
  final Map<String, dynamic>? calculation;

  const LoanLoaded({
    required this.user,
    required this.loans,
    required this.applications,
    required this.offers,
    this.eligibility,
    this.calculation,
  });

  LoanLoaded copyWith({
    UserModel? user,
    List<LoanModel>? loans,
    List<LoanApplicationModel>? applications,
    List<Map<String, dynamic>>? offers,
    Map<String, dynamic>? eligibility,
    Map<String, dynamic>? calculation,
  }) {
    return LoanLoaded(
      user: user ?? this.user,
      loans: loans ?? this.loans,
      applications: applications ?? this.applications,
      offers: offers ?? this.offers,
      eligibility: eligibility ?? this.eligibility,
      calculation: calculation ?? this.calculation,
    );
  }

  @override
  List<Object?> get props => [user, loans, applications, offers, eligibility, calculation];
}

class LoanError extends LoanState {
  final String message;

  const LoanError({required this.message});

  @override
  List<Object?> get props => [message];
}

class LoanApplicationSuccess extends LoanState {
  final String message;
  final LoanApplicationModel application;

  const LoanApplicationSuccess({
    required this.message,
    required this.application,
  });

  @override
  List<Object?> get props => [message, application];
}

class LoanPaymentSuccess extends LoanState {
  final String message;
  final LoanModel loan;

  const LoanPaymentSuccess({
    required this.message,
    required this.loan,
  });

  @override
  List<Object?> get props => [message, loan];
}

// BLoC
class LoanBloc extends Bloc<LoanEvent, LoanState> {
  final ApiServiceManager _apiService;

  LoanBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const LoanInitial()) {
    on<LoadLoans>(_onLoadLoans);
    on<LoadLoanOffers>(_onLoadLoanOffers);
    on<ApplyForLoan>(_onApplyForLoan);
    on<CheckLoanEligibility>(_onCheckLoanEligibility);
    on<CalculateLoan>(_onCalculateLoan);
    on<MakeLoanPayment>(_onMakeLoanPayment);
    on<LoadLoanDetails>(_onLoadLoanDetails);
  }

  Future<void> _onLoadLoans(
    LoadLoans event,
    Emitter<LoanState> emit,
  ) async {
    emit(const LoanLoading());

    try {
      // For now, use mock data but structure it to be API-ready
      // TODO: Replace with real API calls when backend is ready
      final user = await _getMockUser();
      final loans = await _getMockLoans();
      final applications = await _getMockApplications();
      final offers = await _getMockOffers();

      // Future API integration would look like:
      // try {
      //   final loansResponse = await _apiService.loans.getLoans();
      //   final applicationsResponse = await _apiService.loans.getApplications();
      //   final offersResponse = await _apiService.loans.getLoanOffers();
      //   if (loansResponse.success && applicationsResponse.success && offersResponse.success) {
      //     loans = loansResponse.data!.map((data) => LoanModel.fromJson(data)).toList();
      //     applications = applicationsResponse.data!.map((data) => LoanApplicationModel.fromJson(data)).toList();
      //     offers = offersResponse.data!;
      //   }
      // } catch (e) {
      //   // Fallback to mock data
      // }

      emit(LoanLoaded(
        user: user,
        loans: loans,
        applications: applications,
        offers: offers,
      ));
    } catch (e) {
      emit(LoanError(message: 'Failed to load loans: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLoanOffers(
    LoadLoanOffers event,
    Emitter<LoanState> emit,
  ) async {
    if (state is LoanLoaded) {
      final currentState = state as LoanLoaded;
      
      try {
        final offers = await _getMockOffers();

        emit(currentState.copyWith(offers: offers));
      } catch (e) {
        emit(LoanError(message: 'Failed to load loan offers: ${e.toString()}'));
      }
    }
  }

  Future<void> _onApplyForLoan(
    ApplyForLoan event,
    Emitter<LoanState> emit,
  ) async {
    try {
      // TODO: Call real API
      // final response = await _apiService.loans.applyForLoan(
      //   application: event.application.toJson(),
      // );

      // Simulate successful application
      await Future.delayed(const Duration(seconds: 2));
      
      final submittedApplication = event.application.copyWith(
        id: 'app_${DateTime.now().millisecondsSinceEpoch}',
        status: ApplicationStatus.submitted,
        createdAt: DateTime.now(),
      );

      emit(LoanApplicationSuccess(
        message: 'Loan application submitted successfully! We will review your application and get back to you within 24 hours.',
        application: submittedApplication,
      ));

      // Reload loans data
      add(const LoadLoans());
    } catch (e) {
      emit(LoanError(message: 'Failed to submit loan application: ${e.toString()}'));
    }
  }

  Future<void> _onCheckLoanEligibility(
    CheckLoanEligibility event,
    Emitter<LoanState> emit,
  ) async {
    if (state is LoanLoaded) {
      final currentState = state as LoanLoaded;
      
      try {
        // TODO: Call real API
        // final response = await _apiService.loans.checkEligibility(
        //   amount: event.amount,
        //   type: event.type.name,
        //   termMonths: event.termMonths,
        // );

        // Simulate eligibility check
        await Future.delayed(const Duration(seconds: 1));
        
        final eligibility = {
          'eligible': event.amount <= 5000000, // Max ₮5M
          'max_amount': 5000000.0,
          'min_amount': 100000.0,
          'interest_rate': 12.5,
          'max_term': 60,
          'min_term': 6,
          'requirements': [
            'Valid ID document',
            'Proof of income',
            'Bank statements (3 months)',
            'Business registration (for business loans)',
          ],
        };

        emit(currentState.copyWith(eligibility: eligibility));
      } catch (e) {
        emit(LoanError(message: 'Failed to check eligibility: ${e.toString()}'));
      }
    }
  }

  Future<void> _onCalculateLoan(
    CalculateLoan event,
    Emitter<LoanState> emit,
  ) async {
    if (state is LoanLoaded) {
      final currentState = state as LoanLoaded;
      
      try {
        // TODO: Call real API
        // final response = await _apiService.loans.calculateLoan(
        //   amount: event.amount,
        //   interestRate: event.interestRate,
        //   termMonths: event.termMonths,
        // );

        // Calculate loan terms
        final monthlyRate = event.interestRate / 100 / 12;
        final monthlyPayment = event.amount *
            (monthlyRate * math.pow(1 + monthlyRate, event.termMonths)) /
            (math.pow(1 + monthlyRate, event.termMonths) - 1);
        
        final totalPayment = monthlyPayment * event.termMonths;
        final totalInterest = totalPayment - event.amount;

        final calculation = {
          'principal': event.amount,
          'interest_rate': event.interestRate,
          'term_months': event.termMonths,
          'monthly_payment': monthlyPayment,
          'total_payment': totalPayment,
          'total_interest': totalInterest,
        };

        emit(currentState.copyWith(calculation: calculation));
      } catch (e) {
        emit(LoanError(message: 'Failed to calculate loan: ${e.toString()}'));
      }
    }
  }

  Future<void> _onMakeLoanPayment(
    MakeLoanPayment event,
    Emitter<LoanState> emit,
  ) async {
    try {
      // TODO: Call real API
      // final response = await _apiService.loans.makePayment(
      //   loanId: event.loanId,
      //   amount: event.amount,
      //   paymentMethod: event.paymentMethod,
      // );

      // Simulate successful payment
      await Future.delayed(const Duration(seconds: 2));
      
      // Find and update the loan
      if (state is LoanLoaded) {
        final currentState = state as LoanLoaded;
        final loanIndex = currentState.loans.indexWhere((loan) => loan.id == event.loanId);
        
        if (loanIndex != -1) {
          final loan = currentState.loans[loanIndex];
          final updatedLoan = loan.copyWith(
            remainingBalance: loan.remainingBalance - event.amount,
            remainingPayments: loan.remainingPayments - 1,
          );

          emit(LoanPaymentSuccess(
            message: 'Payment successful! ₮${event.amount.toStringAsFixed(0)} paid towards your loan.',
            loan: updatedLoan,
          ));

          // Reload loans data
          add(const LoadLoans());
        }
      }
    } catch (e) {
      emit(LoanError(message: 'Payment failed: ${e.toString()}'));
    }
  }

  Future<void> _onLoadLoanDetails(
    LoadLoanDetails event,
    Emitter<LoanState> emit,
  ) async {
    try {
      // TODO: Call real API to get detailed loan information
      // final response = await _apiService.loans.getLoan(loanId: event.loanId);
      
      // For now, just trigger a reload
      add(const LoadLoans());
    } catch (e) {
      emit(LoanError(message: 'Failed to load loan details: ${e.toString()}'));
    }
  }

  // Mock data methods
  Future<UserModel> _getMockUser() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return UserModel(
      id: 'user_1',
      email: '<EMAIL>',
      firstName: 'Master',
      lastName: 'Tailor',
      phone: '+976 9999 9999',
      role: UserRole.tailor,
      isEmailVerified: true,
      isPhoneVerified: true,
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
    );
  }

  Future<List<LoanModel>> _getMockLoans() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      LoanModel(
        id: 'loan_1',
        userId: 'user_1',
        type: LoanType.business,
        amount: 2000000.0,
        interestRate: 12.5,
        termMonths: 24,
        status: LoanStatus.active,
        applicationDate: DateTime.now().subtract(const Duration(days: 60)),
        approvalDate: DateTime.now().subtract(const Duration(days: 55)),
        disbursementDate: DateTime.now().subtract(const Duration(days: 50)),
        dueDate: DateTime.now().add(const Duration(days: 30)),
        monthlyPayment: 94000.0,
        totalInterest: 256000.0,
        remainingBalance: 1200000.0,
        remainingPayments: 14,
        payments: [],
        purpose: LoanPurpose.business,
        collateral: 'Business equipment',
      ),
    ];
  }

  Future<List<LoanApplicationModel>> _getMockApplications() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      LoanApplicationModel(
        id: 'app_1',
        userId: 'user_1',
        type: LoanType.equipment,
        requestedAmount: 1500000.0,
        termMonths: 18,
        purpose: LoanPurpose.equipment,
        businessDescription: 'Need new sewing machines for expanding tailoring business',
        monthlyIncome: 800000.0,
        monthlyExpenses: 400000.0,
        documents: ['id_card.pdf', 'income_statement.pdf'],
        status: ApplicationStatus.underReview,
        createdAt: DateTime.now().subtract(const Duration(days: 5)),
      ),
    ];
  }

  Future<List<Map<String, dynamic>>> _getMockOffers() async {
    await Future.delayed(const Duration(milliseconds: 500));
    
    return [
      {
        'id': 'offer_1',
        'title': 'Business Growth Loan',
        'description': 'Expand your tailoring business with our competitive rates',
        'min_amount': 500000.0,
        'max_amount': 10000000.0,
        'interest_rate': 11.5,
        'max_term': 60,
        'features': ['No collateral required', 'Quick approval', 'Flexible repayment'],
      },
      {
        'id': 'offer_2',
        'title': 'Equipment Finance',
        'description': 'Get the latest tailoring equipment with easy financing',
        'min_amount': 200000.0,
        'max_amount': 5000000.0,
        'interest_rate': 10.5,
        'max_term': 36,
        'features': ['Equipment as collateral', 'Lower interest rates', 'Fast processing'],
      },
      {
        'id': 'offer_3',
        'title': 'Emergency Fund',
        'description': 'Quick access to funds for urgent business needs',
        'min_amount': 100000.0,
        'max_amount': 2000000.0,
        'interest_rate': 15.0,
        'max_term': 12,
        'features': ['Same day approval', 'No documentation', 'Instant disbursement'],
      },
    ];
  }
}
