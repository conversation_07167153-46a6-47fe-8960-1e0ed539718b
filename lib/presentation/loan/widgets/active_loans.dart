import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/loan_model.dart';

/// Instagram-inspired active loans widget
class ActiveLoans extends StatelessWidget {
  final List<LoanModel> loans;
  final Function(String, double) onPayment;
  final Function(String) onViewDetails;

  const ActiveLoans({
    super.key,
    required this.loans,
    required this.onPayment,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    final activeLoans = loans.where((loan) => 
        loan.status == LoanStatus.active || 
        loan.status == LoanStatus.disbursed
    ).toList();

    if (activeLoans.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      itemCount: activeLoans.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConfig.defaultPadding),
      itemBuilder: (context, index) {
        final loan = activeLoans[index];
        return _buildLoanCard(context, loan, index);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.account_balance_outlined,
            size: 64,
            color: AppTheme.dividerColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No active loans',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your active loans will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.largePadding),
          ElevatedButton(
            onPressed: () {
              // Navigate to loan offers
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Explore Loan Offers'),
          ),
        ],
      ),
    );
  }

  Widget _buildLoanCard(
    BuildContext context,
    LoanModel loan,
    int index,
  ) {
    final progress = (loan.amount - loan.remainingBalance) / loan.amount;
    final isOverdue = loan.dueDate != null && loan.dueDate!.isBefore(DateTime.now());
    
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onViewDetails(loan.id),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(
              color: isOverdue ? Colors.red.withValues(alpha: 0.3) : AppTheme.dividerColor,
            ),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getLoanTypeColor(loan.type).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getLoanTypeIcon(loan.type),
                      color: _getLoanTypeColor(loan.type),
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: AppConfig.defaultPadding),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatLoanType(loan.type),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Text(
                          'Loan #${loan.id.substring(0, 8)}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status Badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: isOverdue 
                          ? Colors.red.withValues(alpha: 0.1)
                          : Colors.green.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      isOverdue ? 'Overdue' : 'Active',
                      style: TextStyle(
                        color: isOverdue ? Colors.red : Colors.green,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Loan Progress
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        'Remaining Balance',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                      Text(
                        '${(progress * 100).toStringAsFixed(0)}% paid',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: AppTheme.dividerColor,
                    valueColor: AlwaysStoppedAnimation<Color>(
                      isOverdue ? Colors.red : AppTheme.primaryColor,
                    ),
                  ),
                  
                  const SizedBox(height: 8),
                  
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '₮${_formatAmount(loan.remainingBalance)}',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: AppTheme.primaryTextColor,
                        ),
                      ),
                      Text(
                        'of ₮${_formatAmount(loan.amount)}',
                        style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: AppTheme.secondaryTextColor,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Loan Details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Monthly Payment',
                      value: '₮${_formatAmount(loan.monthlyPayment)}',
                    ),
                  ),
                  
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Remaining Payments',
                      value: '${loan.remainingPayments}',
                    ),
                  ),
                  
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Interest Rate',
                      value: '${loan.interestRate}%',
                    ),
                  ),
                ],
              ),
              
              if (loan.dueDate != null) ...[
                const SizedBox(height: AppConfig.defaultPadding),
                
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: isOverdue 
                        ? Colors.red.withValues(alpha: 0.1)
                        : AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        isOverdue ? Icons.warning : Icons.schedule,
                        color: isOverdue ? Colors.red : AppTheme.primaryColor,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        isOverdue 
                            ? 'Payment overdue since ${DateFormat('MMM dd').format(loan.dueDate!)}'
                            : 'Next payment due ${DateFormat('MMM dd').format(loan.dueDate!)}',
                        style: TextStyle(
                          color: isOverdue ? Colors.red : AppTheme.primaryColor,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Action Buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton(
                      onPressed: () => onViewDetails(loan.id),
                      style: OutlinedButton.styleFrom(
                        foregroundColor: AppTheme.primaryColor,
                        side: BorderSide(color: AppTheme.primaryColor),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('View Details'),
                    ),
                  ),
                  
                  const SizedBox(width: AppConfig.defaultPadding),
                  
                  Expanded(
                    child: ElevatedButton(
                      onPressed: () => _showPaymentDialog(context, loan),
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppTheme.primaryColor,
                        foregroundColor: Colors.white,
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('Make Payment'),
                    ),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(delay: (index * 100).ms, duration: 400.ms);
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryTextColor,
          ),
        ),
      ],
    );
  }

  void _showPaymentDialog(BuildContext context, LoanModel loan) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Make Payment'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text('Monthly payment: ₮${_formatAmount(loan.monthlyPayment)}'),
            const SizedBox(height: 16),
            Text('Would you like to make the monthly payment?'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              onPayment(loan.id, loan.monthlyPayment);
            },
            child: const Text('Pay Now'),
          ),
        ],
      ),
    );
  }

  IconData _getLoanTypeIcon(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return Icons.person;
      case LoanType.business:
        return Icons.business;
      case LoanType.equipment:
        return Icons.build;
      case LoanType.inventory:
        return Icons.inventory;
      case LoanType.emergency:
        return Icons.emergency;
    }
  }

  Color _getLoanTypeColor(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return Colors.blue;
      case LoanType.business:
        return AppTheme.primaryColor;
      case LoanType.equipment:
        return Colors.orange;
      case LoanType.inventory:
        return Colors.green;
      case LoanType.emergency:
        return Colors.red;
    }
  }

  String _formatLoanType(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.equipment:
        return 'Equipment Loan';
      case LoanType.inventory:
        return 'Inventory Loan';
      case LoanType.emergency:
        return 'Emergency Loan';
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
