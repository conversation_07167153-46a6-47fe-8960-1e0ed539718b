import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/loan_model.dart';

/// Instagram-inspired loan calculator widget
class LoanCalculator extends StatefulWidget {
  final Map<String, dynamic>? calculation;
  final Map<String, dynamic>? eligibility;
  final Function(double, double, int) onCalculate;
  final Function(double, String, int) onCheckEligibility;

  const LoanCalculator({
    super.key,
    this.calculation,
    this.eligibility,
    required this.onCalculate,
    required this.onCheckEligibility,
  });

  @override
  State<LoanCalculator> createState() => _LoanCalculatorState();
}

class _LoanCalculatorState extends State<LoanCalculator> {
  final _formKey = GlobalKey<FormState>();
  final _amountController = TextEditingController();
  final _interestController = TextEditingController(text: '12.5');
  final _termController = TextEditingController();
  
  LoanType _selectedType = LoanType.business;
  double _amount = 1000000;
  double _interestRate = 12.5;
  int _termMonths = 24;

  @override
  void initState() {
    super.initState();
    _amountController.text = _amount.toStringAsFixed(0);
    _termController.text = _termMonths.toString();
  }

  @override
  void dispose() {
    _amountController.dispose();
    _interestController.dispose();
    _termController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Form(
        key: _formKey,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Calculator Section
            _buildCalculatorSection(context),
            
            const SizedBox(height: AppConfig.largePadding),
            
            // Results Section
            if (widget.calculation != null)
              _buildResultsSection(context),
            
            const SizedBox(height: AppConfig.largePadding),
            
            // Eligibility Section
            _buildEligibilitySection(context),
            
            if (widget.eligibility != null) ...[
              const SizedBox(height: AppConfig.defaultPadding),
              _buildEligibilityResults(context),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildCalculatorSection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Loan Calculator',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Loan Amount
          TextFormField(
            controller: _amountController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'Loan Amount (₮)',
              hintText: 'Enter loan amount',
              prefixIcon: const Icon(Icons.attach_money),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                setState(() {
                  _amount = double.tryParse(value) ?? 0;
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter loan amount';
              }
              final amount = double.tryParse(value);
              if (amount == null || amount <= 0) {
                return 'Please enter a valid amount';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Interest Rate
          TextFormField(
            controller: _interestController,
            keyboardType: const TextInputType.numberWithOptions(decimal: true),
            decoration: InputDecoration(
              labelText: 'Interest Rate (%)',
              hintText: 'Enter interest rate',
              prefixIcon: const Icon(Icons.percent),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                setState(() {
                  _interestRate = double.tryParse(value) ?? 0;
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter interest rate';
              }
              final rate = double.tryParse(value);
              if (rate == null || rate <= 0) {
                return 'Please enter a valid rate';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Term
          TextFormField(
            controller: _termController,
            keyboardType: TextInputType.number,
            inputFormatters: [FilteringTextInputFormatter.digitsOnly],
            decoration: InputDecoration(
              labelText: 'Term (months)',
              hintText: 'Enter loan term',
              prefixIcon: const Icon(Icons.schedule),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            onChanged: (value) {
              if (value.isNotEmpty) {
                setState(() {
                  _termMonths = int.tryParse(value) ?? 0;
                });
              }
            },
            validator: (value) {
              if (value == null || value.isEmpty) {
                return 'Please enter loan term';
              }
              final term = int.tryParse(value);
              if (term == null || term <= 0) {
                return 'Please enter a valid term';
              }
              return null;
            },
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Calculate Button
          SizedBox(
            width: double.infinity,
            child: ElevatedButton(
              onPressed: _calculateLoan,
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
                foregroundColor: Colors.white,
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Calculate',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 100.ms, duration: 600.ms);
  }

  Widget _buildResultsSection(BuildContext context) {
    final calculation = widget.calculation!;
    
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Calculation Results',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Monthly Payment
          _buildResultItem(
            context,
            label: 'Monthly Payment',
            value: '₮${_formatAmount(calculation['monthly_payment'])}',
            icon: Icons.payment,
            color: AppTheme.primaryColor,
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Total Payment
          _buildResultItem(
            context,
            label: 'Total Payment',
            value: '₮${_formatAmount(calculation['total_payment'])}',
            icon: Icons.account_balance_wallet,
            color: Colors.blue,
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Total Interest
          _buildResultItem(
            context,
            label: 'Total Interest',
            value: '₮${_formatAmount(calculation['total_interest'])}',
            icon: Icons.trending_up,
            color: Colors.orange,
          ),
        ],
      ),
    ).animate().fadeIn(delay: 200.ms, duration: 600.ms);
  }

  Widget _buildResultItem(
    BuildContext context, {
    required String label,
    required String value,
    required IconData icon,
    required Color color,
  }) {
    return Row(
      children: [
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color.withValues(alpha: 0.1),
            borderRadius: BorderRadius.circular(8),
          ),
          child: Icon(
            icon,
            color: color,
            size: 20,
          ),
        ),
        
        const SizedBox(width: 12),
        
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.secondaryTextColor,
                ),
              ),
              Text(
                value,
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  Widget _buildEligibilitySection(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(color: AppTheme.dividerColor),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Check Eligibility',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Loan Type Dropdown
          DropdownButtonFormField<LoanType>(
            value: _selectedType,
            decoration: InputDecoration(
              labelText: 'Loan Type',
              prefixIcon: const Icon(Icons.category),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
              ),
            ),
            items: LoanType.values.map((type) {
              return DropdownMenuItem(
                value: type,
                child: Text(_formatLoanType(type)),
              );
            }).toList(),
            onChanged: (value) {
              if (value != null) {
                setState(() {
                  _selectedType = value;
                });
              }
            },
          ),
          
          const SizedBox(height: AppConfig.defaultPadding),
          
          // Check Eligibility Button
          SizedBox(
            width: double.infinity,
            child: OutlinedButton(
              onPressed: _checkEligibility,
              style: OutlinedButton.styleFrom(
                foregroundColor: AppTheme.primaryColor,
                side: BorderSide(color: AppTheme.primaryColor),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: const Text(
                'Check Eligibility',
                style: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),
        ],
      ),
    ).animate().fadeIn(delay: 300.ms, duration: 600.ms);
  }

  Widget _buildEligibilityResults(BuildContext context) {
    final eligibility = widget.eligibility!;
    final isEligible = eligibility['eligible'] as bool;
    
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: isEligible 
            ? Colors.green.withValues(alpha: 0.1)
            : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isEligible ? Colors.green : Colors.red,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                isEligible ? Icons.check_circle : Icons.cancel,
                color: isEligible ? Colors.green : Colors.red,
              ),
              const SizedBox(width: 8),
              Text(
                isEligible ? 'You are eligible!' : 'Not eligible',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: isEligible ? Colors.green : Colors.red,
                ),
              ),
            ],
          ),
          
          if (isEligible) ...[
            const SizedBox(height: AppConfig.defaultPadding),
            Text(
              'Max Amount: ₮${_formatAmount(eligibility['max_amount'])}',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Interest Rate: ${eligibility['interest_rate']}%',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
            Text(
              'Max Term: ${eligibility['max_term']} months',
              style: Theme.of(context).textTheme.bodyMedium,
            ),
          ],
        ],
      ),
    ).animate().fadeIn(delay: 400.ms, duration: 600.ms);
  }

  void _calculateLoan() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onCalculate(_amount, _interestRate, _termMonths);
    }
  }

  void _checkEligibility() {
    if (_formKey.currentState?.validate() ?? false) {
      widget.onCheckEligibility(_amount, _selectedType.name, _termMonths);
    }
  }

  String _formatLoanType(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.equipment:
        return 'Equipment Loan';
      case LoanType.inventory:
        return 'Inventory Loan';
      case LoanType.emergency:
        return 'Emergency Loan';
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
