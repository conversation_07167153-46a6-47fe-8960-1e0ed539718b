import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:intl/intl.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/loan_model.dart';

/// Instagram-inspired loan applications widget
class LoanApplications extends StatelessWidget {
  final List<LoanApplicationModel> applications;
  final Function(String) onViewDetails;

  const LoanApplications({
    super.key,
    required this.applications,
    required this.onViewDetails,
  });

  @override
  Widget build(BuildContext context) {
    if (applications.isEmpty) {
      return _buildEmptyState(context);
    }

    return ListView.separated(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      itemCount: applications.length,
      separatorBuilder: (context, index) => const SizedBox(height: AppConfig.defaultPadding),
      itemBuilder: (context, index) {
        final application = applications[index];
        return _buildApplicationCard(context, application, index);
      },
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: AppTheme.dividerColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No loan applications',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Your loan applications will appear here',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: AppConfig.largePadding),
          ElevatedButton(
            onPressed: () {
              Navigator.pushNamed(context, '/loan/apply');
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
              foregroundColor: Colors.white,
            ),
            child: const Text('Apply for Loan'),
          ),
        ],
      ),
    );
  }

  Widget _buildApplicationCard(
    BuildContext context,
    LoanApplicationModel application,
    int index,
  ) {
    return Material(
      color: Colors.transparent,
      child: InkWell(
        onTap: () => onViewDetails(application.id ?? ''),
        borderRadius: BorderRadius.circular(16),
        child: Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: AppTheme.dividerColor),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 4),
              ),
            ],
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: _getApplicationStatusColor(application.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Icon(
                      _getApplicationStatusIcon(application.status),
                      color: _getApplicationStatusColor(application.status),
                      size: 24,
                    ),
                  ),
                  
                  const SizedBox(width: AppConfig.defaultPadding),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          _formatLoanType(application.type),
                          style: Theme.of(context).textTheme.titleMedium?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        
                        const SizedBox(height: 4),
                        
                        Text(
                          'Applied ${DateFormat('MMM dd, yyyy').format(application.createdAt)}',
                          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  
                  // Status Badge
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: _getApplicationStatusColor(application.status).withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      _formatApplicationStatus(application.status),
                      style: TextStyle(
                        color: _getApplicationStatusColor(application.status),
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Application Details
              Row(
                children: [
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Requested Amount',
                      value: '₮${_formatAmount(application.requestedAmount)}',
                    ),
                  ),
                  
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Term',
                      value: '${application.termMonths} months',
                    ),
                  ),
                  
                  Expanded(
                    child: _buildDetailItem(
                      context,
                      label: 'Purpose',
                      value: _formatLoanPurpose(application.purpose),
                    ),
                  ),
                ],
              ),
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Business Description
              if (application.businessDescription.isNotEmpty) ...[
                Text(
                  'Description',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w600,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  application.businessDescription,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.secondaryTextColor,
                  ),
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                const SizedBox(height: AppConfig.defaultPadding),
              ],
              
              // Documents
              if (application.documents.isNotEmpty) ...[
                Row(
                  children: [
                    Icon(
                      Icons.attach_file,
                      size: 16,
                      color: AppTheme.secondaryTextColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      '${application.documents.length} documents attached',
                      style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: AppConfig.defaultPadding),
              ],
              
              // Rejection Reason (if applicable)
              if (application.status == ApplicationStatus.rejected && 
                  application.rejectionReason != null) ...[
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    children: [
                      Icon(
                        Icons.info_outline,
                        color: Colors.red,
                        size: 16,
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          application.rejectionReason!,
                          style: TextStyle(
                            color: Colors.red,
                            fontSize: 12,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(height: AppConfig.defaultPadding),
              ],
              
              // Action Button
              SizedBox(
                width: double.infinity,
                child: OutlinedButton(
                  onPressed: () => onViewDetails(application.id ?? ''),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.primaryColor,
                    side: BorderSide(color: AppTheme.primaryColor),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: const Text('View Details'),
                ),
              ),
            ],
          ),
        ),
      ),
    ).animate().fadeIn(delay: (index * 100).ms, duration: 400.ms);
  }

  Widget _buildDetailItem(
    BuildContext context, {
    required String label,
    required String value,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.secondaryTextColor,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          value,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryTextColor,
          ),
        ),
      ],
    );
  }

  IconData _getApplicationStatusIcon(ApplicationStatus status) {
    switch (status) {
      case ApplicationStatus.draft:
        return Icons.edit;
      case ApplicationStatus.submitted:
        return Icons.send;
      case ApplicationStatus.underReview:
        return Icons.hourglass_empty;
      case ApplicationStatus.approved:
        return Icons.check_circle;
      case ApplicationStatus.rejected:
        return Icons.cancel;
      case ApplicationStatus.cancelled:
        return Icons.close;
    }
  }

  Color _getApplicationStatusColor(ApplicationStatus status) {
    switch (status) {
      case ApplicationStatus.draft:
        return Colors.grey;
      case ApplicationStatus.submitted:
        return Colors.blue;
      case ApplicationStatus.underReview:
        return Colors.orange;
      case ApplicationStatus.approved:
        return Colors.green;
      case ApplicationStatus.rejected:
        return Colors.red;
      case ApplicationStatus.cancelled:
        return Colors.grey;
    }
  }

  String _formatApplicationStatus(ApplicationStatus status) {
    switch (status) {
      case ApplicationStatus.draft:
        return 'Draft';
      case ApplicationStatus.submitted:
        return 'Submitted';
      case ApplicationStatus.underReview:
        return 'Under Review';
      case ApplicationStatus.approved:
        return 'Approved';
      case ApplicationStatus.rejected:
        return 'Rejected';
      case ApplicationStatus.cancelled:
        return 'Cancelled';
    }
  }

  String _formatLoanType(LoanType type) {
    switch (type) {
      case LoanType.personal:
        return 'Personal Loan';
      case LoanType.business:
        return 'Business Loan';
      case LoanType.equipment:
        return 'Equipment Loan';
      case LoanType.inventory:
        return 'Inventory Loan';
      case LoanType.emergency:
        return 'Emergency Loan';
    }
  }

  String _formatLoanPurpose(LoanPurpose purpose) {
    switch (purpose) {
      case LoanPurpose.business:
        return 'Business';
      case LoanPurpose.equipment:
        return 'Equipment';
      case LoanPurpose.inventory:
        return 'Inventory';
      case LoanPurpose.expansion:
        return 'Expansion';
      case LoanPurpose.emergency:
        return 'Emergency';
      case LoanPurpose.education:
        return 'Education';
      case LoanPurpose.personal:
        return 'Personal';
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
