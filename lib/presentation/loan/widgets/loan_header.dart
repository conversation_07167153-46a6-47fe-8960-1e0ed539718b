import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../data/models/loan_model.dart';
import '../../../data/models/user_model.dart';

/// Instagram-inspired loan header with user info and loan summary
class LoanHeader extends StatelessWidget {
  final UserModel user;
  final List<LoanModel> loans;
  final List<LoanApplicationModel> applications;

  const LoanHeader({
    super.key,
    required this.user,
    required this.loans,
    required this.applications,
  });

  @override
  Widget build(BuildContext context) {
    final activeLoans = loans.where((loan) => loan.status == LoanStatus.active).toList();
    final totalDebt = activeLoans.fold(0.0, (sum, loan) => sum + loan.remainingBalance);
    final monthlyPayment = activeLoans.fold(0.0, (sum, loan) => sum + loan.monthlyPayment);

    return Container(
      decoration: BoxDecoration(
        gradient: AppTheme.instagramGradient,
      ),
      child: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              const SizedBox(height: 60), // Space for app bar
              
              // User Info
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: user.profileImageUrl != null
                        ? NetworkImage(user.profileImageUrl!)
                        : null,
                    child: user.profileImageUrl == null
                        ? Text(
                            (user.firstName!.isNotEmpty) ? user.firstName![0].toUpperCase() : 'U',
                            style: const TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Colors.white,
                            ),
                          )
                        : null,
                  ).animate().scale(delay: 100.ms, duration: 400.ms),
                  
                  const SizedBox(width: AppConfig.defaultPadding),
                  
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          '${user.firstName} ${user.lastName}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                        Text(
                          'Credit Score: 750', // Mock credit score
                          style: TextStyle(
                            color: Colors.white.withValues(alpha: 0.8),
                            fontSize: 12,
                          ),
                        ),
                      ],
                    ).animate().fadeIn(delay: 200.ms, duration: 600.ms),
                  ),
                  
                  // Credit Status
                  Container(
                    padding: const EdgeInsets.symmetric(
                      horizontal: 8,
                      vertical: 4,
                    ),
                    decoration: BoxDecoration(
                      color: Colors.green.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(12),
                      border: Border.all(color: Colors.green),
                    ),
                    child: Text(
                      'Good',
                      style: TextStyle(
                        color: Colors.green,
                        fontSize: 10,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ).animate().fadeIn(delay: 300.ms, duration: 600.ms),
                ],
              ),
              
              const SizedBox(height: AppConfig.defaultPadding),
              
              // Loan Summary
              Row(
                children: [
                  Expanded(
                    child: _buildSummaryItem(
                      context,
                      title: 'Total Debt',
                      value: '₮${_formatAmount(totalDebt)}',
                      subtitle: '${activeLoans.length} active loans',
                    ).animate().fadeIn(delay: 400.ms, duration: 600.ms),
                  ),
                  
                  const SizedBox(width: AppConfig.defaultPadding),
                  
                  Expanded(
                    child: _buildSummaryItem(
                      context,
                      title: 'Monthly Payment',
                      value: '₮${_formatAmount(monthlyPayment)}',
                      subtitle: 'Due every month',
                    ).animate().fadeIn(delay: 500.ms, duration: 600.ms),
                  ),
                ],
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildSummaryItem(
    BuildContext context, {
    required String title,
    required String value,
    required String subtitle,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.8),
            fontSize: 12,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontSize: 18,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 2),
        Text(
          subtitle,
          style: TextStyle(
            color: Colors.white.withValues(alpha: 0.7),
            fontSize: 10,
          ),
        ),
      ],
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 1000000) {
      return '${(amount / 1000000).toStringAsFixed(1)}M';
    } else if (amount >= 1000) {
      return '${(amount / 1000).toStringAsFixed(0)}K';
    } else {
      return amount.toStringAsFixed(0);
    }
  }
}
