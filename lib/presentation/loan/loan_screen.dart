import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';
import '../../data/models/loan_model.dart';
import 'bloc/loan_bloc.dart';
import 'widgets/loan_header.dart';
import 'widgets/loan_offers.dart';
import 'widgets/active_loans.dart';
import 'widgets/loan_applications.dart';
import 'widgets/loan_calculator.dart';

/// Instagram-inspired loan screen for financial services
class LoanScreen extends StatefulWidget {
  const LoanScreen({super.key});

  @override
  State<LoanScreen> createState() => _LoanScreenState();
}

class _LoanScreenState extends State<LoanScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final ScrollController _scrollController = ScrollController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => LoanBloc()..add(const LoadLoans()),
      child: Scaffold(
        body: BlocConsumer<LoanBloc, LoanState>(
          listener: (context, state) {
            if (state is LoanError) {
              ScaffoldMessenger.of(context).showSnackBar(
                AccessibilityHelper.accessibleSnackBar(
                  message: state.message,
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            } else if (state is LoanApplicationSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                AccessibilityHelper.accessibleSnackBar(
                  message: state.message,
                  backgroundColor: AppTheme.successColor,
                ),
              );
            } else if (state is LoanPaymentSuccess) {
              ScaffoldMessenger.of(context).showSnackBar(
                AccessibilityHelper.accessibleSnackBar(
                  message: state.message,
                  backgroundColor: AppTheme.successColor,
                ),
              );
            }
          },
          builder: (context, state) {
            if (state is LoanLoading) {
              return _buildLoadingState();
            } else if (state is LoanLoaded) {
              return _buildLoadedState(context, state);
            } else if (state is LoanError) {
              return _buildErrorState(context, state);
            }
            return _buildInitialState();
          },
        ),
      ),
    );
  }

  Widget _buildLoadingState() {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          CircularProgressIndicator(),
          SizedBox(height: AppConfig.defaultPadding),
          Text('Loading loan information...'),
        ],
      ),
    );
  }

  Widget _buildErrorState(BuildContext context, LoanError state) {
    return AccessibilityHelper.accessibleErrorMessage(
      message: state.message,
      onRetry: () {
        context.read<LoanBloc>().add(const LoadLoans());
      },
    );
  }

  Widget _buildInitialState() {
    return const Center(
      child: Text('Welcome to loan services'),
    );
  }

  Widget _buildLoadedState(BuildContext context, LoanLoaded state) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // Custom App Bar with Loan Header
        SliverAppBar(
          expandedHeight: 200,
          floating: false,
          pinned: true,
          backgroundColor: AppTheme.primaryColor,
          flexibleSpace: FlexibleSpaceBar(
            background: LoanHeader(
              user: state.user,
              loans: state.loans,
              applications: state.applications,
            ).animate().fadeIn(duration: 600.ms),
          ),
          title: Text(
            'Loans & Credit',
            style: TextStyle(
              color: Colors.white,
              fontWeight: FontWeight.bold,
            ),
          ),
          actions: [
            IconButton(
              onPressed: () {
                context.read<LoanBloc>().add(const LoadLoans());
              },
              icon: const Icon(
                Icons.refresh,
                color: Colors.white,
              ),
              tooltip: 'Refresh loans',
            ),
            IconButton(
              onPressed: () {
                _showLoanMenu(context);
              },
              icon: const Icon(
                Icons.more_vert,
                color: Colors.white,
              ),
              tooltip: 'Loan options',
            ),
          ],
        ),

        // Tab Bar
        SliverToBoxAdapter(
          child: Container(
            color: Colors.white,
            child: TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: AppTheme.secondaryTextColor,
              indicatorColor: AppTheme.primaryColor,
              isScrollable: true,
              tabs: const [
                Tab(text: 'Offers'),
                Tab(text: 'Active'),
                Tab(text: 'Applications'),
                Tab(text: 'Calculator'),
              ],
            ),
          ),
        ),

        // Tab Content
        SliverFillRemaining(
          child: TabBarView(
            controller: _tabController,
            children: [
              // Loan Offers
              LoanOffers(
                offers: state.offers,
                onApply: (offer) => _showLoanApplication(context, offer),
              ),

              // Active Loans
              ActiveLoans(
                loans: state.loans,
                onPayment: (loanId, amount) => _makeLoanPayment(context, loanId, amount),
                onViewDetails: (loanId) => _viewLoanDetails(context, loanId),
              ),

              // Loan Applications
              LoanApplications(
                applications: state.applications,
                onViewDetails: (applicationId) => _viewApplicationDetails(context, applicationId),
              ),

              // Loan Calculator
              LoanCalculator(
                calculation: state.calculation,
                eligibility: state.eligibility,
                onCalculate: (amount, rate, term) => _calculateLoan(context, amount, rate, term),
                onCheckEligibility: (amount, type, term) => _checkEligibility(context, amount, type, term),
              ),
            ],
          ),
        ),
      ],
    );
  }

  void _showLoanMenu(BuildContext context) {
    showModalBottomSheet(
      context: context,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(
            top: Radius.circular(20),
          ),
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Handle
            Container(
              width: 40,
              height: 4,
              margin: const EdgeInsets.symmetric(vertical: 12),
              decoration: BoxDecoration(
                color: AppTheme.dividerColor,
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            
            ListTile(
              leading: const Icon(Icons.add),
              title: const Text('Apply for New Loan'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/loan/apply');
              },
            ),
            ListTile(
              leading: const Icon(Icons.history),
              title: const Text('Loan History'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/loan/history');
              },
            ),
            ListTile(
              leading: const Icon(Icons.calculate),
              title: const Text('Loan Calculator'),
              onTap: () {
                Navigator.pop(context);
                _tabController.animateTo(3);
              },
            ),
            ListTile(
              leading: const Icon(Icons.help_outline),
              title: const Text('Help & Support'),
              onTap: () {
                Navigator.pop(context);
                Navigator.pushNamed(context, '/help');
              },
            ),
            const SizedBox(height: AppConfig.defaultPadding),
          ],
        ),
      ),
    );
  }

  void _showLoanApplication(BuildContext context, Map<String, dynamic> offer) {
    Navigator.pushNamed(
      context,
      '/loan/apply',
      arguments: offer,
    );
  }

  void _makeLoanPayment(BuildContext context, String loanId, double amount) {
    context.read<LoanBloc>().add(MakeLoanPayment(
      loanId: loanId,
      amount: amount,
    ));
  }

  void _viewLoanDetails(BuildContext context, String loanId) {
    Navigator.pushNamed(
      context,
      '/loan/details',
      arguments: loanId,
    );
  }

  void _viewApplicationDetails(BuildContext context, String applicationId) {
    Navigator.pushNamed(
      context,
      '/loan/application-details',
      arguments: applicationId,
    );
  }

  void _calculateLoan(BuildContext context, double amount, double rate, int term) {
    context.read<LoanBloc>().add(CalculateLoan(
      amount: amount,
      interestRate: rate,
      termMonths: term,
    ));
  }

  void _checkEligibility(BuildContext context, double amount, String type, int term) {
    context.read<LoanBloc>().add(CheckLoanEligibility(
      amount: amount,
      type: LoanType.values.firstWhere((e) => e.name == type),
      termMonths: term,
    ));
  }
}
