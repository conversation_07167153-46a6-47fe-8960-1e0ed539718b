import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';
import '../../data/models/tailor_model.dart';

import 'bloc/tailor_profile_bloc.dart';
import 'widgets/tailor_header.dart';
import 'widgets/tailor_services.dart';
import 'widgets/tailor_portfolio.dart';
import 'widgets/tailor_reviews.dart';

/// Instagram-inspired tailor profile screen
class TailorProfileScreen extends StatefulWidget {
  final String tailorId;

  const TailorProfileScreen({
    super.key,
    required this.tailorId,
  });

  @override
  State<TailorProfileScreen> createState() => _TailorProfileScreenState();
}

class _TailorProfileScreenState extends State<TailorProfileScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  late ScrollController _scrollController;
  bool _isAppBarCollapsed = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _scrollController = ScrollController();
    _scrollController.addListener(_onScroll);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  void _onScroll() {
    final isCollapsed = _scrollController.offset > 200;
    if (isCollapsed != _isAppBarCollapsed) {
      setState(() {
        _isAppBarCollapsed = isCollapsed;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => TailorProfileBloc()
        ..add(LoadTailorProfile(tailorId: widget.tailorId)),
      child: Scaffold(
        body: BlocBuilder<TailorProfileBloc, TailorProfileState>(
          builder: (context, state) {
            if (state is TailorProfileLoading) {
              return AccessibilityHelper.accessibleLoadingIndicator(
                semanticLabel: 'Loading tailor profile',
              );
            }

            if (state is TailorProfileError) {
              return AccessibilityHelper.accessibleErrorMessage(
                message: state.message,
                onRetry: () {
                  context.read<TailorProfileBloc>().add(
                        LoadTailorProfile(tailorId: widget.tailorId),
                      );
                },
              );
            }

            if (state is TailorProfileLoaded) {
              return _buildProfileContent(state.tailor);
            }

            return const SizedBox.shrink();
          },
        ),
      ),
    );
  }

  Widget _buildProfileContent(TailorModel tailor) {
    return CustomScrollView(
      controller: _scrollController,
      slivers: [
        // App Bar with Tailor Header
        SliverAppBar(
          expandedHeight: 400,
          pinned: true,
          backgroundColor: Colors.white,
          elevation: _isAppBarCollapsed ? 1 : 0,
          leading: AccessibilityHelper.accessibleButton(
            child: Container(
              margin: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.3),
                borderRadius: BorderRadius.circular(20),
              ),
              child: const Icon(
                Icons.arrow_back_ios_new,
                color: Colors.white,
                size: 20,
              ),
            ),
            onPressed: () => Navigator.of(context).pop(),
            semanticLabel: 'Go back',
          ),
          actions: [
            AccessibilityHelper.accessibleButton(
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.favorite_border,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: () {
                // TODO: Add to favorites
              },
              semanticLabel: 'Add to favorites',
            ),
            AccessibilityHelper.accessibleButton(
              child: Container(
                margin: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.3),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.share,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              onPressed: () {
                // TODO: Share tailor profile
              },
              semanticLabel: 'Share profile',
            ),
          ],
          flexibleSpace: FlexibleSpaceBar(
            background: TailorHeader(tailor: tailor),
          ),
          title: _isAppBarCollapsed
              ? Text(
                  tailor.name,
                  style: const TextStyle(
                    color: AppTheme.primaryTextColor,
                    fontWeight: FontWeight.w600,
                  ),
                )
              : null,
        ),

        // Tab Bar
        SliverPersistentHeader(
          pinned: true,
          delegate: _SliverTabBarDelegate(
            TabBar(
              controller: _tabController,
              labelColor: AppTheme.primaryColor,
              unselectedLabelColor: AppTheme.secondaryTextColor,
              indicatorColor: AppTheme.primaryColor,
              indicatorWeight: 3,
              tabs: const [
                Tab(text: 'Services'),
                Tab(text: 'Portfolio'),
                Tab(text: 'Reviews'),
              ],
            ),
          ),
        ),

        // Tab Content
        SliverFillRemaining(
          child: TabBarView(
            controller: _tabController,
            children: [
              TailorServices(tailor: tailor),
              TailorPortfolio(tailor: tailor),
              TailorReviews(tailorId: tailor.id),
            ],
          ),
        ),
      ],
    );
  }
}

class _SliverTabBarDelegate extends SliverPersistentHeaderDelegate {
  final TabBar _tabBar;

  _SliverTabBarDelegate(this._tabBar);

  @override
  double get minExtent => _tabBar.preferredSize.height;

  @override
  double get maxExtent => _tabBar.preferredSize.height;

  @override
  Widget build(
    BuildContext context,
    double shrinkOffset,
    bool overlapsContent,
  ) {
    return Container(
      color: Colors.white,
      child: _tabBar,
    );
  }

  @override
  bool shouldRebuild(_SliverTabBarDelegate oldDelegate) {
    return false;
  }
}
