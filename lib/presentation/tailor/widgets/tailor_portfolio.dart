import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_staggered_grid_view/flutter_staggered_grid_view.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';

/// Instagram-inspired portfolio grid
class TailorPortfolio extends StatelessWidget {
  final TailorModel tailor;

  const TailorPortfolio({
    super.key,
    required this.tailor,
  });

  @override
  Widget build(BuildContext context) {
    // Mock portfolio images
    final portfolioImages = [
      'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'https://images.unsplash.com/photo-1617137984095-74e4e5e3613f?w=400',
      'https://images.unsplash.com/photo-1581803118522-7b72a50f7e9f?w=400',
      'https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=400',
      'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400',
      'https://images.unsplash.com/photo-1617137984095-74e4e5e3613f?w=400',
      'https://images.unsplash.com/photo-1581803118522-7b72a50f7e9f?w=400',
    ];

    if (portfolioImages.isEmpty) {
      return _buildEmptyPortfolio(context);
    }

    return Padding(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Portfolio Header
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                'Portfolio',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              Text(
                '${portfolioImages.length} items',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.secondaryTextColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConfig.defaultPadding),

          // Portfolio Grid
          Expanded(
            child: MasonryGridView.count(
              crossAxisCount: 2,
              mainAxisSpacing: 8,
              crossAxisSpacing: 8,
              itemCount: portfolioImages.length,
              itemBuilder: (context, index) {
                return _buildPortfolioItem(
                  context,
                  portfolioImages[index],
                  index,
                );
              },
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildPortfolioItem(BuildContext context, String imageUrl, int index) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Portfolio item ${index + 1}',
      onTap: () => _showImageDetail(context, imageUrl, index),
      child: ClipRRect(
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        child: Stack(
          children: [
            // Image
            AccessibilityHelper.accessibleImage(
              image: CachedNetworkImageProvider(imageUrl),
              semanticLabel: 'Portfolio work ${index + 1}',
              width: double.infinity,
              fit: BoxFit.cover,
            ),

            // Overlay on hover/tap
            Positioned.fill(
              child: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    begin: Alignment.topCenter,
                    end: Alignment.bottomCenter,
                    colors: [
                      Colors.transparent,
                      Colors.black.withValues(alpha: 0.3),
                    ],
                  ),
                ),
              ),
            ),

            // Like button
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                padding: const EdgeInsets.all(6),
                decoration: BoxDecoration(
                  color: Colors.black.withValues(alpha: 0.5),
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.favorite_border,
                  color: Colors.white,
                  size: 16,
                ),
              ),
            ),
          ],
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .scale(begin: const Offset(0.8, 0.8), duration: 600.ms);
  }

  Widget _buildEmptyPortfolio(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.photo_library_outlined,
            size: 64,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'No Portfolio Items',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'This tailor hasn\'t uploaded any portfolio items yet.',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showImageDetail(BuildContext context, String imageUrl, int index) {
    Navigator.of(context).push(
      PageRouteBuilder(
        opaque: false,
        barrierColor: Colors.black87,
        pageBuilder: (context, animation, secondaryAnimation) {
          return _ImageDetailView(
            imageUrl: imageUrl,
            heroTag: 'portfolio_$index',
          );
        },
        transitionsBuilder: (context, animation, secondaryAnimation, child) {
          return FadeTransition(
            opacity: animation,
            child: ScaleTransition(
              scale: Tween<double>(begin: 0.8, end: 1.0).animate(
                CurvedAnimation(parent: animation, curve: Curves.easeOut),
              ),
              child: child,
            ),
          );
        },
      ),
    );
  }
}

class _ImageDetailView extends StatelessWidget {
  final String imageUrl;
  final String heroTag;

  const _ImageDetailView({
    required this.imageUrl,
    required this.heroTag,
  });

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: Colors.transparent,
      body: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Center(
          child: Hero(
            tag: heroTag,
            child: Container(
              margin: const EdgeInsets.all(AppConfig.largePadding),
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withValues(alpha: 0.3),
                    blurRadius: 20,
                    offset: const Offset(0, 10),
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                child: AccessibilityHelper.accessibleImage(
                  image: CachedNetworkImageProvider(imageUrl),
                  semanticLabel: 'Portfolio detail image',
                  fit: BoxFit.contain,
                ),
              ),
            ),
          ),
        ),
      ),
    );
  }
}
