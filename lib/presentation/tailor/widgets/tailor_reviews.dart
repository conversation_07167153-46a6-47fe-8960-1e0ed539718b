import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Tailor reviews tab content
class TailorReviews extends StatelessWidget {
  final String tailorId;

  const TailorReviews({
    super.key,
    required this.tailorId,
  });

  @override
  Widget build(BuildContext context) {
    // Mock reviews data
    final reviews = _getMockReviews();

    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Reviews Summary
          _buildReviewsSummary(context),

          const SizedBox(height: AppConfig.largePadding),

          // Reviews List
          Text(
            'Customer Reviews',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),

          const SizedBox(height: AppConfig.defaultPadding),

          ...reviews.asMap().entries.map((entry) {
            final index = entry.key;
            final review = entry.value;
            return _buildReviewItem(context, review, index);
          }),

          const SizedBox(height: AppConfig.largePadding),

          // Write Review Button
          SizedBox(
            width: double.infinity,
            child: AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  border: Border.all(color: AppTheme.primaryColor),
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: Text(
                  'Write a Review',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: AppTheme.primaryColor,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              onPressed: () {
                // TODO: Navigate to write review screen
              },
              semanticLabel: 'Write a review for this tailor',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewsSummary(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.largePadding),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
        border: Border.all(color: AppTheme.dividerColor),
      ),
      child: Column(
        children: [
          // Overall Rating
          Row(
            children: [
              Text(
                '4.8',
                style: Theme.of(context).textTheme.displayMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: AppTheme.primaryTextColor,
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: List.generate(5, (index) {
                        return Icon(
                          index < 4 ? Icons.star : Icons.star_border,
                          color: Colors.amber,
                          size: 20,
                        );
                      }),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Based on 127 reviews',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: AppConfig.defaultPadding),

          // Rating Breakdown
          ...List.generate(5, (index) {
            final stars = 5 - index;
            final percentage = [0.7, 0.2, 0.05, 0.03, 0.02][index];
            return _buildRatingBar(context, stars, percentage);
          }),
        ],
      ),
    );
  }

  Widget _buildRatingBar(BuildContext context, int stars, double percentage) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Row(
        children: [
          Text(
            '$stars',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
          const SizedBox(width: 8),
          const Icon(
            Icons.star,
            color: Colors.amber,
            size: 12,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: LinearProgressIndicator(
              value: percentage,
              backgroundColor: AppTheme.dividerColor,
              valueColor: const AlwaysStoppedAnimation<Color>(Colors.amber),
            ),
          ),
          const SizedBox(width: 8),
          Text(
            '${(percentage * 100).toInt()}%',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildReviewItem(BuildContext context, ReviewModel review, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleCard(
        semanticLabel: 'Review by ${review.customerName}',
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Reviewer Info
              Row(
                children: [
                  CircleAvatar(
                    radius: 20,
                    backgroundImage: CachedNetworkImageProvider(
                      review.customerAvatar,
                    ),
                  ),
                  const SizedBox(width: AppConfig.defaultPadding),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          review.customerName,
                          style: Theme.of(context).textTheme.titleSmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        Text(
                          review.date,
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),
                  ),
                  // Rating
                  Row(
                    children: List.generate(5, (starIndex) {
                      return Icon(
                        starIndex < review.rating ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                        size: 16,
                      );
                    }),
                  ),
                ],
              ),

              const SizedBox(height: AppConfig.defaultPadding),

              // Review Text
              Text(
                review.comment,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  height: 1.5,
                  color: AppTheme.primaryTextColor,
                ),
              ),

              if (review.images.isNotEmpty) ...[
                const SizedBox(height: AppConfig.defaultPadding),
                // Review Images
                SizedBox(
                  height: 80,
                  child: ListView.builder(
                    scrollDirection: Axis.horizontal,
                    itemCount: review.images.length,
                    itemBuilder: (context, imageIndex) {
                      return Container(
                        margin: const EdgeInsets.only(right: 8),
                        child: ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: AccessibilityHelper.accessibleImage(
                            image: CachedNetworkImageProvider(
                              review.images[imageIndex],
                            ),
                            semanticLabel: 'Review image ${imageIndex + 1}',
                            width: 80,
                            height: 80,
                            fit: BoxFit.cover,
                          ),
                        ),
                      );
                    },
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  List<ReviewModel> _getMockReviews() {
    return [
      ReviewModel(
        customerName: 'Sarah Johnson',
        customerAvatar: 'https://api.dicebear.com/7.x/avataaars/png?seed=sarah',
        rating: 5,
        date: '2 days ago',
        comment: 'Excellent work! The dress fits perfectly and the quality is outstanding. Highly recommend!',
        images: ['https://images.unsplash.com/photo-1594633312681-425c7b97ccd1?w=200'],
      ),
      ReviewModel(
        customerName: 'Michael Chen',
        customerAvatar: 'https://api.dicebear.com/7.x/avataaars/png?seed=michael',
        rating: 5,
        date: '1 week ago',
        comment: 'Amazing tailor! Got my suit altered perfectly for my wedding. Professional and timely service.',
        images: [],
      ),
      ReviewModel(
        customerName: 'Emma Wilson',
        customerAvatar: 'https://api.dicebear.com/7.x/avataaars/png?seed=emma',
        rating: 4,
        date: '2 weeks ago',
        comment: 'Good quality work. The alterations were done well, though it took a bit longer than expected.',
        images: [],
      ),
    ];
  }
}

class ReviewModel {
  final String customerName;
  final String customerAvatar;
  final int rating;
  final String date;
  final String comment;
  final List<String> images;

  ReviewModel({
    required this.customerName,
    required this.customerAvatar,
    required this.rating,
    required this.date,
    required this.comment,
    required this.images,
  });
}
