import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';

/// Contact buttons for tailor profile
class TailorContactButtons extends StatelessWidget {
  final TailorModel tailor;
  final VoidCallback? onMessage;
  final VoidCallback? onCall;
  final VoidCallback? onWhatsApp;
  final VoidCallback? onBookAppointment;

  const TailorContactButtons({
    super.key,
    required this.tailor,
    this.onMessage,
    this.onCall,
    this.onWhatsApp,
    this.onBookAppointment,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.1),
            blurRadius: 10,
            offset: const Offset(0, -2),
          ),
        ],
      ),
      child: SafeArea(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Primary Action - Book Appointment
            SizedBox(
              width: double.infinity,
              child: AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  decoration: BoxDecoration(
                    gradient: AppTheme.instagramGradient,
                    borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  ),
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      const Icon(
                        Icons.calendar_today,
                        color: Colors.white,
                        size: 20,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        'Book Appointment',
                        style: Theme.of(context).textTheme.titleMedium?.copyWith(
                          color: Colors.white,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                onPressed: onBookAppointment,
                semanticLabel: 'Book appointment with ${tailor.name}',
              ),
            )
                .animate()
                .fadeIn(delay: 100.ms, duration: 600.ms)
                .slideY(begin: 0.3, end: 0, duration: 600.ms),

            const SizedBox(height: AppConfig.defaultPadding),

            // Secondary Actions
            Row(
              children: [
                // Message Button
                Expanded(
                  child: AccessibilityHelper.accessibleButton(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundColor,
                        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                        border: Border.all(color: AppTheme.dividerColor),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.chat_bubble_outline,
                            color: AppTheme.primaryColor,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Message',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onPressed: onMessage,
                    semanticLabel: 'Send message to ${tailor.name}',
                  ),
                )
                    .animate()
                    .fadeIn(delay: 200.ms, duration: 600.ms)
                    .slideY(begin: 0.3, end: 0, duration: 600.ms),

                const SizedBox(width: 12),

                // Call Button
                Expanded(
                  child: AccessibilityHelper.accessibleButton(
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      decoration: BoxDecoration(
                        color: AppTheme.backgroundColor,
                        borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                        border: Border.all(color: AppTheme.dividerColor),
                      ),
                      child: Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.phone_outlined,
                            color: AppTheme.primaryColor,
                            size: 18,
                          ),
                          const SizedBox(width: 6),
                          Text(
                            'Call',
                            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              color: AppTheme.primaryColor,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ],
                      ),
                    ),
                    onPressed: onCall,
                    semanticLabel: 'Call ${tailor.name}',
                  ),
                )
                    .animate()
                    .fadeIn(delay: 300.ms, duration: 600.ms)
                    .slideY(begin: 0.3, end: 0, duration: 600.ms),

                const SizedBox(width: 12),

                // WhatsApp Button
                AccessibilityHelper.accessibleButton(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: const Color(0xFF25D366),
                      borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                    ),
                    child: const Icon(
                      Icons.chat,
                      color: Colors.white,
                      size: 18,
                    ),
                  ),
                  onPressed: onWhatsApp,
                  semanticLabel: 'Contact ${tailor.name} on WhatsApp',
                )
                    .animate()
                    .fadeIn(delay: 400.ms, duration: 600.ms)
                    .slideY(begin: 0.3, end: 0, duration: 600.ms),
              ],
            ),

            const SizedBox(height: 8),

            // Availability Status
            _buildAvailabilityStatus(context),
          ],
        ),
      ),
    );
  }

  Widget _buildAvailabilityStatus(BuildContext context) {
    final isAvailable = tailor.isOnline;
    
    return Row(
      mainAxisAlignment: MainAxisAlignment.center,
      children: [
        Container(
          width: 8,
          height: 8,
          decoration: BoxDecoration(
            color: isAvailable ? AppTheme.successColor : AppTheme.errorColor,
            borderRadius: BorderRadius.circular(4),
          ),
        ),
        const SizedBox(width: 6),
        Text(
          isAvailable ? 'Available now' : 'Currently busy',
          style: Theme.of(context).textTheme.bodySmall?.copyWith(
            color: AppTheme.secondaryTextColor,
          ),
        ),
        if (!isAvailable) ...[
          const SizedBox(width: 4),
          Text(
            '• Next available: Tomorrow 9:00 AM',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ],
      ],
    );
  }
}
