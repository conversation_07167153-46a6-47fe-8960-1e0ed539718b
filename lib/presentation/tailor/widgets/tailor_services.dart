import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';

/// Tailor services tab content
class TailorServices extends StatelessWidget {
  final TailorModel tailor;

  const TailorServices({
    super.key,
    required this.tailor,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // About Section
          _buildSection(
            context,
            title: 'About',
            child: Text(
              tailor.bio ?? 'Experienced tailor providing high-quality custom clothing services.',
              style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                height: 1.6,
                color: AppTheme.primaryTextColor,
              ),
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Services Section
          _buildSection(
            context,
            title: 'Services Offered',
            child: Column(
              children: tailor.services.asMap().entries.map((entry) {
                final index = entry.key;
                final service = entry.value;
                return _buildServiceItem(context, service, index);
              }).toList(),
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Pricing Section
          _buildSection(
            context,
            title: 'Starting Prices',
            child: Column(
              children: [
                _buildPriceItem(context, 'Shirt Tailoring', '₮25,000 - ₮45,000'),
                _buildPriceItem(context, 'Suit Tailoring', '₮150,000 - ₮300,000'),
                _buildPriceItem(context, 'Dress Making', '₮35,000 - ₮80,000'),
                _buildPriceItem(context, 'Alterations', '₮5,000 - ₮20,000'),
              ],
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Working Hours Section
          _buildSection(
            context,
            title: 'Working Hours',
            child: Column(
              children: tailor.workingHours.entries.map((entry) {
                return _buildWorkingHourItem(context, entry.key, entry.value);
              }).toList(),
            ),
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Book Appointment Button
          SizedBox(
            width: double.infinity,
            child: AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.symmetric(vertical: 16),
                decoration: BoxDecoration(
                  gradient: AppTheme.instagramGradient,
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                ),
                child: Text(
                  'Book Appointment',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Colors.white,
                    fontWeight: FontWeight.w600,
                  ),
                  textAlign: TextAlign.center,
                ),
              ),
              onPressed: () {
                // TODO: Navigate to booking screen
              },
              semanticLabel: 'Book appointment with ${tailor.name}',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSection(
    BuildContext context, {
    required String title,
    required Widget child,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        child,
      ],
    );
  }

  Widget _buildServiceItem(BuildContext context, String service, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      child: AccessibilityHelper.accessibleCard(
        semanticLabel: 'Service: $service',
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Row(
            children: [
              Container(
                width: 40,
                height: 40,
                decoration: BoxDecoration(
                  gradient: AppTheme.instagramGradient,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: const Icon(
                  Icons.check,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: AppConfig.defaultPadding),
              Expanded(
                child: Text(
                  service,
                  style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                    fontWeight: FontWeight.w500,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildPriceItem(BuildContext context, String service, String price) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            service,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.primaryTextColor,
            ),
          ),
          Text(
            price,
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              fontWeight: FontWeight.w600,
              color: AppTheme.primaryColor,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildWorkingHourItem(BuildContext context, String day, TimeOfDay time) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(
            day,
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.primaryTextColor,
            ),
          ),
          Text(
            '${time.format(context)} - 18:00',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }
}
