import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/tailor_model.dart';
import '../../../data/services/api_service_manager.dart';

// Events
abstract class TailorProfileEvent extends Equatable {
  const TailorProfileEvent();

  @override
  List<Object?> get props => [];
}

class LoadTailorProfile extends TailorProfileEvent {
  final String tailorId;

  const LoadTailorProfile({required this.tailorId});

  @override
  List<Object?> get props => [tailorId];
}

class RefreshTailorProfile extends TailorProfileEvent {
  final String tailorId;

  const RefreshTailorProfile({required this.tailorId});

  @override
  List<Object?> get props => [tailorId];
}

class ToggleFavorite extends TailorProfileEvent {
  final String tailorId;

  const ToggleFavorite({required this.tailorId});

  @override
  List<Object?> get props => [tailorId];
}

class ContactTailor extends TailorProfileEvent {
  final String tailorId;
  final String contactMethod; // 'call', 'message', 'whatsapp'

  const ContactTailor({
    required this.tailorId,
    required this.contactMethod,
  });

  @override
  List<Object?> get props => [tailorId, contactMethod];
}

// States
abstract class TailorProfileState extends Equatable {
  const TailorProfileState();

  @override
  List<Object?> get props => [];
}

class TailorProfileInitial extends TailorProfileState {
  const TailorProfileInitial();
}

class TailorProfileLoading extends TailorProfileState {
  const TailorProfileLoading();
}

class TailorProfileLoaded extends TailorProfileState {
  final TailorModel tailor;
  final bool isFavorite;

  const TailorProfileLoaded({
    required this.tailor,
    this.isFavorite = false,
  });

  @override
  List<Object?> get props => [tailor, isFavorite];

  TailorProfileLoaded copyWith({
    TailorModel? tailor,
    bool? isFavorite,
  }) {
    return TailorProfileLoaded(
      tailor: tailor ?? this.tailor,
      isFavorite: isFavorite ?? this.isFavorite,
    );
  }
}

class TailorProfileError extends TailorProfileState {
  final String message;

  const TailorProfileError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class TailorProfileBloc extends Bloc<TailorProfileEvent, TailorProfileState> {
  final ApiServiceManager _apiService;

  TailorProfileBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const TailorProfileInitial()) {
    on<LoadTailorProfile>(_onLoadTailorProfile);
    on<RefreshTailorProfile>(_onRefreshTailorProfile);
    on<ToggleFavorite>(_onToggleFavorite);
    on<ContactTailor>(_onContactTailor);
  }

  Future<void> _onLoadTailorProfile(
    LoadTailorProfile event,
    Emitter<TailorProfileState> emit,
  ) async {
    emit(const TailorProfileLoading());

    try {
      final response = await _apiService.tailors.getTailorById(event.tailorId);

      if (response.success && response.data != null) {
        emit(TailorProfileLoaded(
          tailor: response.data!,
          isFavorite: false, // TODO: Check if tailor is in favorites
        ));
      } else {
        emit(TailorProfileError(
          message: response.error?.message ?? 'Failed to load tailor profile',
        ));
      }
    } catch (e) {
      emit(TailorProfileError(
        message: 'Failed to load tailor profile: ${e.toString()}',
      ));
    }
  }

  Future<void> _onRefreshTailorProfile(
    RefreshTailorProfile event,
    Emitter<TailorProfileState> emit,
  ) async {
    if (state is TailorProfileLoaded) {
      try {
        final response = await _apiService.getTailorById(event.tailorId);

        if (response.success && response.data != null) {
          final currentState = state as TailorProfileLoaded;
          emit(currentState.copyWith(tailor: response.data!));
        }
      } catch (e) {
        // Keep current state if refresh fails
      }
    }
  }

  Future<void> _onToggleFavorite(
    ToggleFavorite event,
    Emitter<TailorProfileState> emit,
  ) async {
    if (state is TailorProfileLoaded) {
      final currentState = state as TailorProfileLoaded;
      
      try {
        // TODO: Call API to toggle favorite
        final newFavoriteStatus = !currentState.isFavorite;
        
        emit(currentState.copyWith(isFavorite: newFavoriteStatus));
      } catch (e) {
        // Handle error - could show snackbar
      }
    }
  }

  Future<void> _onContactTailor(
    ContactTailor event,
    Emitter<TailorProfileState> emit,
  ) async {
    try {
      // TODO: Handle different contact methods
      switch (event.contactMethod) {
        case 'call':
          // Launch phone dialer
          break;
        case 'message':
          // Navigate to chat screen
          break;
        case 'whatsapp':
          // Launch WhatsApp
          break;
      }
    } catch (e) {
      // Handle error
    }
  }
}
