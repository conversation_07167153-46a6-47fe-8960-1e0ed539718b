import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../home/<USER>';
import '../search/search_screen.dart';
import '../training/training_screen.dart';
import '../supply_chain/supply_chain_screen.dart';
import '../profile/profile_screen.dart';

/// Main screen with bottom navigation
class MainScreen extends StatefulWidget {
  const MainScreen({super.key});

  @override
  State<MainScreen> createState() => _MainScreenState();
}

class _MainScreenState extends State<MainScreen> {
  int _currentIndex = 0;
  late PageController _pageController;

  final List<MainTab> _tabs = [
    MainTab(
      icon: Icons.home_outlined,
      activeIcon: Icons.home,
      label: 'Home',
      screen: const HomeScreen(),
    ),
    MainTab(
      icon: Icons.search_outlined,
      activeIcon: Icons.search,
      label: 'Search',
      screen: const SearchScreen(),
    ),
    MainTab(
      icon: Icons.school_outlined,
      activeIcon: Icons.school,
      label: 'Training',
      screen: const TrainingScreen(),
    ),
    MainTab(
      icon: Icons.inventory_outlined,
      activeIcon: Icons.inventory,
      label: 'Supply',
      screen: const SupplyChainScreen(),
    ),
    MainTab(
      icon: Icons.person_outline,
      activeIcon: Icons.person,
      label: 'Profile',
      screen: const ProfileScreen(),
    ),
  ];

  @override
  void initState() {
    super.initState();
    _pageController = PageController();
  }

  @override
  void dispose() {
    _pageController.dispose();
    super.dispose();
  }

  void _onTabTapped(int index) {
    setState(() {
      _currentIndex = index;
    });
    _pageController.animateToPage(
      index,
      duration: AppConfig.mediumAnimation,
      curve: Curves.easeInOut,
    );
  }

  void _onPageChanged(int index) {
    setState(() {
      _currentIndex = index;
    });
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: PageView(
        controller: _pageController,
        onPageChanged: _onPageChanged,
        children: _tabs.map((tab) => tab.screen).toList(),
      ),
      bottomNavigationBar: Container(
        decoration: BoxDecoration(
          color: Colors.white,
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 10,
              offset: const Offset(0, -2),
            ),
          ],
        ),
        child: SafeArea(
          child: Container(
            height: 70,
            padding: const EdgeInsets.symmetric(
              horizontal: AppConfig.defaultPadding,
              vertical: AppConfig.smallPadding,
            ),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: _tabs.asMap().entries.map((entry) {
                final index = entry.key;
                final tab = entry.value;
                final isActive = index == _currentIndex;
                
                return GestureDetector(
                  onTap: () => _onTabTapped(index),
                  child: AnimatedContainer(
                    duration: AppConfig.shortAnimation,
                    curve: Curves.easeInOut,
                    padding: const EdgeInsets.symmetric(
                      horizontal: 12,
                      vertical: 8,
                    ),
                    decoration: BoxDecoration(
                      gradient: isActive ? AppTheme.instagramGradient : null,
                      borderRadius: BorderRadius.circular(20),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          isActive ? tab.activeIcon : tab.icon,
                          color: isActive ? Colors.white : AppTheme.secondaryTextColor,
                          size: 24,
                        ),
                        if (isActive) ...[
                          const SizedBox(width: 8),
                          Text(
                            tab.label,
                            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                                  color: Colors.white,
                                  fontWeight: FontWeight.w600,
                                ),
                          ),
                        ],
                      ],
                    ),
                  ),
                ).animate(target: isActive ? 1 : 0).scale(
                  begin: const Offset(1.0, 1.0),
                  end: const Offset(1.1, 1.1),
                  duration: 200.ms,
                );
              }).toList(),
            ),
          ),
        ),
      ),
    );
  }
}

class MainTab {
  final IconData icon;
  final IconData activeIcon;
  final String label;
  final Widget screen;

  const MainTab({
    required this.icon,
    required this.activeIcon,
    required this.label,
    required this.screen,
  });
}
