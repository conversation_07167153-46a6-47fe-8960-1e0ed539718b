import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import '../../core/config/app_config.dart';
import '../../core/theme/app_theme.dart';
import '../../core/accessibility/accessibility_helper.dart';
import 'bloc/search_bloc.dart';
import 'widgets/search_app_bar.dart';
import 'widgets/search_filters.dart';
import 'widgets/search_results.dart';
import 'widgets/recent_searches.dart';

/// Instagram-inspired search screen with advanced filters
class SearchScreen extends StatefulWidget {
  const SearchScreen({super.key});

  @override
  State<SearchScreen> createState() => _SearchScreenState();
}

class _SearchScreenState extends State<SearchScreen>
    with TickerProviderStateMixin {
  late TabController _tabController;
  final TextEditingController _searchController = TextEditingController();
  bool _showFilters = false;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => SearchBloc(),
      child: Scaffold(
        body: SafeArea(
          child: Column(
            children: [
              // Search App Bar
              SearchAppBar(
                controller: _searchController,
                onSearch: (query) {
                  context.read<SearchBloc>().add(SearchTailors(query: query));
                },
                onFilterTap: () {
                  setState(() {
                    _showFilters = !_showFilters;
                  });
                },
                showFilters: _showFilters,
              ),

              // Filters Panel (Collapsible)
              AnimatedContainer(
                duration: AppConfig.mediumAnimation,
                height: _showFilters ? 200 : 0,
                child: _showFilters
                    ? SearchFilters(
                        onFiltersChanged: (filters) {
                          context.read<SearchBloc>().add(
                                ApplyFilters(filters: filters),
                              );
                        },
                      )
                    : null,
              ),

              // Tab Bar
              Container(
                decoration: BoxDecoration(
                  color: Colors.white,
                  border: Border(
                    bottom: BorderSide(
                      color: AppTheme.dividerColor,
                      width: 1,
                    ),
                  ),
                ),
                child: TabBar(
                  controller: _tabController,
                  labelColor: AppTheme.primaryColor,
                  unselectedLabelColor: AppTheme.secondaryTextColor,
                  indicatorColor: AppTheme.primaryColor,
                  tabs: const [
                    Tab(text: 'All'),
                    Tab(text: 'Nearby'),
                    Tab(text: 'Featured'),
                  ],
                ),
              ),

              // Content
              Expanded(
                child: BlocBuilder<SearchBloc, SearchState>(
                  builder: (context, state) {
                    if (state is SearchInitial) {
                      return _buildInitialContent();
                    }

                    return TabBarView(
                      controller: _tabController,
                      children: [
                        SearchResults(searchState: state),
                        SearchResults(searchState: state, filterType: 'nearby'),
                        SearchResults(searchState: state, filterType: 'featured'),
                      ],
                    );
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildInitialContent() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Recent Searches
          RecentSearches(
            onSearchTap: (query) {
              _searchController.text = query;
              // context.read<SearchBloc>().add(SearchTailors(query: query));
            },
          ),

          const SizedBox(height: AppConfig.largePadding),

          // Popular Categories
          _buildPopularCategories(),

          const SizedBox(height: AppConfig.largePadding),

          // Quick Filters
          _buildQuickFilters(),
        ],
      ),
    );
  }

  Widget _buildPopularCategories() {
    final categories = [
      'Formal Wear',
      'Casual Wear',
      'Traditional',
      'Wedding',
      'Alterations',
      'Suits',
    ];

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Popular Categories',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: categories.map((category) {
            return AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 16,
                  vertical: 8,
                ),
                decoration: BoxDecoration(
                  color: AppTheme.backgroundColor,
                  borderRadius: BorderRadius.circular(20),
                  border: Border.all(color: AppTheme.dividerColor),
                ),
                child: Text(
                  category,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    color: AppTheme.primaryTextColor,
                  ),
                ),
              ),
              onPressed: () {
                _searchController.text = category;
                // context.read<SearchBloc>().add(SearchTailors(query: category));
              },
              semanticLabel: 'Search for $category',
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _buildQuickFilters() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Filters',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: AppConfig.defaultPadding),
        Row(
          children: [
            Expanded(
              child: _buildQuickFilterCard(
                'Nearby',
                Icons.location_on,
                AppTheme.primaryColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickFilterCard(
                'Top Rated',
                Icons.star,
                Colors.amber,
              ),
            ),
          ],
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: _buildQuickFilterCard(
                'Available Now',
                Icons.access_time,
                AppTheme.successColor,
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: _buildQuickFilterCard(
                'Verified',
                Icons.verified,
                AppTheme.primaryColor,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickFilterCard(String title, IconData icon, Color color) {
    return AccessibilityHelper.accessibleCard(
      semanticLabel: 'Filter by $title',
      onTap: () {
        // Apply quick filter
      },
      child: Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          children: [
            Icon(
              icon,
              color: color,
              size: 32,
            ),
            const SizedBox(height: 8),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}
