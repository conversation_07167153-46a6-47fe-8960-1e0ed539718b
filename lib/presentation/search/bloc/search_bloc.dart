import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/tailor_model.dart';
import '../../../data/services/api_service_manager.dart';

// Events
abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

class SearchTailors extends SearchEvent {
  final String query;

  const SearchTailors({required this.query});

  @override
  List<Object?> get props => [query];
}

class ApplyFilters extends SearchEvent {
  final SearchFilterData filters;

  const ApplyFilters({required this.filters});

  @override
  List<Object?> get props => [filters];
}

class ClearSearch extends SearchEvent {
  const ClearSearch();
}

class LoadMoreResults extends SearchEvent {
  const LoadMoreResults();
}

// States
abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {
  const SearchInitial();
}

class SearchLoading extends SearchState {
  const SearchLoading();
}

class SearchLoaded extends SearchState {
  final List<TailorModel> tailors;
  final String query;
  final SearchFilterData filters;
  final bool hasMoreResults;
  final int currentPage;

  const SearchLoaded({
    required this.tailors,
    required this.query,
    required this.filters,
    this.hasMoreResults = false,
    this.currentPage = 1,
  });

  @override
  List<Object?> get props => [
        tailors,
        query,
        filters,
        hasMoreResults,
        currentPage,
      ];

  SearchLoaded copyWith({
    List<TailorModel>? tailors,
    String? query,
    SearchFilterData? filters,
    bool? hasMoreResults,
    int? currentPage,
  }) {
    return SearchLoaded(
      tailors: tailors ?? this.tailors,
      query: query ?? this.query,
      filters: filters ?? this.filters,
      hasMoreResults: hasMoreResults ?? this.hasMoreResults,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class SearchError extends SearchState {
  final String message;

  const SearchError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final ApiServiceManager _apiService;

  SearchBloc({ApiServiceManager? apiService})
      : _apiService = apiService ?? ApiServiceManager.instance,
        super(const SearchInitial()) {
    on<SearchTailors>(_onSearchTailors);
    on<ApplyFilters>(_onApplyFilters);
    on<ClearSearch>(_onClearSearch);
    on<LoadMoreResults>(_onLoadMoreResults);
  }

  Future<void> _onSearchTailors(
    SearchTailors event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.isEmpty) {
      emit(const SearchInitial());
      return;
    }

    emit(const SearchLoading());

    try {
      final tailors = await _apiService.searchTailors(
        query: event.query,
      );

      emit(SearchLoaded(
        tailors: tailors,
        query: event.query,
        filters: SearchFilterData(),
        hasMoreResults: tailors.length >= 20, // Assume more if we got full page
        currentPage: 1,
      ));
    } catch (e) {
      emit(SearchError(
        message: 'Search failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onApplyFilters(
    ApplyFilters event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchLoaded) {
      final currentState = state as SearchLoaded;
      emit(const SearchLoading());

      try {
        final tailors = await _apiService.searchTailors(
          query: currentState.query,
          categories: event.filters.categories,
          minRating: event.filters.minRating,
          maxDistance: event.filters.maxDistance?.toInt(),
          isVerifiedOnly: event.filters.isVerifiedOnly,
        );

        emit(currentState.copyWith(
          tailors: tailors,
          filters: event.filters,
          hasMoreResults: tailors.length >= 20,
          currentPage: 1,
        ));
      } catch (e) {
        emit(SearchError(
          message: 'Filter application failed: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onClearSearch(
    ClearSearch event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchInitial());
  }

  Future<void> _onLoadMoreResults(
    LoadMoreResults event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchLoaded) {
      final currentState = state as SearchLoaded;
      
      if (!currentState.hasMoreResults) return;

      try {
        // For simplicity, we'll just return the same results for now
        // In a real implementation, you'd pass the page parameter to the API
        final tailors = await _apiService.searchTailors(
          query: currentState.query,
          categories: currentState.filters.categories,
          minRating: currentState.filters.minRating,
          maxDistance: currentState.filters.maxDistance?.toInt(),
          isVerifiedOnly: currentState.filters.isVerifiedOnly,
        );

        // Simulate pagination by limiting results
        final newTailors = tailors.skip(currentState.tailors.length).take(20).toList();

        emit(currentState.copyWith(
          tailors: [...currentState.tailors, ...newTailors],
          hasMoreResults: newTailors.length >= 20,
          currentPage: currentState.currentPage + 1,
        ));
      } catch (e) {
        // Keep current state if load more fails
      }
    }
  }
}

// Search Filters Model
class SearchFilterData extends Equatable {
  final List<String>? categories;
  final List<String>? services;
  final double? minRating;
  final double? maxDistance;
  final List<String>? priceRanges;
  final bool? isVerifiedOnly;
  final bool? isOnlineOnly;

  const SearchFilterData({
    this.categories,
    this.services,
    this.minRating,
    this.maxDistance,
    this.priceRanges,
    this.isVerifiedOnly,
    this.isOnlineOnly,
  });

  @override
  List<Object?> get props => [
        categories,
        services,
        minRating,
        maxDistance,
        priceRanges,
        isVerifiedOnly,
        isOnlineOnly,
      ];

  SearchFilterData copyWith({
    List<String>? categories,
    List<String>? services,
    double? minRating,
    double? maxDistance,
    List<String>? priceRanges,
    bool? isVerifiedOnly,
    bool? isOnlineOnly,
  }) {
    return SearchFilterData(
      categories: categories ?? this.categories,
      services: services ?? this.services,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      priceRanges: priceRanges ?? this.priceRanges,
      isVerifiedOnly: isVerifiedOnly ?? this.isVerifiedOnly,
      isOnlineOnly: isOnlineOnly ?? this.isOnlineOnly,
    );
  }
}
