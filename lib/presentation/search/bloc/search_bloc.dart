import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:equatable/equatable.dart';

import '../../../data/models/tailor_model.dart';
import '../../../data/models/api_response.dart';
import '../../../data/services/mock_api_service.dart';

// Events
abstract class SearchEvent extends Equatable {
  const SearchEvent();

  @override
  List<Object?> get props => [];
}

class SearchTailors extends SearchEvent {
  final String query;

  const SearchTailors({required this.query});

  @override
  List<Object?> get props => [query];
}

class ApplyFilters extends SearchEvent {
  final SearchFilters filters;

  const ApplyFilters({required this.filters});

  @override
  List<Object?> get props => [filters];
}

class ClearSearch extends SearchEvent {
  const ClearSearch();
}

class LoadMoreResults extends SearchEvent {
  const LoadMoreResults();
}

// States
abstract class SearchState extends Equatable {
  const SearchState();

  @override
  List<Object?> get props => [];
}

class SearchInitial extends SearchState {
  const SearchInitial();
}

class SearchLoading extends SearchState {
  const SearchLoading();
}

class SearchLoaded extends SearchState {
  final List<TailorModel> tailors;
  final String query;
  final SearchFilters filters;
  final bool hasMoreResults;
  final int currentPage;

  const SearchLoaded({
    required this.tailors,
    required this.query,
    required this.filters,
    this.hasMoreResults = false,
    this.currentPage = 1,
  });

  @override
  List<Object?> get props => [
        tailors,
        query,
        filters,
        hasMoreResults,
        currentPage,
      ];

  SearchLoaded copyWith({
    List<TailorModel>? tailors,
    String? query,
    SearchFilters? filters,
    bool? hasMoreResults,
    int? currentPage,
  }) {
    return SearchLoaded(
      tailors: tailors ?? this.tailors,
      query: query ?? this.query,
      filters: filters ?? this.filters,
      hasMoreResults: hasMoreResults ?? this.hasMoreResults,
      currentPage: currentPage ?? this.currentPage,
    );
  }
}

class SearchError extends SearchState {
  final String message;

  const SearchError({required this.message});

  @override
  List<Object?> get props => [message];
}

// BLoC
class SearchBloc extends Bloc<SearchEvent, SearchState> {
  final MockApiService _apiService;

  SearchBloc({MockApiService? apiService})
      : _apiService = apiService ?? MockApiService(),
        super(const SearchInitial()) {
    on<SearchTailors>(_onSearchTailors);
    on<ApplyFilters>(_onApplyFilters);
    on<ClearSearch>(_onClearSearch);
    on<LoadMoreResults>(_onLoadMoreResults);
  }

  Future<void> _onSearchTailors(
    SearchTailors event,
    Emitter<SearchState> emit,
  ) async {
    if (event.query.isEmpty) {
      emit(const SearchInitial());
      return;
    }

    emit(const SearchLoading());

    try {
      final filters = TailorSearchFilters(query: event.query);
      final response = await _apiService.searchTailors(
        filters: filters,
        page: 1,
        limit: 20,
      );

      if (response.success && response.data != null) {
        emit(SearchLoaded(
          tailors: response.data!,
          query: event.query,
          filters: SearchFilters(),
          hasMoreResults: response.meta?.hasNextPage ?? false,
          currentPage: 1,
        ));
      } else {
        emit(SearchError(
          message: response.error?.message ?? 'Search failed',
        ));
      }
    } catch (e) {
      emit(SearchError(
        message: 'Search failed: ${e.toString()}',
      ));
    }
  }

  Future<void> _onApplyFilters(
    ApplyFilters event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchLoaded) {
      final currentState = state as SearchLoaded;
      emit(const SearchLoading());

      try {
        final filters = TailorSearchFilters(
          query: currentState.query,
          categories: event.filters.categories,
          services: event.filters.services,
          minRating: event.filters.minRating,
          maxDistance: event.filters.maxDistance,
          priceRanges: event.filters.priceRanges,
          isVerifiedOnly: event.filters.isVerifiedOnly,
          isOnlineOnly: event.filters.isOnlineOnly,
        );

        final response = await _apiService.searchTailors(
          filters: filters,
          page: 1,
          limit: 20,
        );

        if (response.success && response.data != null) {
          emit(currentState.copyWith(
            tailors: response.data!,
            filters: event.filters,
            hasMoreResults: response.meta?.hasNextPage ?? false,
            currentPage: 1,
          ));
        } else {
          emit(SearchError(
            message: response.error?.message ?? 'Filter application failed',
          ));
        }
      } catch (e) {
        emit(SearchError(
          message: 'Filter application failed: ${e.toString()}',
        ));
      }
    }
  }

  Future<void> _onClearSearch(
    ClearSearch event,
    Emitter<SearchState> emit,
  ) async {
    emit(const SearchInitial());
  }

  Future<void> _onLoadMoreResults(
    LoadMoreResults event,
    Emitter<SearchState> emit,
  ) async {
    if (state is SearchLoaded) {
      final currentState = state as SearchLoaded;
      
      if (!currentState.hasMoreResults) return;

      try {
        final filters = TailorSearchFilters(
          query: currentState.query,
          categories: currentState.filters.categories,
          services: currentState.filters.services,
          minRating: currentState.filters.minRating,
          maxDistance: currentState.filters.maxDistance,
          priceRanges: currentState.filters.priceRanges,
          isVerifiedOnly: currentState.filters.isVerifiedOnly,
          isOnlineOnly: currentState.filters.isOnlineOnly,
        );

        final response = await _apiService.searchTailors(
          filters: filters,
          page: currentState.currentPage + 1,
          limit: 20,
        );

        if (response.success && response.data != null) {
          emit(currentState.copyWith(
            tailors: [...currentState.tailors, ...response.data!],
            hasMoreResults: response.meta?.hasNextPage ?? false,
            currentPage: currentState.currentPage + 1,
          ));
        }
      } catch (e) {
        // Keep current state if load more fails
      }
    }
  }
}

// Search Filters Model
class SearchFilters extends Equatable {
  final List<String>? categories;
  final List<String>? services;
  final double? minRating;
  final double? maxDistance;
  final List<String>? priceRanges;
  final bool? isVerifiedOnly;
  final bool? isOnlineOnly;

  const SearchFilters({
    this.categories,
    this.services,
    this.minRating,
    this.maxDistance,
    this.priceRanges,
    this.isVerifiedOnly,
    this.isOnlineOnly,
  });

  @override
  List<Object?> get props => [
        categories,
        services,
        minRating,
        maxDistance,
        priceRanges,
        isVerifiedOnly,
        isOnlineOnly,
      ];

  SearchFilters copyWith({
    List<String>? categories,
    List<String>? services,
    double? minRating,
    double? maxDistance,
    List<String>? priceRanges,
    bool? isVerifiedOnly,
    bool? isOnlineOnly,
  }) {
    return SearchFilters(
      categories: categories ?? this.categories,
      services: services ?? this.services,
      minRating: minRating ?? this.minRating,
      maxDistance: maxDistance ?? this.maxDistance,
      priceRanges: priceRanges ?? this.priceRanges,
      isVerifiedOnly: isVerifiedOnly ?? this.isVerifiedOnly,
      isOnlineOnly: isOnlineOnly ?? this.isOnlineOnly,
    );
  }
}
