import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../bloc/search_bloc.dart';

/// Advanced search filters panel
class SearchFilters extends StatefulWidget {
  final Function(SearchFilterData) onFiltersChanged;

  const SearchFilters({
    super.key,
    required this.onFiltersChanged,
  });

  @override
  State<SearchFilters> createState() => _SearchFiltersState();
}

class _SearchFiltersState extends State<SearchFilters> {
  double _maxDistance = 10.0;
  double _minRating = 0.0;
  bool _verifiedOnly = false;
  bool _availableNow = false;
  List<String> _selectedCategories = [];
  final List<String> _selectedPriceRanges = [];

  final List<String> _categories = [
    'Formal Wear',
    'Casual Wear',
    'Traditional',
    'Wedding',
    'Alterations',
    'Suits',
  ];

  final List<String> _priceRanges = [
    'Under ₮50,000',
    '₮50,000 - ₮100,000',
    '₮100,000 - ₮200,000',
    'Over ₮200,000',
  ];

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: AppTheme.backgroundColor,
        border: Border(
          bottom: BorderSide(
            color: AppTheme.dividerColor,
            width: 1,
          ),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Filter Header
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceBetween,
              children: [
                Text(
                  'Filters',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryTextColor,
                  ),
                ),
                AccessibilityHelper.accessibleButton(
                  child: Text(
                    'Clear All',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  onPressed: _clearAllFilters,
                  semanticLabel: 'Clear all filters',
                ),
              ],
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Quick Filters Row
            Row(
              children: [
                Expanded(
                  child: _buildQuickFilter(
                    'Verified Only',
                    _verifiedOnly,
                    (value) {
                      setState(() {
                        _verifiedOnly = value;
                      });
                      _applyFilters();
                    },
                  ),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: _buildQuickFilter(
                    'Available Now',
                    _availableNow,
                    (value) {
                      setState(() {
                        _availableNow = value;
                      });
                      _applyFilters();
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Distance Slider
            _buildSliderFilter(
              'Distance',
              '${_maxDistance.toInt()} km',
              _maxDistance,
              0.0,
              50.0,
              (value) {
                setState(() {
                  _maxDistance = value;
                });
                _applyFilters();
              },
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Rating Slider
            _buildSliderFilter(
              'Minimum Rating',
              '${_minRating.toStringAsFixed(1)} stars',
              _minRating,
              0.0,
              5.0,
              (value) {
                setState(() {
                  _minRating = value;
                });
                _applyFilters();
              },
            ),

            const SizedBox(height: AppConfig.defaultPadding),

            // Categories
            _buildChipFilter(
              'Categories',
              _categories,
              _selectedCategories,
              (selected) {
                setState(() {
                  _selectedCategories = selected;
                });
                _applyFilters();
              },
            ),
          ],
        ),
      ),
    )
        .animate()
        .fadeIn(duration: 300.ms)
        .slideY(begin: -0.3, end: 0, duration: 300.ms);
  }

  Widget _buildQuickFilter(
    String title,
    bool value,
    Function(bool) onChanged,
  ) {
    return AccessibilityHelper.accessibleButton(
      child: Container(
        padding: const EdgeInsets.symmetric(
          horizontal: 12,
          vertical: 8,
        ),
        decoration: BoxDecoration(
          color: value ? AppTheme.primaryColor : Colors.white,
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: value ? AppTheme.primaryColor : AppTheme.dividerColor,
          ),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              value ? Icons.check_circle : Icons.circle_outlined,
              size: 16,
              color: value ? Colors.white : AppTheme.secondaryTextColor,
            ),
            const SizedBox(width: 6),
            Flexible(
              child: Text(
                title,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: value ? Colors.white : AppTheme.primaryTextColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
      onPressed: () => onChanged(!value),
      semanticLabel: value ? 'Disable $title filter' : 'Enable $title filter',
    );
  }

  Widget _buildSliderFilter(
    String title,
    String value,
    double currentValue,
    double min,
    double max,
    Function(double) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryTextColor,
              ),
            ),
            Text(
              value,
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.primaryColor,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        SliderTheme(
          data: SliderTheme.of(context).copyWith(
            activeTrackColor: AppTheme.primaryColor,
            inactiveTrackColor: AppTheme.dividerColor,
            thumbColor: AppTheme.primaryColor,
            overlayColor: AppTheme.primaryColor.withValues(alpha: 0.2),
            trackHeight: 4,
          ),
          child: Slider(
            value: currentValue,
            min: min,
            max: max,
            divisions: title == 'Minimum Rating' ? 50 : 50,
            onChanged: onChanged,
          ),
        ),
      ],
    );
  }

  Widget _buildChipFilter(
    String title,
    List<String> options,
    List<String> selected,
    Function(List<String>) onChanged,
  ) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            fontWeight: FontWeight.w600,
            color: AppTheme.primaryTextColor,
          ),
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: options.map((option) {
            final isSelected = selected.contains(option);
            return AccessibilityHelper.accessibleButton(
              child: Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: isSelected ? AppTheme.primaryColor : Colors.white,
                  borderRadius: BorderRadius.circular(16),
                  border: Border.all(
                    color: isSelected ? AppTheme.primaryColor : AppTheme.dividerColor,
                  ),
                ),
                child: Text(
                  option,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: isSelected ? Colors.white : AppTheme.primaryTextColor,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
              onPressed: () {
                final newSelected = List<String>.from(selected);
                if (isSelected) {
                  newSelected.remove(option);
                } else {
                  newSelected.add(option);
                }
                onChanged(newSelected);
              },
              semanticLabel: isSelected
                  ? 'Remove $option filter'
                  : 'Add $option filter',
            );
          }).toList(),
        ),
      ],
    );
  }

  void _clearAllFilters() {
    setState(() {
      _maxDistance = 10.0;
      _minRating = 0.0;
      _verifiedOnly = false;
      _availableNow = false;
      _selectedCategories.clear();
      _selectedPriceRanges.clear();
    });
    _applyFilters();
  }

  void _applyFilters() {
    final filters = SearchFilterData(
      categories: _selectedCategories.isNotEmpty ? _selectedCategories : null,
      minRating: _minRating > 0 ? _minRating : null,
      maxDistance: _maxDistance < 50 ? _maxDistance : null,
      priceRanges: _selectedPriceRanges.isNotEmpty ? _selectedPriceRanges : null,
      isVerifiedOnly: _verifiedOnly ? true : null,
      isOnlineOnly: _availableNow ? true : null,
    );

    widget.onFiltersChanged(filters);
  }
}
