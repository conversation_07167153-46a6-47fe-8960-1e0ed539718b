import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Instagram-inspired search app bar
class SearchAppBar extends StatefulWidget {
  final TextEditingController controller;
  final Function(String) onSearch;
  final VoidCallback onFilterTap;
  final bool showFilters;

  const SearchAppBar({
    super.key,
    required this.controller,
    required this.onSearch,
    required this.onFilterTap,
    this.showFilters = false,
  });

  @override
  State<SearchAppBar> createState() => _SearchAppBarState();
}

class _SearchAppBarState extends State<SearchAppBar> {
  final FocusNode _focusNode = FocusNode();
  bool _isSearchFocused = false;

  @override
  void initState() {
    super.initState();
    _focusNode.addListener(() {
      setState(() {
        _isSearchFocused = _focusNode.hasFocus;
      });
    });
  }

  @override
  void dispose() {
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(AppConfig.defaultPadding),
      decoration: BoxDecoration(
        color: Colors.white,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.05),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Search Bar Row
          Row(
            children: [
              // Back Button (when search is focused)
              if (_isSearchFocused)
                AccessibilityHelper.accessibleButton(
                  child: Container(
                    padding: const EdgeInsets.all(8),
                    child: const Icon(
                      Icons.arrow_back_ios,
                      size: 20,
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                  onPressed: () {
                    _focusNode.unfocus();
                    widget.controller.clear();
                    widget.onSearch('');
                  },
                  semanticLabel: 'Cancel search',
                )
                    .animate()
                    .fadeIn(duration: 200.ms)
                    .slideX(begin: -0.5, end: 0, duration: 200.ms),

              // Search Field
              Expanded(
                child: Container(
                  margin: EdgeInsets.only(
                    left: _isSearchFocused ? 8 : 0,
                    right: 8,
                  ),
                  child: AccessibilityHelper.accessibleTextField(
                    controller: widget.controller,
                    label: 'Search',
                    hint: 'Search tailors, services, locations...',
                    semanticLabel: 'Search for tailors and services',
                    keyboardType: TextInputType.text,
                    prefixIcon: const Icon(
                      Icons.search,
                      color: AppTheme.secondaryTextColor,
                    ),
                    suffixIcon: widget.controller.text.isNotEmpty
                        ? AccessibilityHelper.accessibleButton(
                            child: const Icon(
                              Icons.clear,
                              color: AppTheme.secondaryTextColor,
                            ),
                            onPressed: () {
                              widget.controller.clear();
                              widget.onSearch('');
                            },
                            semanticLabel: 'Clear search',
                          )
                        : null,
                  ),
                ),
              ),

              // Filter Button
              if (!_isSearchFocused)
                AccessibilityHelper.accessibleButton(
                  child: Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: widget.showFilters
                          ? AppTheme.primaryColor
                          : AppTheme.backgroundColor,
                      borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                      border: Border.all(
                        color: widget.showFilters
                            ? AppTheme.primaryColor
                            : AppTheme.dividerColor,
                      ),
                    ),
                    child: Icon(
                      Icons.tune,
                      color: widget.showFilters
                          ? Colors.white
                          : AppTheme.primaryTextColor,
                      size: 20,
                    ),
                  ),
                  onPressed: widget.onFilterTap,
                  semanticLabel: widget.showFilters
                      ? 'Hide filters'
                      : 'Show filters',
                )
                    .animate()
                    .fadeIn(duration: 200.ms)
                    .slideX(begin: 0.5, end: 0, duration: 200.ms),
            ],
          ),

          // Search Suggestions (when focused and no text)
          if (_isSearchFocused && widget.controller.text.isEmpty)
            _buildSearchSuggestions(),
        ],
      ),
    );
  }

  Widget _buildSearchSuggestions() {
    final suggestions = [
      'Formal suits',
      'Wedding dresses',
      'Shirt alterations',
      'Traditional wear',
      'Casual tailoring',
    ];

    return Container(
      margin: const EdgeInsets.only(top: AppConfig.defaultPadding),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Popular Searches',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              color: AppTheme.secondaryTextColor,
              fontWeight: FontWeight.w600,
            ),
          ),
          const SizedBox(height: 8),
          ...suggestions.map((suggestion) {
            return AccessibilityHelper.accessibleListTile(
              title: Row(
                children: [
                  const Icon(
                    Icons.trending_up,
                    size: 16,
                    color: AppTheme.secondaryTextColor,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    suggestion,
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppTheme.primaryTextColor,
                    ),
                  ),
                ],
              ),
              semanticLabel: 'Search for $suggestion',
              onTap: () {
                widget.controller.text = suggestion;
                widget.onSearch(suggestion);
                _focusNode.unfocus();
              },
            );
          }),
        ],
      ),
    )
        .animate()
        .fadeIn(delay: 100.ms, duration: 300.ms)
        .slideY(begin: -0.2, end: 0, duration: 300.ms);
  }
}
