import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';
import '../../../data/models/tailor_model.dart';
import '../bloc/search_bloc.dart';

/// Search results list with Instagram-inspired design
class SearchResults extends StatelessWidget {
  final SearchState searchState;
  final String? filterType;

  const SearchResults({
    super.key,
    required this.searchState,
    this.filterType,
  });

  @override
  Widget build(BuildContext context) {
    if (searchState is SearchLoading) {
      return AccessibilityHelper.accessibleLoadingIndicator(
        semanticLabel: 'Loading search results',
      );
    }

    if (searchState is SearchError) {
      final errorState = searchState as SearchError;
      return AccessibilityHelper.accessibleErrorMessage(
        message: errorState.message,
        onRetry: () {
          // TODO: Retry search
        },
      );
    }

    if (searchState is SearchLoaded) {
      final loadedState = searchState as SearchLoaded;
      return _buildResults(context, loadedState);
    }

    return _buildEmptyState(context);
  }

  Widget _buildResults(BuildContext context, SearchLoaded state) {
    final tailors = _filterTailors(state.tailors);

    if (tailors.isEmpty) {
      return _buildNoResults(context, state.query);
    }

    return Column(
      children: [
        // Results Header
        Container(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '${tailors.length} results found',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.secondaryTextColor,
                ),
              ),
              AccessibilityHelper.accessibleButton(
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    const Icon(
                      Icons.sort,
                      size: 16,
                      color: AppTheme.primaryColor,
                    ),
                    const SizedBox(width: 4),
                    Text(
                      'Sort',
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.primaryColor,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
                onPressed: () => _showSortOptions(context),
                semanticLabel: 'Sort search results',
              ),
            ],
          ),
        ),

        // Results List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(
              horizontal: AppConfig.defaultPadding,
            ),
            itemCount: tailors.length + (state.hasMoreResults ? 1 : 0),
            itemBuilder: (context, index) {
              if (index == tailors.length) {
                return _buildLoadMoreButton(context);
              }
              
              return _buildTailorCard(context, tailors[index], index);
            },
          ),
        ),
      ],
    );
  }

  Widget _buildTailorCard(BuildContext context, TailorModel tailor, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleCard(
        semanticLabel: 'Tailor: ${tailor.name}',
        onTap: () {
          Navigator.of(context).pushNamed(
            '/tailor-profile',
            arguments: tailor.id,
          );
        },
        child: Padding(
          padding: const EdgeInsets.all(AppConfig.defaultPadding),
          child: Row(
            children: [
              // Profile Image
              Container(
                width: 80,
                height: 80,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withValues(alpha: 0.1),
                      blurRadius: 8,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: ClipRRect(
                  borderRadius: BorderRadius.circular(AppConfig.borderRadius),
                  child: AccessibilityHelper.accessibleImage(
                    image: CachedNetworkImageProvider(
                      tailor.imageUrl ?? 'https://via.placeholder.com/80x80',
                    ),
                    semanticLabel: '${tailor.name} profile image',
                    fit: BoxFit.cover,
                  ),
                ),
              ),

              const SizedBox(width: AppConfig.defaultPadding),

              // Tailor Info
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Name and Verification
                    Row(
                      children: [
                        Expanded(
                          child: Text(
                            tailor.name,
                            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppTheme.primaryTextColor,
                            ),
                          ),
                        ),
                        if (tailor.isVerified)
                          const Icon(
                            Icons.verified,
                            color: AppTheme.primaryColor,
                            size: 16,
                          ),
                      ],
                    ),

                    const SizedBox(height: 4),

                    // Specialization
                    Text(
                      tailor.categories.take(2).join(' • '),
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: AppTheme.secondaryTextColor,
                      ),
                    ),

                    const SizedBox(height: 8),

                    // Rating and Distance
                    Row(
                      children: [
                        Icon(
                          Icons.star,
                          color: Colors.amber,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          tailor.rating.toStringAsFixed(1),
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            fontWeight: FontWeight.w600,
                            color: AppTheme.primaryTextColor,
                          ),
                        ),
                        const SizedBox(width: 12),
                        Icon(
                          Icons.location_on,
                          color: AppTheme.secondaryTextColor,
                          size: 16,
                        ),
                        const SizedBox(width: 4),
                        Text(
                          tailor.location?.distance != null
                              ? '${tailor.location!.distance!.toStringAsFixed(1)} km'
                              : 'Unknown',
                          style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color: AppTheme.secondaryTextColor,
                          ),
                        ),
                      ],
                    ),

                    const SizedBox(height: 8),

                    // Availability Status
                    Container(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 8,
                        vertical: 4,
                      ),
                      decoration: BoxDecoration(
                        color: tailor.isOnline
                            ? AppTheme.successColor.withValues(alpha: 0.1)
                            : AppTheme.errorColor.withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Text(
                        tailor.isOnline ? 'Online' : 'Offline',
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: tailor.isOnline
                              ? AppTheme.successColor
                              : AppTheme.errorColor,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ],
                ),
              ),

              // Action Button
              AccessibilityHelper.accessibleButton(
                child: Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: AppTheme.primaryColor.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(20),
                  ),
                  child: Icon(
                    Icons.chat_bubble_outline,
                    color: AppTheme.primaryColor,
                    size: 20,
                  ),
                ),
                onPressed: () {
                  // TODO: Navigate to chat
                },
                semanticLabel: 'Message ${tailor.name}',
              ),
            ],
          ),
        ),
      ),
    )
        .animate(delay: (index * 100).ms)
        .fadeIn(duration: 600.ms)
        .slideX(begin: -0.3, end: 0, duration: 600.ms);
  }

  Widget _buildLoadMoreButton(BuildContext context) {
    return Container(
      margin: const EdgeInsets.all(AppConfig.defaultPadding),
      child: AccessibilityHelper.accessibleButton(
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 12),
          decoration: BoxDecoration(
            border: Border.all(color: AppTheme.primaryColor),
            borderRadius: BorderRadius.circular(AppConfig.borderRadius),
          ),
          child: Text(
            'Load More Results',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: AppTheme.primaryColor,
              fontWeight: FontWeight.w600,
            ),
            textAlign: TextAlign.center,
          ),
        ),
        onPressed: () {
          // TODO: Load more results
        },
        semanticLabel: 'Load more search results',
      ),
    );
  }

  Widget _buildNoResults(BuildContext context, String query) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(AppConfig.largePadding),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: AppTheme.secondaryTextColor,
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            Text(
              'No results found',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'Try adjusting your search or filters',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: AppTheme.secondaryTextColor,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEmptyState(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.search,
            size: 64,
            color: AppTheme.secondaryTextColor,
          ),
          const SizedBox(height: AppConfig.defaultPadding),
          Text(
            'Start searching',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: AppTheme.primaryTextColor,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Find the perfect tailor for your needs',
            style: Theme.of(context).textTheme.bodyMedium?.copyWith(
              color: AppTheme.secondaryTextColor,
            ),
          ),
        ],
      ),
    );
  }

  List<TailorModel> _filterTailors(List<TailorModel> tailors) {
    switch (filterType) {
      case 'nearby':
        return tailors.where((t) => t.location?.distance != null && t.location!.distance! <= 5.0).toList();
      case 'featured':
        return tailors.where((t) => t.isVerified).toList();
      default:
        return tailors;
    }
  }

  void _showSortOptions(BuildContext context) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(AppConfig.defaultPadding),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Sort by',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: AppConfig.defaultPadding),
            // Sort options would go here
          ],
        ),
      ),
    );
  }
}
