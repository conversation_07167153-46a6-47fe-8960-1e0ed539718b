import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';

import '../../../core/config/app_config.dart';
import '../../../core/theme/app_theme.dart';
import '../../../core/accessibility/accessibility_helper.dart';

/// Recent searches widget with Instagram-inspired design
class RecentSearches extends StatelessWidget {
  final Function(String) onSearchTap;

  const RecentSearches({
    super.key,
    required this.onSearchTap,
  });

  @override
  Widget build(BuildContext context) {
    // Mock recent searches - in real app, this would come from local storage
    final recentSearches = [
      'Wedding dresses',
      'Formal suits',
      'Shirt alterations',
      'Traditional wear',
      'Casual tailoring',
    ];

    if (recentSearches.isEmpty) {
      return const SizedBox.shrink();
    }

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Header
        Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            Text(
              'Recent Searches',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryTextColor,
              ),
            ),
            AccessibilityHelper.accessibleButton(
              child: Text(
                'Clear All',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: AppTheme.primaryColor,
                  fontWeight: FontWeight.w600,
                ),
              ),
              onPressed: () {
                // TODO: Clear recent searches
              },
              semanticLabel: 'Clear all recent searches',
            ),
          ],
        ),

        const SizedBox(height: AppConfig.defaultPadding),

        // Recent Searches List
        ...recentSearches.asMap().entries.map((entry) {
          final index = entry.key;
          final search = entry.value;
          return _buildRecentSearchItem(context, search, index);
        }),
      ],
    );
  }

  Widget _buildRecentSearchItem(BuildContext context, String search, int index) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      child: AccessibilityHelper.accessibleListTile(
        title: Row(
          children: [
            Icon(
              Icons.history,
              size: 20,
              color: AppTheme.secondaryTextColor,
            ),
            const SizedBox(width: AppConfig.defaultPadding),
            Expanded(
              child: Text(
                search,
                style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                  color: AppTheme.primaryTextColor,
                ),
              ),
            ),
            AccessibilityHelper.accessibleButton(
              child: Icon(
                Icons.north_west,
                size: 16,
                color: AppTheme.secondaryTextColor,
              ),
              onPressed: () => onSearchTap(search),
              semanticLabel: 'Use search term: $search',
            ),
            const SizedBox(width: 8),
            AccessibilityHelper.accessibleButton(
              child: Icon(
                Icons.close,
                size: 16,
                color: AppTheme.secondaryTextColor,
              ),
              onPressed: () {
                // TODO: Remove this search from recent searches
              },
              semanticLabel: 'Remove $search from recent searches',
            ),
          ],
        ),
        semanticLabel: 'Recent search: $search',
        onTap: () => onSearchTap(search),
      ),
    )
        .animate(delay: (index * 50).ms)
        .fadeIn(duration: 400.ms)
        .slideX(begin: -0.2, end: 0, duration: 400.ms);
  }
}
