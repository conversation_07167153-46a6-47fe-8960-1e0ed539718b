import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'presentation/auth/auth_wrapper.dart';
import 'presentation/auth/bloc/auth_bloc.dart';
import 'presentation/onboarding/onboarding_screen.dart';
import 'presentation/auth/auth_screen.dart';
import 'presentation/main/main_screen.dart';
import 'presentation/training/training_screen.dart';
import 'presentation/supply_chain/supply_chain_screen.dart';
import 'presentation/orders/orders_screen.dart';
import 'presentation/chat/chat_list_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const TailorLinkApp());
}

class TailorLinkApp extends StatelessWidget {
  const TailorLinkApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthBloc()
        ..add(const AuthInitialize()),
      child: MaterialApp(
        title: AppConfig.appName,
        debugShowCheckedModeBanner: false,

        // Theme Configuration
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,

        // Home Screen with Auth Wrapper
        home: const AuthWrapper(),

        // Routes
        routes: {
          '/onboarding': (context) => const OnboardingScreen(),
          '/auth': (context) => const AuthScreen(),
          '/main': (context) => const MainScreen(),
          '/training': (context) => const TrainingScreen(),
          '/supply-chain': (context) => const SupplyChainScreen(),
          '/orders': (context) => const OrdersScreen(),
          '/chat': (context) => const ChatListScreen(),
        },

      // Localization
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('mn', 'MN'),
      ],

        // Builder for global configurations
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaler: const TextScaler.linear(1.0), // Prevent text scaling
            ),
            child: child!,
          );
        },
      ),
    );
  }
}


