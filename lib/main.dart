import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_bloc/flutter_bloc.dart';

import 'core/config/app_config.dart';
import 'core/theme/app_theme.dart';
import 'presentation/auth/auth_wrapper.dart';
import 'presentation/auth/bloc/auth_bloc.dart';
import 'presentation/onboarding/onboarding_screen.dart';
import 'presentation/auth/auth_screen.dart';
import 'presentation/main/main_screen.dart';
import 'presentation/wallet/wallet_screen.dart';
import 'presentation/loan/loan_screen.dart';
import 'presentation/orders/orders_screen.dart';
import 'presentation/chat/chat_list_screen.dart';
import 'presentation/common/placeholder_screen.dart';
import 'presentation/common/api_demo_screen.dart';
import 'presentation/search/search_screen.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
    DeviceOrientation.portraitDown,
  ]);

  // Set system UI overlay style
  SystemChrome.setSystemUIOverlayStyle(
    const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
      statusBarIconBrightness: Brightness.dark,
      systemNavigationBarColor: Colors.white,
      systemNavigationBarIconBrightness: Brightness.dark,
    ),
  );

  runApp(const TailorLinkApp());
}

class TailorLinkApp extends StatelessWidget {
  const TailorLinkApp({super.key});

  @override
  Widget build(BuildContext context) {
    return BlocProvider(
      create: (context) => AuthBloc()
        ..add(const AuthInitialize()),
      child: MaterialApp(
        title: AppConfig.appName,
        debugShowCheckedModeBanner: false,

        // Theme Configuration
        theme: AppTheme.lightTheme,
        darkTheme: AppTheme.darkTheme,
        themeMode: ThemeMode.system,

        // Home Screen with Auth Wrapper
        home: const AuthWrapper(),

        // Routes
        routes: {
          '/onboarding': (context) => const OnboardingScreen(),
          '/auth': (context) => const AuthScreen(),
          '/main': (context) => const MainScreen(),
          '/wallet': (context) => const WalletScreen(),
          '/loans': (context) => const LoanScreen(),
          '/orders': (context) => const OrdersScreen(),
          '/chat': (context) => const ChatListScreen(),

          // Wallet sub-routes (placeholder screens - to be implemented)
          '/wallet/top-up': (context) => const PlaceholderScreen(title: 'Wallet Top Up'),
          '/wallet/withdraw': (context) => const PlaceholderScreen(title: 'Wallet Withdraw'),
          '/wallet/transfer': (context) => const PlaceholderScreen(title: 'Wallet Transfer'),
          '/wallet/pay-bills': (context) => const PlaceholderScreen(title: 'Pay Bills'),
          '/wallet/history': (context) => const PlaceholderScreen(title: 'Transaction History'),
          '/wallet/settings': (context) => const PlaceholderScreen(title: 'Wallet Settings'),
          '/wallet/security': (context) => const PlaceholderScreen(title: 'Wallet Security'),
          '/wallet/contacts': (context) => const PlaceholderScreen(title: 'Transfer Contacts'),
          '/wallet/transaction-details': (context) => const PlaceholderScreen(title: 'Transaction Details'),

          // Loan sub-routes (placeholder screens - to be implemented)
          '/loan/apply': (context) => const PlaceholderScreen(title: 'Loan Application'),
          '/loan/history': (context) => const PlaceholderScreen(title: 'Loan History'),
          '/loan/details': (context) => const PlaceholderScreen(title: 'Loan Details'),
          '/loan/application-details': (context) => const PlaceholderScreen(title: 'Application Details'),

          // General routes (placeholder screens - to be implemented)
          '/help': (context) => const PlaceholderScreen(title: 'Help & Support'),
          '/search': (context) => const SearchScreen(),
          '/map': (context) => const PlaceholderScreen(title: 'Map View'),
          '/api-demo': (context) => const ApiDemoScreen(),
        },

      // Localization
      supportedLocales: const [
        Locale('en', 'US'),
        Locale('mn', 'MN'),
      ],

        // Builder for global configurations
        builder: (context, child) {
          return MediaQuery(
            data: MediaQuery.of(context).copyWith(
              textScaler: const TextScaler.linear(1.0), // Prevent text scaling
            ),
            child: child!,
          );
        },
      ),
    );
  }
}


