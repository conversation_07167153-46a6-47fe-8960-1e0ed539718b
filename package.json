{"name": "tailor-api", "version": "1.0.0", "description": "Comprehensive tailor management system API", "main": "src/server.js", "scripts": {"start": "node src/server.js", "dev": "nodemon src/server.js", "test": "jest", "test:watch": "jest --watch", "lint": "eslint src/", "lint:fix": "eslint src/ --fix"}, "keywords": ["api", "tailor", "e-commerce", "inventory", "orders", "finance"], "author": "Tailor API Team", "license": "MIT", "dependencies": {"express": "^4.18.2", "mongoose": "^7.5.0", "bcryptjs": "^2.4.3", "jsonwebtoken": "^9.0.2", "cors": "^2.8.5", "helmet": "^7.0.0", "express-rate-limit": "^6.10.0", "express-validator": "^7.0.1", "multer": "^1.4.5-lts.1", "dotenv": "^16.3.1", "compression": "^1.7.4", "morgan": "^1.10.0", "uuid": "^9.0.0", "moment": "^2.29.4", "nodemailer": "^6.9.4", "swagger-ui-express": "^5.0.0", "yamljs": "^0.3.0"}, "devDependencies": {"nodemon": "^3.0.1", "jest": "^29.6.2", "supertest": "^6.3.3", "eslint": "^8.47.0", "eslint-config-airbnb-base": "^15.0.0", "eslint-plugin-import": "^2.28.1"}, "engines": {"node": ">=16.0.0"}}