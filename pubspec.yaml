name: tailor_link
description: A comprehensive tailor management mobile application with Instagram-inspired UI design.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.2.1
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.4.1
  shimmer: ^3.0.0
  lottie: ^3.1.2
  flutter_staggered_grid_view: ^0.7.0
  card_swiper: ^3.0.1
  smooth_page_indicator: ^1.2.0+3

  # State Management
  flutter_bloc: ^8.1.6
  equatable: ^2.0.7
  hydrated_bloc: ^9.1.5

  # Navigation
  go_router: ^14.2.7

  # Dependency Injection
  get_it: ^8.0.0

  # Network & API
  http: ^1.2.2
  dio: ^5.7.0
  retrofit: ^4.4.1
  json_annotation: ^4.9.0
  connectivity_plus: ^6.0.5

  # Local Storage
  shared_preferences: ^2.3.2
  flutter_secure_storage: ^9.2.2
  sqflite: ^2.3.3+1
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Authentication & Security
  local_auth: ^2.3.0
  crypto: ^3.0.6
  jwt_decoder: ^2.0.1

  # Media & Camera
  image_picker: ^1.1.2
  camera: ^0.11.0+2
  video_player: ^2.9.1
  photo_view: ^0.15.0
  image_cropper: ^8.0.2

  # Communication
  web_socket_channel: ^3.0.1
  socket_io_client: ^3.1.2

  # Push Notifications
  firebase_core: ^3.6.0
  firebase_messaging: ^15.1.3
  flutter_local_notifications: ^17.2.3

  # Location & Maps
  geolocator: ^13.0.1
  geocoding: ^3.0.0
  google_maps_flutter: ^2.9.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  url_launcher: ^6.3.1
  share_plus: ^10.0.2
  path_provider: ^2.1.4
  permission_handler: ^11.3.1
  device_info_plus: ^10.1.2
  package_info_plus: ^8.0.2

  # Form & Validation
  flutter_form_builder: ^9.4.1
  form_builder_validators: ^11.0.0

  # Animation
  flutter_animate: ^4.5.0
  rive: ^0.13.15

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.13
  json_serializable: ^6.9.0
  retrofit_generator: ^8.2.1
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  integration_test:
    sdk: flutter

  # Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
