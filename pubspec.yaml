name: tailor_link
description: A comprehensive tailor management mobile application with Instagram-inspired UI design.
publish_to: 'none'

version: 1.0.0+1

environment:
  sdk: '>=3.0.0 <4.0.0'
  flutter: ">=3.16.0"

dependencies:
  flutter:
    sdk: flutter

  # UI & Design
  cupertino_icons: ^1.0.8
  google_fonts: ^6.1.0
  flutter_svg: ^2.0.9
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  lottie: ^2.7.0
  flutter_staggered_grid_view: ^0.7.0
  card_swiper: ^3.0.1
  smooth_page_indicator: ^1.1.0

  # State Management
  flutter_bloc: ^8.1.3
  equatable: ^2.0.5
  hydrated_bloc: ^9.1.2

  # Navigation
  go_router: ^12.1.3

  # Dependency Injection
  get_it: ^7.6.4

  # Network & API
  dio: ^5.3.2
  retrofit: ^4.0.3
  json_annotation: ^4.8.1
  connectivity_plus: ^5.0.2

  # Local Storage
  shared_preferences: ^2.2.2
  flutter_secure_storage: ^9.0.0
  sqflite: ^2.3.0
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Authentication & Security
  local_auth: ^2.1.6
  crypto: ^3.0.3
  jwt_decoder: ^2.0.1

  # Media & Camera
  image_picker: ^1.0.4
  camera: ^0.10.5+5
  video_player: ^2.7.2
  photo_view: ^0.14.0
  image_cropper: ^5.0.1

  # Communication
  web_socket_channel: ^2.4.0
  socket_io_client: ^2.0.3+1

  # Push Notifications
  firebase_core: ^2.24.2
  firebase_messaging: ^14.7.9
  firebase_analytics: ^10.7.4
  firebase_crashlytics: ^3.4.8
  firebase_performance: ^0.9.3+8
  firebase_storage: ^11.5.6
  flutter_local_notifications: ^16.3.0

  # Location & Maps
  geolocator: ^10.1.0
  geocoding: ^2.1.1
  google_maps_flutter: ^2.5.0

  # Utilities
  intl: ^0.19.0
  uuid: ^4.1.0
  url_launcher: ^6.2.1
  share_plus: ^7.2.1
  path_provider: ^2.1.1
  permission_handler: ^11.1.0
  device_info_plus: ^9.1.1
  package_info_plus: ^4.2.0

  # Form & Validation
  flutter_form_builder: ^9.1.1
  form_builder_validators: ^11.1.2

  # Animation
  flutter_animate: ^4.2.0+1
  rive: ^0.12.4

dev_dependencies:
  flutter_test:
    sdk: flutter

  # Code Generation
  build_runner: ^2.4.7
  json_serializable: ^6.7.1
  retrofit_generator: ^8.0.4
  hive_generator: ^2.0.1

  # Testing
  bloc_test: ^9.1.5
  mocktail: ^1.0.1
  integration_test:
    sdk: flutter

  # Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^5.1.0

flutter:
  uses-material-design: true

  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/
